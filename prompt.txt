You are an expert in workflow automation and n8n JSON analysis. 
You will be given:
1. A user prompt (what the workflow should do)
2. A ground truth n8n workflow JSON (reference)
3. A generated n8n workflow JSON (candidate to evaluate)

Your task:
- Focus ALL scoring and analysis on the GENERATED workflow
- Use the ground truth workflow ONLY as a reference for comparison
- Ignore isolated or unused nodes in either workflow
- Compare structure, parameters, and functional correctness separately
- Assign scores from 1 to 10 for each aspect using the definitions below
- Provide reasoning for each score in relation to the reference workflow

---

### SCORING SCALE (Applied to GENERATED workflow)
**1–3:** Major flaws — critical steps missing, structure/parameters mostly incorrect compared to the reference.  
**4–6:** Partially correct — some correct logic or parameters, but significant gaps or errors compared to the reference.  
**7–8:** Mostly correct — minor omissions or small parameter mismatches compared to the reference.  
**9–10:** Fully correct — matches the intended workflow in structure, parameters, and functionality with negligible or no issues.

---

[BEGIN DATA]

Prompt:
<<<
Create a JSON object representing an n8n workflow for backing up other workflows, including nodes for scheduling, fetching workflows, looping, executing workflow backups, and sending Telegram and Gmail notifications. Ensure connections between nodes are defined to create a functional workflow.
>>>

Ground truth workflow JSON (reference):
<<<
{"name": "Main workflow loop backup", "nodes": [{"parameters": {}, "name": "On clicking 'execute'", "type": "n8n-nodes-base.manualTrigger", "typeVersion": 1, "position": [1640, 580]}, {"parameters": {"filters": {}, "requestOptions": {}}, "name": "n8n", "type": "n8n-nodes-base.n8n", "typeVersion": 1, "position": [2040, 680]}, {"parameters": {"options": {}}, "name": "Loop Over Items", "type": "n8n-nodes-base.splitInBatches", "typeVersion": 3, "position": [2240, 680]}, {"parameters": {"rule": {"interval": [{"triggerAtHour": 1, "triggerAtMinute": 33}]}}, "name": "Schedule Trigger", "type": "n8n-nodes-base.scheduleTrigger", "typeVersion": 1.2, "position": [1620, 800]}, {"parameters": {"content": "## Main workflow loop", "height": 416.1856906618075, "width": 1272.6408145680155, "color": 7}, "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [1620, 500]}, {"parameters": {"workflowId": "=rkExj7m8P27lT2xs", "options": {}}, "name": "Execute Workflow", "type": "n8n-nodes-base.executeWorkflow", "typeVersion": 1, "position": [2460, 720]}, {"parameters": {"sendTo": "<EMAIL>", "subject": "=Backup Started {{ $execution.id }}", "emailType": "text", "message": "=:information_source:  Starting Workflow Backup [{{ $execution.id }}]", "options": {}}, "name": "Gmail", "type": "n8n-nodes-base.gmail", "typeVersion": 2.1, "position": [1860, 220]}, {"parameters": {"sendTo": "<EMAIL>", "subject": "=Backup Started {{ $execution.id }}", "emailType": "text", "message": "=\u2705 Backup has completed - {{ $('n8n').all().length }} workflows have been processed.", "options": {}}, "name": "Gmail1", "type": "n8n-nodes-base.gmail", "typeVersion": 2.1, "position": [2460, 360]}, {"parameters": {"sendTo": "<EMAIL>", "subject": "=Backup Started {{ $execution.id }}", "emailType": "text", "message": "=:x: Failed to backup {{ $('Loop Over Items').item.json.id }}", "options": {}}, "name": "Gmail2", "type": "n8n-nodes-base.gmail", "typeVersion": 2.1, "position": [2660, 1020]}, {"parameters": {"chatId": "489359810", "text": "=:information_source:  Starting Workflow Backup [{{ $execution.id }}]", "additionalFields": {}}, "name": "Telegram", "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [1860, 680]}, {"parameters": {"chatId": "489359810", "text": "=Backup n8n.psyii.od.ua/workflow Done {{ $execution.id }}", "additionalFields": {}}, "name": "Telegram1", "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [2460, 520]}, {"parameters": {"chatId": "489359810", "text": "=:x: Failed to backup {{ $('Loop Over Items').item.json.id }}", "additionalFields": {}}, "name": "Telegram2", "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [2720, 780]}], "connections": {"On clicking 'execute'": {"main": [[{"node": "Telegram", "type": "main", "index": 0}]]}, "n8n": {"main": [[{"node": "Loop Over Items", "type": "main", "index": 0}]]}, "Loop Over Items": {"main": [[{"node": "Telegram1", "type": "main", "index": 0}], [{"node": "Execute Workflow", "type": "main", "index": 0}]]}, "Schedule Trigger": {"main": [[{"node": "Telegram", "type": "main", "index": 0}]]}, "Execute Workflow": {"main": [[{"node": "Loop Over Items", "type": "main", "index": 0}], [{"node": "Telegram2", "type": "main", "index": 0}]]}, "Gmail": {"main": [[]]}, "Gmail2": {"main": [[]]}, "Telegram": {"main": [[{"node": "n8n", "type": "main", "index": 0}]]}, "Telegram2": {"main": [[{"node": "Loop Over Items", "type": "main", "index": 0}]]}}, "active": false}
>>>

Generated workflow JSON (to be scored):
<<<
{
  "name": "Backup Workflows",
  "nodes": [
    {
      "parameters": {
        "cronTime": "* 2 * * *",
        "timezone": "",
        "start": false,
        "stop": false,
        "inExpressions": false
      },
      "name": "Schedule",
      "type": "n8n-nodes-base.cron",
      "typeVersion": 1,
      "position": [
        200,
        300
      ]
    },
    {
      "parameters": {
        "url": "https://YOUR-N8N-INSTANCE/api/workflows",
        "method": "GET",
        "responseFormat": "json",
        "simpleHttpRequestOptions": {}
      },
      "name": "Get Workflows",
      "type": "n8n-nodes-base.httpRequest",
      "typeVersion": 1,
      "position": [
        400,
        300
      ]
    },
    {
      "parameters": {
        "batchSize": 1
      },
      "name": "Loop Workflows",
      "type": "n8n-nodes-base.splitInBatches",
      "typeVersion": 1,
      "position": [
        600,
        300
      ]
    },
    {
      "parameters": {
        "url": "https://YOUR-N8N-INSTANCE/api/workflows/{{$json.id}}",
        "method": "GET",
        "responseFormat": "json",
        "simpleHttpRequestOptions": {}
      },
      "name": "Get Workflow",
      "type": "n8n-nodes-base.httpRequest",
      "typeVersion": 1,
      "position": [
        800,
        300
      ]
    },
    {
      "parameters": {
        "functionCode": "items[0].json.data = items[0].json;\nitems[0].binary = { data: Buffer.from(JSON.stringify(items[0].json, null, 2)) };\nreturn items[0];"
      },
      "name": "Prepare Backup",
      "type": "n8n-nodes-base.function",
      "typeVersion": 1,
      "position": [
        1000,
        300
      ]
    },
    {
      "parameters": {
        "fileName": "workflow-backup-{{$json.id}}.json",
        "binaryPropertyName": "data",
        "destination": "/tmp"
      },
      "name": "Save Backup",
      "type": "n8n-nodes-base.writeBinaryFile",
      "typeVersion": 1,
      "position": [
        1200,
        300
      ]
    },
    {
      "parameters": {
        "chatId": "{{your_chat_id}}",
        "message": "Workflow backup completed for workflow ID {{$json.id}}."
      },
      "name": "Telegram Notification",
      "type": "n8n-nodes-base.telegram",
      "typeVersion": 1,
      "position": [
        1400,
        200
      ]
    },
    {
      "parameters": {
        "subject": "Workflow Backup Completed",
        "text": "Backup has been created for workflow ID {{$json.id}}.",
        "to": "{{<EMAIL>}}",
        "from": "{{<EMAIL>}}",
        "attachments": [
          {
            "filename": "workflow-backup-{{$json.id}}.json",
            "content": "{{$binary.data.base64}}",
            "encoding": "base64"
          }
        ]
      },
      "name": "Send Email",
      "type": "n8n-nodes-base.gmail",
      "typeVersion": 1,
      "position": [
        1400,
        400
      ]
    }
  ],
  "connections": {
    "Schedule": {
      "main": [
        [
          {
            "node": "Get Workflows",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "Get Workflows": {
      "main": [
        [
          {
            "node": "Loop Workflows",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "Loop Workflows": {
      "main": [
        [
          {
            "node": "Get Workflow",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "Get Workflow": {
      "main": [
        [
          {
            "node": "Prepare Backup",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "Prepare Backup": {
      "main": [
        [
          {
            "node": "Save Backup",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "Save Backup": {
      "main": [
        [
          {
            "node": "Telegram Notification",
            "type": "main",
            "index": 0
          },
          {
            "node": "Send Email",
            "type": "main",
            "index": 0
          }
        ]
      ]
    }
  },
  "active": false
}

>>>

[END DATA]

---

### Step 1 — Parse workflows
- Identify the trigger node(s) in each workflow
- Traverse only connected nodes to final output
- For each workflow, list:
  - Step number
  - Node type
  - Key parameters (max 3)
  - One-sentence purpose

### Step 2 — Structural comparison (Generated vs Ground Truth)
- Compare the sequence of node types
- Compare branching and connection patterns
- Ignore extra isolated nodes in the ground truth
- Assign **structural_similarity_score** (1–10) for the GENERATED workflow based on how closely it follows the reference structure

### Step 3 — Parameter accuracy (Generated vs Ground Truth)
- Compare critical parameters: URLs, credentials, field mappings, expressions
- Mark differences or omissions in the GENERATED workflow
- Assign **parameter_accuracy_score** (1–10) for the GENERATED workflow

### Step 4 — Functional equivalence (Generated vs Prompt)
- Judge if the GENERATED workflow achieves the same functional outcome as required by the prompt
- Ignore harmless extra steps
- Penalize if critical steps are missing
- Assign **functional_equivalence_score** (1–10) for the GENERATED workflow

---

### Step 5 — Output
Respond ONLY in the following JSON format:

{
  "reasoning": {
    "parsing": {
      "ground_truth_steps": [...],
      "generated_steps": [...]
    },
    "structural_analysis": {
      "score": INTEGER(1-10),
      "definition_reference": "State why this score fits the 1–10 definition for structure",
      "notes": "Explain structural differences between generated workflow and ground truth"
    },
    "parameter_analysis": {
      "score": INTEGER(1-10),
      "definition_reference": "State why this score fits the 1–10 definition for parameters",
      "mismatches": ["List parameter mismatches in generated workflow vs ground truth"]
    },
    "functional_analysis": {
      "score": INTEGER(1-10),
      "definition_reference": "State why this score fits the 1–10 definition for functionality",
      "missing_steps": ["List functional steps missing in generated workflow"],
      "extra_steps": ["List extra steps in generated workflow"],
      "comments": "Explain functional accuracy of generated workflow compared to prompt"
    }
  }
}
