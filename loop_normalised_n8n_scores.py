import pandas as pd
base_files = ["result_openai_orr_20B-{}.json"]

for base in base_files:
    for i in range(0, 4):
        file_path = base.format(i)
        df = pd.read_json(file_path)
        df["percent_node_with_valid_type"] = df["num_nodes_with_valid_type"] / df["num_nodes"] * 100
        df["percent_node_with_valid_version"] = df["num_nodes_with_valid_version"] / df["num_nodes"] * 100
        df["percent_node_with_valid_structure"] = df["num_nodes_with_valid_structure"] / df["num_nodes"] * 100
        df["percent_connections_with_valid_structure"] = df["num_connections_with_valid_structure"] / df["num_connections"] * 100
        df["percent_connections_with_valid_target_node"] = df["num_connections_with_valid_target_node"] / df["num_connections"] * 100
        df["percent_node_with_parameters"] = df["num_nodes_with_parameters"] / df["num_nodes"] * 100
        df.to_json(file_path, orient="records")
