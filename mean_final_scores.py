import pandas as pd

base_files = ["result_openai_orr_20B-{}.json"]

for base in base_files:
    dfs = []
    for i in range(0, 4):
        file_path = base.format(i)
        df = pd.read_json(file_path)
        dfs.append(df)

    # Concatenate all dataframes
    combined_df = pd.concat(dfs, ignore_index=True)

    # Exclude rows with empty generated_json
    filtered_df = combined_df[combined_df["generated_json"].str.strip() != ""]

    # Calculate mean of numeric columns per prompt
    mean_df = filtered_df.groupby("prompt", as_index=False).mean(numeric_only=True)

    # Extract difficulty (assumes it's the same per prompt)
    difficulty_df = combined_df.drop_duplicates("prompt")[["prompt", "difficulty"]]

    # Merge mean values with difficulty
    result_df = pd.merge(mean_df, difficulty_df, on="prompt", how="left")

    # Save to JSON
    result_df.to_json(base.replace("{}", "mean"), orient="records", force_ascii=False, indent=2)
