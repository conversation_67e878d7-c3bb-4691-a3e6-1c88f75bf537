{"cells": [{"cell_type": "code", "execution_count": 1, "id": "cf9d20bd", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import seaborn as sns"]}, {"cell_type": "code", "execution_count": 2, "id": "2a29a094", "metadata": {}, "outputs": [], "source": ["df = pd.read_json(\"sample.json_medium_mean.json\")"]}, {"cell_type": "code", "execution_count": 3, "id": "55ed4ffb", "metadata": {}, "outputs": [{"data": {"application/vnd.microsoft.datawrangler.viewer.v0+json": {"columns": [{"name": "index", "rawType": "int64", "type": "integer"}, {"name": "prompt", "rawType": "object", "type": "string"}, {"name": "index", "rawType": "int64", "type": "integer"}, {"name": "llm_score", "rawType": "float64", "type": "float"}, {"name": "top_level_keys_present", "rawType": "int64", "type": "integer"}, {"name": "num_nodes", "rawType": "float64", "type": "float"}, {"name": "num_nodes_with_valid_type", "rawType": "float64", "type": "float"}, {"name": "num_nodes_with_valid_version", "rawType": "float64", "type": "float"}, {"name": "num_nodes_with_valid_structure", "rawType": "float64", "type": "float"}, {"name": "num_nodes_with_parameters", "rawType": "float64", "type": "float"}, {"name": "num_connections", "rawType": "float64", "type": "float"}, {"name": "num_connections_with_valid_structure", "rawType": "float64", "type": "float"}, {"name": "num_connections_with_valid_target_node", "rawType": "float64", "type": "float"}, {"name": "percent_node_with_valid_type", "rawType": "float64", "type": "float"}, {"name": "percent_node_with_valid_version", "rawType": "float64", "type": "float"}, {"name": "percent_node_with_valid_structure", "rawType": "float64", "type": "float"}, {"name": "percent_connections_with_valid_structure", "rawType": "float64", "type": "float"}, {"name": "percent_connections_with_valid_target_node", "rawType": "float64", "type": "float"}, {"name": "percent_node_with_parameters", "rawType": "float64", "type": "float"}, {"name": "difficulty", "rawType": "object", "type": "string"}], "ref": "6e4a1881-71d3-4032-9b43-ca0d2e2822c9", "rows": [["0", "Create an n8n workflow named 'Multiple trigger node rerun' that can be triggered manually or on a minute-by-minute schedule. Both triggers should initiate an HTTP request to `https://internal.users.n8n.cloud/webhook/random-data-api` to fetch random users, then use a code node to process the data by reversing the 'firstname' field.", "8", "9.75", "4", "4.5", "4.25", "4.25", "4.5", "4.5", "3.25", "3.25", "3.25", "93.75", "93.75", "100.0", "100.0", "100.0", "100.0", "medium"], ["1", "Create an n8n workflow that triggers on a web form submission titled \"Newsletter de UDIA\", collecting a user's name, email, and privacy consent. Upon submission, it should append or update the collected name and email to \"Hoja 1\" in the specified Google Sheet, matching existing entries by email.", "24", "9.0", "4", "5.0", "5.0", "5.0", "5.0", "5.0", "4.0", "4.0", "4.0", "100.0", "100.0", "100.0", "100.0", "100.0", "100.0", "medium"], ["2", "Generate a JSON object for an inactive workflow named '+11 AI-Powered RAG Workflow For Stock Earnings Report Analysis', with `settings.executionOrder` set to 'v1', empty `nodes` and `connections`, and a `triggerCount` of 0.", "20", "10.0", "4", "0.0", "0.0", "0.0", "0.0", "0.0", "0.0", "0.0", "0.0", null, null, null, null, null, null, "easy"], ["3", "Generate a JSON object representing a n8n workflow named \"email extractor in airtable,\" including its ID, creation/update timestamps, status, and basic structure (empty nodes/connections). The workflow is currently inactive and uses execution order \"v1\".\n", "17", "8.75", "4", "0.0", "0.0", "0.0", "0.0", "0.0", "0.0", "0.0", "0.0", null, null, null, null, null, null, "easy"], ["4", "Generate an active n8n workflow named \"AppTest n8n Onboarding Scaling Automation Solutions\". It should start with a Webhook node configured at `onboarding/n8n/tests/ScalingAutomationSolutions` that connects to a \"Respond to Webhook\" node. The \"Respond to Webhook\" node must return an HTML document that renders a \"Scaling Automation Solutions\" quiz, which upon submission or task completion, posts data to `https://auto.crm-s.com/webhook/Onboarding/Update` and `https://auto.crm-s.com/webhook/Onboarding/UpdateTaskTable` respectively. The workflow should also be tagged with \"n8n\", \"Tests\", \"Day5\", and \"Onboarding\".", "1", "10.0", "4", "2.0", "0.6666666667000001", "0.6666666667000001", "2.0", "2.0", "1.0", "1.0", "1.0", "33.3333333333", "33.3333333333", "100.0", "100.0", "100.0", "100.0", "easy"]], "shape": {"columns": 19, "rows": 5}}, "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>prompt</th>\n", "      <th>index</th>\n", "      <th>llm_score</th>\n", "      <th>top_level_keys_present</th>\n", "      <th>num_nodes</th>\n", "      <th>num_nodes_with_valid_type</th>\n", "      <th>num_nodes_with_valid_version</th>\n", "      <th>num_nodes_with_valid_structure</th>\n", "      <th>num_nodes_with_parameters</th>\n", "      <th>num_connections</th>\n", "      <th>num_connections_with_valid_structure</th>\n", "      <th>num_connections_with_valid_target_node</th>\n", "      <th>percent_node_with_valid_type</th>\n", "      <th>percent_node_with_valid_version</th>\n", "      <th>percent_node_with_valid_structure</th>\n", "      <th>percent_connections_with_valid_structure</th>\n", "      <th>percent_connections_with_valid_target_node</th>\n", "      <th>percent_node_with_parameters</th>\n", "      <th>difficulty</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>Create an n8n workflow named 'Multiple trigger...</td>\n", "      <td>8</td>\n", "      <td>9.75</td>\n", "      <td>4</td>\n", "      <td>4.5</td>\n", "      <td>4.250000</td>\n", "      <td>4.250000</td>\n", "      <td>4.5</td>\n", "      <td>4.5</td>\n", "      <td>3.25</td>\n", "      <td>3.25</td>\n", "      <td>3.25</td>\n", "      <td>93.750000</td>\n", "      <td>93.750000</td>\n", "      <td>100.0</td>\n", "      <td>100.0</td>\n", "      <td>100.0</td>\n", "      <td>100.0</td>\n", "      <td>medium</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>Create an n8n workflow that triggers on a web ...</td>\n", "      <td>24</td>\n", "      <td>9.00</td>\n", "      <td>4</td>\n", "      <td>5.0</td>\n", "      <td>5.000000</td>\n", "      <td>5.000000</td>\n", "      <td>5.0</td>\n", "      <td>5.0</td>\n", "      <td>4.00</td>\n", "      <td>4.00</td>\n", "      <td>4.00</td>\n", "      <td>100.000000</td>\n", "      <td>100.000000</td>\n", "      <td>100.0</td>\n", "      <td>100.0</td>\n", "      <td>100.0</td>\n", "      <td>100.0</td>\n", "      <td>medium</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>Generate a JSON object for an inactive workflo...</td>\n", "      <td>20</td>\n", "      <td>10.00</td>\n", "      <td>4</td>\n", "      <td>0.0</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>easy</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>Generate a JSON object representing a n8n work...</td>\n", "      <td>17</td>\n", "      <td>8.75</td>\n", "      <td>4</td>\n", "      <td>0.0</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>easy</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>Generate an active n8n workflow named \"AppTest...</td>\n", "      <td>1</td>\n", "      <td>10.00</td>\n", "      <td>4</td>\n", "      <td>2.0</td>\n", "      <td>0.666667</td>\n", "      <td>0.666667</td>\n", "      <td>2.0</td>\n", "      <td>2.0</td>\n", "      <td>1.00</td>\n", "      <td>1.00</td>\n", "      <td>1.00</td>\n", "      <td>33.333333</td>\n", "      <td>33.333333</td>\n", "      <td>100.0</td>\n", "      <td>100.0</td>\n", "      <td>100.0</td>\n", "      <td>100.0</td>\n", "      <td>easy</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                                              prompt  index  llm_score  \\\n", "0  Create an n8n workflow named 'Multiple trigger...      8       9.75   \n", "1  Create an n8n workflow that triggers on a web ...     24       9.00   \n", "2  Generate a JSON object for an inactive workflo...     20      10.00   \n", "3  Generate a JSON object representing a n8n work...     17       8.75   \n", "4  Generate an active n8n workflow named \"AppTest...      1      10.00   \n", "\n", "   top_level_keys_present  num_nodes  num_nodes_with_valid_type  \\\n", "0                       4        4.5                   4.250000   \n", "1                       4        5.0                   5.000000   \n", "2                       4        0.0                   0.000000   \n", "3                       4        0.0                   0.000000   \n", "4                       4        2.0                   0.666667   \n", "\n", "   num_nodes_with_valid_version  num_nodes_with_valid_structure  \\\n", "0                      4.250000                             4.5   \n", "1                      5.000000                             5.0   \n", "2                      0.000000                             0.0   \n", "3                      0.000000                             0.0   \n", "4                      0.666667                             2.0   \n", "\n", "   num_nodes_with_parameters  num_connections  \\\n", "0                        4.5             3.25   \n", "1                        5.0             4.00   \n", "2                        0.0             0.00   \n", "3                        0.0             0.00   \n", "4                        2.0             1.00   \n", "\n", "   num_connections_with_valid_structure  \\\n", "0                                  3.25   \n", "1                                  4.00   \n", "2                                  0.00   \n", "3                                  0.00   \n", "4                                  1.00   \n", "\n", "   num_connections_with_valid_target_node  percent_node_with_valid_type  \\\n", "0                                    3.25                     93.750000   \n", "1                                    4.00                    100.000000   \n", "2                                    0.00                           NaN   \n", "3                                    0.00                           NaN   \n", "4                                    1.00                     33.333333   \n", "\n", "   percent_node_with_valid_version  percent_node_with_valid_structure  \\\n", "0                        93.750000                              100.0   \n", "1                       100.000000                              100.0   \n", "2                              NaN                                NaN   \n", "3                              NaN                                NaN   \n", "4                        33.333333                              100.0   \n", "\n", "   percent_connections_with_valid_structure  \\\n", "0                                     100.0   \n", "1                                     100.0   \n", "2                                       NaN   \n", "3                                       NaN   \n", "4                                     100.0   \n", "\n", "   percent_connections_with_valid_target_node  percent_node_with_parameters  \\\n", "0                                       100.0                         100.0   \n", "1                                       100.0                         100.0   \n", "2                                         NaN                           NaN   \n", "3                                         NaN                           NaN   \n", "4                                       100.0                         100.0   \n", "\n", "  difficulty  \n", "0     medium  \n", "1     medium  \n", "2       easy  \n", "3       easy  \n", "4       easy  "]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["df.head()"]}, {"cell_type": "markdown", "id": "e459bb7b", "metadata": {}, "source": ["## Overall"]}, {"cell_type": "code", "execution_count": 4, "id": "aeaeeef2", "metadata": {}, "outputs": [{"data": {"text/plain": ["<Axes: xlabel='llm_score', ylabel='Count'>"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["sns.histplot(df, x=\"llm_score\", bins=20)"]}, {"cell_type": "code", "execution_count": 5, "id": "8825d978", "metadata": {}, "outputs": [{"data": {"text/plain": ["np.float64(0.8888888888888888)"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["(df[\"llm_score\"]>=6).sum()/len(df)"]}, {"cell_type": "code", "execution_count": 6, "id": "c1ecdd0c", "metadata": {}, "outputs": [{"data": {"text/plain": ["<Axes: xlabel='top_level_keys_present', ylabel='Count'>"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAioAAAGxCAYAAABMeZ2uAAAAOnRFWHRTb2Z0d2FyZQBNYXRwbG90bGliIHZlcnNpb24zLjEwLjAsIGh0dHBzOi8vbWF0cGxvdGxpYi5vcmcvlHJYcgAAAAlwSFlzAAAPYQAAD2EBqD+naQAAJFNJREFUeJzt3Xl4jXf+//HXkaRBJVEqJioEtaVqDT+pUTuDGrqpvaVKidim/VpKLTNtaqaWWksX9KqtLabaxlZqX5rEWhS1VEowsWTRSJvk/v7Rn/OdI0ESybk/4fm4rnNdzr2c886nYzyv+5yT47AsyxIAAICBCtk9AAAAwK0QKgAAwFiECgAAMBahAgAAjEWoAAAAYxEqAADAWIQKAAAwFqECAACM5Wn3AHcjIyND586dk4+PjxwOh93jAACAbLAsS0lJSSpTpowKFbr9NZMCHSrnzp1TYGCg3WMAAIBciI2NVdmyZW97TIEOFR8fH0l//KC+vr42TwMAALIjMTFRgYGBzn/Hb6dAh8qNl3t8fX0JFQAACpjsvG2DN9MCAABjESoAAMBYhAoAADAWoQIAAIxFqAAAAGMRKgAAwFiECgAAMBahAgAAjEWoAAAAYxEqAADAWIQKAAAwFqECAACMRagAAABjESoAAMBYnnYPYLIzZ84oPj7e7jEAALDNww8/rHLlytn2/ITKLZw5c0bVqlVXSsqvdo8CAIBtihQpqh9/PGJbrBAqtxAfH6+UlF/1//qMk29AkN3jAADgdolxp7X74wmKj48nVEzlGxCkEuWq2j0GAAD3Jd5MCwAAjEWoAAAAYxEqAADAWIQKAAAwFqECAACMRagAAABjESoAAMBYhAoAADAWoQIAAIxFqAAAAGMRKgAAwFiECgAAMBahAgAAjEWoAAAAYxEqAADAWIQKAAAwFqECAACMRagAAABjESoAAMBYhAoAADAWoQIAAIxFqAAAAGMRKgAAwFiECgAAMBahAgAAjEWoAAAAYxEqAADAWIQKAAAwFqECAACMRagAAABjESoAAMBYtoZKWlqaxowZowoVKqhIkSKqWLGiJk6cqIyMDDvHAgAAhvC088knTZqk999/XwsXLtRjjz2m6Oho9e7dW35+fhoyZIidowEAAAPYGio7d+5Ux44d1b59e0lSUFCQlixZoujoaDvHAgAAhrD1pZ8///nP2rBhg44dOyZJ2r9/v7Zt26Z27drZORYAADCErVdURowYoYSEBFWrVk0eHh5KT0/XW2+9pa5du2Z5fGpqqlJTU533ExMT3TUqAACwga1XVJYtW6ZPP/1Uixcv1p49e7Rw4UK9++67WrhwYZbHR0REyM/Pz3kLDAx088QAAMCdbA2V119/XSNHjlSXLl30+OOPq2fPnho2bJgiIiKyPH7UqFFKSEhw3mJjY908MQAAcCdbX/r59ddfVaiQayt5eHjc8uPJ3t7e8vb2dsdoAADAALaGSocOHfTWW2+pXLlyeuyxx7R3715NmTJFffr0sXMsAABgCFtDZcaMGRo7dqwGDhyoixcvqkyZMurfv7/efPNNO8cCAACGsDVUfHx8NG3aNE2bNs3OMQAAgKH4rh8AAGAsQgUAABiLUAEAAMYiVAAAgLEIFQAAYCxCBQAAGItQAQAAxiJUAACAsQgVAABgLEIFAAAYi1ABAADGIlQAAICxCBUAAGAsQgUAABiLUAEAAMYiVAAAgLEIFQAAYCxCBQAAGItQAQAAxiJUAACAsQgVAABgLEIFAAAYi1ABAADGIlQAAICxCBUAAGAsQgUAABiLUAEAAMYiVAAAgLEIFQAAYCxCBQAAGItQAQAAxiJUAACAsQgVAABgLEIFAAAYi1ABAADGIlQAAICxCBUAAGAsQgUAABiLUAEAAMYiVAAAgLEIFQAAYCxCBQAAGItQAQAAxiJUAACAsQgVAABgLEIFAAAYi1ABAADGIlQAAICxCBUAAGAsQgUAABiLUAEAAMYiVAAAgLEIFQAAYCxCBQAAGItQAQAAxiJUAACAsQgVAABgLEIFAAAYi1ABAADGIlQAAICxCBUAAGAsQgUAABiLUAEAAMYiVAAAgLEIFQAAYCxCBQAAGItQAQAAxrI9VM6ePasePXqoZMmSKlq0qGrXrq2YmBi7xwIAAAbwtPPJr1y5okaNGqlZs2ZavXq1/P39deLECRUvXtzOsQAAgCFsDZVJkyYpMDBQ8+fPd24LCgqybyAAAGAUW1/6WbVqlUJCQvT888/L399fderU0QcffGDnSAAAwCC2hsrJkyc1Z84cVa5cWWvXrtWrr76qwYMH65NPPsny+NTUVCUmJrrcAADAvcvWl34yMjIUEhKit99+W5JUp04dHTp0SHPmzFGvXr0yHR8REaEJEya4e0wAAGATW6+oBAQEKDg42GVb9erVdebMmSyPHzVqlBISEpy32NhYd4wJAABsYusVlUaNGuno0aMu244dO6by5ctneby3t7e8vb3dMRoAADCArVdUhg0bpl27duntt9/WTz/9pMWLF2vevHkKCwuzcywAAGAIW0Olfv36WrlypZYsWaIaNWro73//u6ZNm6bu3bvbORYAADCErS/9SNJTTz2lp556yu4xAACAgWz/FfoAAAC3QqgAAABjESoAAMBYhAoAADAWoQIAAIxFqAAAAGMRKgAAwFiECgAAMBahAgAAjEWoAAAAYxEqAADAWIQKAAAwFqECAACMRagAAABjESoAAMBYhAoAADAWoQIAAIxFqAAAAGMRKgAAwFiECgAAMBahAgAAjEWoAAAAYxEqAADAWIQKAAAwFqECAACMlatQqVixoi5dupRp+9WrV1WxYsW7HgoAAEDKZaicPn1a6enpmbanpqbq7Nmzdz0UAACAJHnm5OBVq1Y5/7x27Vr5+fk576enp2vDhg0KCgrKs+EAAMD9LUeh0qlTJ0mSw+HQiy++6LLPy8tLQUFBmjx5cp4NBwAA7m85CpWMjAxJUoUKFRQVFaWHH344X4YCAACQchgqN5w6dSqv5wAAAMgkV6EiSRs2bNCGDRt08eJF55WWGz7++OO7HgwAACBXoTJhwgRNnDhRISEhCggIkMPhyOu5AAAAchcq77//vhYsWKCePXvm9TwAAABOufo9Kr/99pueeOKJvJ4FAADARa5CpW/fvlq8eHFezwIAAOAiVy/9XL9+XfPmzdO3336rmjVrysvLy2X/lClT8mQ4AABwf8tVqBw4cEC1a9eWJP3www8u+3hjLQAAyCu5CpXvvvsur+cAAADIJFfvUQEAAHCHXF1Radas2W1f4tm4cWOuBwIAALghV6Fy4/0pN/z+++/at2+ffvjhh0xfVggAAJBbuQqVqVOnZrl9/PjxSk5OvquBAAAAbsjT96j06NGD7/kBAAB5Jk9DZefOnSpcuHBePiQAALiP5eqln2eeecblvmVZiouLU3R0tMaOHZsngwEAAOQqVPz8/FzuFypUSFWrVtXEiRPVunXrPBkMAAAgV6Eyf/78vJ4DAAAgk1yFyg0xMTE6cuSIHA6HgoODVadOnbyaCwAAIHehcvHiRXXp0kWbNm1S8eLFZVmWEhIS1KxZMy1dulSlSpXK6zkBAMB9KFef+gkPD1diYqIOHTqky5cv68qVK/rhhx+UmJiowYMH5/WMAADgPpWrKypr1qzRt99+q+rVqzu3BQcHa9asWbyZFgAA5JlcXVHJyMiQl5dXpu1eXl7KyMi466EAAACkXIZK8+bNNWTIEJ07d8657ezZsxo2bJhatGiRZ8MBAID7W65CZebMmUpKSlJQUJAqVaqkRx99VBUqVFBSUpJmzJiR1zMCAID7VK7eoxIYGKg9e/Zo/fr1+vHHH2VZloKDg9WyZcu8ng8AANzHcnRFZePGjQoODlZiYqIkqVWrVgoPD9fgwYNVv359PfbYY9q6dWu+DAoAAO4/OQqVadOm6ZVXXpGvr2+mfX5+furfv7+mTJmSZ8MBAID7W45CZf/+/frLX/5yy/2tW7dWTEzMXQ8FAAAg5TBULly4kOXHkm/w9PTUf/7zn7seCgAAQMphqDzyyCM6ePDgLfcfOHBAAQEBdz0UAACAlMNQadeund58801dv349076UlBSNGzdOTz31VJ4NBwAA7m85+njymDFjtGLFClWpUkWDBg1S1apV5XA4dOTIEc2aNUvp6el644038mtWAABwn8lRqJQuXVo7duzQgAEDNGrUKFmWJUlyOBxq06aNZs+erdKlS+fLoAAA4P6T41/4Vr58eUVGRurKlSv66aefZFmWKleurIceeig/5gMAAPexXP1mWkl66KGHVL9+/bycBQAAwEWuvusHAADAHQgVAABgLGNCJSIiQg6HQ0OHDrV7FAAAYAgjQiUqKkrz5s1TzZo17R4FAAAYxPZQSU5OVvfu3fXBBx/wySEAAODC9lAJCwtT+/bt1bJlyzsem5qaqsTERJcbAAC4d+X648l5YenSpdqzZ4+ioqKydXxERIQmTJiQz1MBAABT2HZFJTY2VkOGDNGnn36qwoULZ+ucUaNGKSEhwXmLjY3N5ykBAICdbLuiEhMTo4sXL6pevXrObenp6dqyZYtmzpyp1NRUeXh4uJzj7e0tb29vd48KAABsYluotGjRQgcPHnTZ1rt3b1WrVk0jRozIFCkAAOD+Y1uo+Pj4qEaNGi7bHnzwQZUsWTLTdgAAcH+y/VM/AAAAt2Lrp35utmnTJrtHAAAABuGKCgAAMBahAgAAjEWoAAAAYxEqAADAWIQKAAAwFqECAACMRagAAABjESoAAMBYhAoAADAWoQIAAIxFqAAAAGMRKgAAwFiECgAAMBahAgAAjEWoAAAAYxEqAADAWIQKAAAwFqECAACMRagAAABjESoAAMBYhAoAADAWoQIAAIxFqAAAAGMRKgAAwFiECgAAMBahAgAAjEWoAAAAYxEqAADAWIQKAAAwFqECAACMRagAAABjESoAAMBYhAoAADAWoQIAAIxFqAAAAGMRKgAAwFiECgAAMBahAgAAjEWoAAAAYxEqAADAWIQKAAAwFqECAACMRagAAABjESoAAMBYhAoAADAWoQIAAIxFqAAAAGMRKgAAwFiECgAAMBahAgAAjEWoAAAAYxEqAADAWIQKAAAwFqECAACMRagAAABjESoAAMBYhAoAADAWoQIAAIxFqAAAAGMRKgAAwFiECgAAMBahAgAAjEWoAAAAYxEqAADAWIQKAAAwFqECAACMRagAAABj2RoqERERql+/vnx8fOTv769OnTrp6NGjdo4EAAAMYmuobN68WWFhYdq1a5fWr1+vtLQ0tW7dWteuXbNzLAAAYAhPO598zZo1Lvfnz58vf39/xcTE6Mknn7RpKgAAYApbQ+VmCQkJkqQSJUpkuT81NVWpqanO+4mJiW6ZCwAA2MOYN9NalqXhw4frz3/+s2rUqJHlMREREfLz83PeAgMD3TwlAABwJ2NCZdCgQTpw4ICWLFlyy2NGjRqlhIQE5y02NtaNEwIAAHcz4qWf8PBwrVq1Slu2bFHZsmVveZy3t7e8vb3dOBkAALCTraFiWZbCw8O1cuVKbdq0SRUqVLBzHAAAYBhbQyUsLEyLFy/Wl19+KR8fH50/f16S5OfnpyJFitg5GgAAMICt71GZM2eOEhIS1LRpUwUEBDhvy5Yts3MsAABgCNtf+gEAALgVYz71AwAAcDNCBQAAGItQAQAAxiJUAACAsQgVAABgLEIFAAAYi1ABAADGIlQAAICxCBUAAGAsQgUAABiLUAEAAMYiVAAAgLEIFQAAYCxCBQAAGItQAQAAxiJUAACAsQgVAABgLEIFAAAYi1ABAADGIlQAAICxCBUAAGAsQgUAABiLUAEAAMYiVAAAgLEIFQAAYCxCBQAAGItQAQAAxiJUAACAsQgVAABgLEIFAAAYi1ABAADGIlQAAICxCBUAAGAsQgUAABiLUAEAAMYiVAAAgLEIFQAAYCxCBQAAGItQAQAAxiJUAACAsQgVAABgLEIFAAAYi1ABAADGIlQAAICxCBUAAGAsQgUAABiLUAEAAMYiVAAAgLEIFQAAYCxCBQAAGItQAQAAxiJUAACAsQgVAABgLEIFAAAYi1ABAADGIlQAAICxCBUAAGAsQgUAABiLUAEAAMYiVAAAgLEIFQAAYCxCBQAAGItQAQAAxiJUAACAsQgVAABgLEIFAAAYi1ABAADGsj1UZs+erQoVKqhw4cKqV6+etm7davdIAADAELaGyrJlyzR06FC98cYb2rt3rxo3bqy2bdvqzJkzdo4FAAAMYWuoTJkyRS+//LL69u2r6tWra9q0aQoMDNScOXPsHAsAABjCtlD57bffFBMTo9atW7tsb926tXbs2GHTVAAAwCSedj1xfHy80tPTVbp0aZftpUuX1vnz57M8JzU1Vampqc77CQkJkqTExMQ8ny85OVmSdPnno0pLTcnzxwcAwHSJ5/94K0ZycnKe/lt747Esy7rjsbaFyg0Oh8PlvmVZmbbdEBERoQkTJmTaHhgYmC+zSVLMp+/k22MDAFAQNGnSJF8eNykpSX5+frc9xrZQefjhh+Xh4ZHp6snFixczXWW5YdSoURo+fLjzfkZGhi5fvqySJUveMm7uJ4mJiQoMDFRsbKx8fX3tHueexTq7B+vsHqyz+7DW/8eyLCUlJalMmTJ3PNa2UHnggQdUr149rV+/Xk8//bRz+/r169WxY8csz/H29pa3t7fLtuLFi+fnmAWSr6/vff+XwB1YZ/dgnd2DdXYf1voPd7qScoOtL/0MHz5cPXv2VEhIiEJDQzVv3jydOXNGr776qp1jAQAAQ9gaKi+88IIuXbqkiRMnKi4uTjVq1FBkZKTKly9v51gAAMAQtr+ZduDAgRo4cKDdY9wTvL29NW7cuEwvjyFvsc7uwTq7B+vsPqx17jis7Hw2CAAAwAa2f9cPAADArRAqAADAWIQKAAAwFqFSQMyZM0c1a9Z0fv4+NDRUq1evvu05qampeuONN1S+fHl5e3urUqVK+vjjj900ccGUm3VetGiRatWqpaJFiyogIEC9e/fWpUuX3DTxvSEiIkIOh0NDhw697XGbN29WvXr1VLhwYVWsWFHvv/++ewa8R2RnnVesWKFWrVqpVKlSzr8Da9eudd+Q94Ds/u/5hu3bt8vT01O1a9fO17kKKkKlgChbtqzeeecdRUdHKzo6Ws2bN1fHjh116NChW57TuXNnbdiwQR999JGOHj2qJUuWqFq1am6cuuDJ6Tpv27ZNvXr10ssvv6xDhw7p888/V1RUlPr27evmyQuuqKgozZs3TzVr1rztcadOnVK7du3UuHFj7d27V6NHj9bgwYO1fPlyN01asGV3nbds2aJWrVopMjJSMTExatasmTp06KC9e/e6adKCLbvrfENCQoJ69eqlFi1a5PNkBZiFAuuhhx6yPvzwwyz3rV692vLz87MuXbrk5qnuPbdb53/9619WxYoVXbZNnz7dKlu2rDtGK/CSkpKsypUrW+vXr7eaNGliDRky5JbH/s///I9VrVo1l239+/e3GjZsmM9TFnw5WeesBAcHWxMmTMif4e4huVnnF154wRozZow1btw4q1atWvk+Y0HEFZUCKD09XUuXLtW1a9cUGhqa5TGrVq1SSEiI/vnPf+qRRx5RlSpV9NprryklhW+Czq7srPMTTzyhX375RZGRkbIsSxcuXNAXX3yh9u3bu3nagiksLEzt27dXy5Yt73jszp071bp1a5dtbdq0UXR0tH7//ff8GvGekJN1vllGRoaSkpJUokSJfJjs3pLTdZ4/f75OnDihcePG5fNkBZvtv/AN2Xfw4EGFhobq+vXrKlasmFauXKng4OAsjz158qS2bdumwoULa+XKlYqPj9fAgQN1+fJl3qdyBzlZ5yeeeEKLFi3SCy+8oOvXrystLU1//etfNWPGDDdPXfAsXbpUe/bsUVRUVLaOP3/+fKYvLC1durTS0tIUHx+vgICA/BizwMvpOt9s8uTJunbtmjp37pzHk91bcrrOx48f18iRI7V161Z5evJP8e1wRaUAqVq1qvbt26ddu3ZpwIABevHFF3X48OEsj83IyJDD4dCiRYvUoEEDtWvXTlOmTNGCBQu4qnIHOVnnw4cPa/DgwXrzzTcVExOjNWvW6NSpU3xf1R3ExsZqyJAh+vTTT1W4cOFsn3fzt6Rb///3VfLt6VnL7TrfsGTJEo0fP17Lli2Tv79/Pkx4b8jpOqenp6tbt26aMGGCqlSp4oYJCzi7X3tC7rVo0cLq169flvt69eplVapUyWXb4cOHLUnWsWPH3DHePeN269yjRw/rueeec9m2detWS5J17tw5d4xXIK1cudKSZHl4eDhvkiyHw2F5eHhYaWlpmc5p3LixNXjwYJdtK1assDw9Pa3ffvvNXaMXKLlZ5xuWLl1qFSlSxPr666/dOHHBlNN1vnLlSqbjHQ6Hc9uGDRts+knMxPWmAsyyLKWmpma5r1GjRvr888+VnJysYsWKSZKOHTumQoUKqWzZsu4cs8C73Tr/+uuvmS7benh4OM9D1lq0aKGDBw+6bOvdu7eqVaumESNGONfwv4WGhuqrr75y2bZu3TqFhITIy8srX+ctqHKzztIfV1L69OmjJUuW8H6rbMjpOvv6+mY6fvbs2dq4caO++OILVahQId9nLlBsDiVk06hRo6wtW7ZYp06dsg4cOGCNHj3aKlSokLVu3TrLsixr5MiRVs+ePZ3HJyUlWWXLlrWee+4569ChQ9bmzZutypUrW3379rXrRygQcrrO8+fPtzw9Pa3Zs2dbJ06csLZt22aFhIRYDRo0sOtHKLBu/pTEzWt98uRJq2jRotawYcOsw4cPWx999JHl5eVlffHFFzZMW3DdaZ0XL15seXp6WrNmzbLi4uKct6tXr9owbcF1p3W+GZ/6uTWuqBQQFy5cUM+ePRUXFyc/Pz/VrFlTa9asUatWrSRJcXFxOnPmjPP4YsWKaf369QoPD1dISIhKliypzp076x//+IddP0KBkNN1fumll5SUlKSZM2fqb3/7m4oXL67mzZtr0qRJdv0I94yb17pChQqKjIzUsGHDNGvWLJUpU0bTp0/Xs88+a+OUBd/N6zx37lylpaUpLCxMYWFhzu0vvviiFixYYMOE94ab1xnZx7cnAwAAY/GpHwAAYCxCBQAAGItQAQAAxiJUAACAsQgVAABgLEIFAAAYi1ABAADGIlQAAICxCBXgHhQUFKRp06a59TmbNm2qoUOHZutYO+YDUDARKkA+y8k/4EBunT59Wg6HQ/v27bN7FCBPESoAcBd+//13u0cA7mmECpCPXnrpJW3evFnvvfeeHA6HHA6HTp8+rc2bN6tBgwby9vZWQECARo4cqbS0NOd5TZs21aBBgzRo0CAVL15cJUuW1JgxY5Tbr+ZKSEhQv3795O/vL19fXzVv3lz79++XJB09elQOh0M//vijyzlTpkxRUFCQ8zkPHz6sdu3aqVixYipdurR69uyp+Pj4XK6Mq/nz58vPz0/r16+/43N98sknKlmypFJTU10e49lnn1WvXr0kSfv371ezZs3k4+MjX19f1atXT9HR0XecY8GCBSpevLj+/e9/q0qVKipcuLBatWql2NhY5zHjx49X7dq19fHHH6tixYry9vaWZVm3XePszLRjxw49+eSTKlKkiAIDAzV48GBdu3bNuT8oKEhvv/22+vTpIx8fH5UrV07z5s1z7q9QoYIkqU6dOnI4HGratGl2lx8wGqEC5KP33ntPoaGheuWVVxQXF6e4uDh5eXmpXbt2ql+/vvbv3685c+boo48+yvTN1gsXLpSnp6d2796t6dOna+rUqfrwww9zPINlWWrfvr3Onz+vyMhIxcTEqG7dumrRooUuX76sqlWrql69elq0aJHLeYsXL1a3bt3kcDgUFxenJk2aqHbt2oqOjtaaNWt04cIFde7c+a7WR5Leffddvfbaa1q7dq1atWp1x+d6/vnnlZ6erlWrVjkfIz4+Xl9//bV69+4tSerevbvKli2rqKgoxcTEaOTIkfLy8srWPL/++qveeustLVy4UNu3b1diYqK6dOnicsxPP/2kzz77TMuXL3e+1HK7Nb7TTAcPHlSbNm30zDPP6MCBA1q2bJm2bdumQYMGuTzv5MmTFRISor1792rgwIEaMGCAMzC///57SdK3336ruLg4rVixIif/GQBzWQDyVZMmTawhQ4Y4748ePdqqWrWqlZGR4dw2a9Ysq1ixYlZ6errznOrVq7scM2LECKt69erZes7y5ctbU6dOtSzLsjZs2GD5+vpa169fdzmmUqVK1ty5cy3LsqwpU6ZYFStWdO47evSoJck6dOiQZVmWNXbsWKt169Yu58fGxlqSrKNHj2b5c2ZnvpEjR1oBAQHWgQMHnPuy81wDBgyw2rZt69w/bdo0q2LFis718vHxsRYsWJCtWf7b/PnzLUnWrl27nNuOHDliSbJ2795tWZZljRs3zvLy8rIuXrzoPCY7a3y7mXr27Gn169fPZdvWrVutQoUKWSkpKZZl/bFmPXr0cO7PyMiw/P39rTlz5liWZVmnTp2yJFl79+7N8c8NmIwrKoCbHTlyRKGhoXI4HM5tjRo1UnJysn755RfntoYNG7ocExoaquPHjys9PT1HzxcTE6Pk5GSVLFlSxYoVc95OnTqlEydOSJK6dOmin3/+Wbt27ZIkLVq0SLVr11ZwcLDzMb777juX86tVqyZJzsfIqcmTJ2vu3Lnatm2bHn/8cZd57/Rcr7zyitatW6ezZ89K+uOlo5deesm5XsOHD1ffvn3VsmVLvfPOOzma0dPTUyEhIc771apVU/HixXXkyBHntvLly6tUqVIuM99pjW83U0xMjBYsWOBybps2bZSRkaFTp045j6tZs6bzzw6HQ3/605908eLFbP9sQEHkafcAwP3GsiyXALmxTVKm7XkhIyNDAQEB2rRpU6Z9xYsXlyQFBASoWbNmWrx4sRo2bKglS5aof//+Lo/RoUMHTZo0KdNjBAQE5Gquxo0b65tvvtFnn32mkSNH5ui56tSpo1q1aumTTz5RmzZtdPDgQX311VfO48aPH69u3brpm2++0erVqzVu3DgtXbpUTz/9dLZmy+q/w39ve/DBB132ZWeNbzdTRkaG+vfvr8GDB2c6v1y5cs4/3/zylcPhUEZGRrZ+JqCgIlSAfPbAAw+4XAUJDg7W8uXLXYJlx44d8vHx0SOPPOI87sbVjf++X7lyZXl4eOTo+evWravz58/L09NTQUFBtzyue/fuGjFihLp27aoTJ064vC+jbt26Wr58uYKCguTpmTf/t9GgQQOFh4erTZs28vDw0Ouvv56j5+rbt6+mTp2qs2fPqmXLlgoMDHTZX6VKFVWpUkXDhg1T165dNX/+/GyFSlpamqKjo9WgQQNJf7zZ+OrVq86rOlnJ7hrfaqa6devq0KFDevTRR+8436088MADkpTjK26A6XjpB8hnQUFB2r17t06fPq34+HgNHDhQsbGxCg8P148//qgvv/xS48aN0/Dhw1Wo0P/9lYyNjdXw4cN19OhRLVmyRDNmzNCQIUNy/PwtW7ZUaGioOnXqpLVr1+r06dPasWOHxowZ4/Kpk2eeeUaJiYkaMGCAmjVr5hJNYWFhunz5srp27arvv/9eJ0+e1Lp169SnT5+7+ocxNDRUq1ev1sSJEzV16tQcPVf37t119uxZffDBB+rTp49ze0pKigYNGqRNmzbp559/1vbt2xUVFaXq1atnayYvLy+Fh4dr9+7d2rNnj3r37q2GDRs6wyUrd1rjO800YsQI7dy5U2FhYdq3b5+OHz+uVatWKTw8PNtr6e/vryJFijjffJyQkJDtcwGTESpAPnvttdfk4eGh4OBglSpVSr///rsiIyP1/fffq1atWnr11Vf18ssva8yYMS7n9erVSykpKWrQoIHCwsIUHh6ufv365fj5HQ6HIiMj9eSTT6pPnz6qUqWKunTpotOnT6t06dLO43x9fdWhQwft379f3bt3d3mMMmXKaPv27UpPT1ebNm1Uo0YNDRkyRH5+fi5xlRuNGjXSN998o7Fjx2r69OnZfi5fX189++yzKlasmDp16uTc7uHhoUuXLqlXr16qUqWKOnfurLZt22rChAnZmqdo0aIaMWKEunXrptDQUBUpUkRLly697Tl3WuM7zVSzZk1t3rxZx48fV+PGjVWnTh2NHTs2Ry+reXp6avr06Zo7d67KlCmjjh07ZvtcwGQOy8rlL2YAkG+aNm2q2rVr82vm76BVq1aqXr26pk+fniePt2DBAg0dOlRXr17Nk8cDcPd4jwqAAufy5ctat26dNm7cqJkzZ9o9DoB8RKgABczWrVvVtm3bW+5PTk524zSZuWO+unXr6sqVK5o0aZKqVq2a7fPatm2rrVu3Zrlv9OjRKlOmzF3PBiBv8dIPUMCkpKQ4f39IVu7mkyN5weT5zp49q5SUlCz3lShRQiVKlHDzRADuhFABAADG4lM/AADAWIQKAAAwFqECAACMRagAAABjESoAAMBYhAoAADAWoQIAAIxFqAAAAGP9L4gyX+Nx1SOFAAAAAElFTkSuQmCC", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["sns.histplot(df, x=\"top_level_keys_present\")"]}, {"cell_type": "code", "execution_count": 7, "id": "a18db11e", "metadata": {}, "outputs": [{"data": {"text/plain": ["<Axes: xlabel='percent_node_with_valid_type', ylabel='Count'>"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["sns.histplot(df, x=\"percent_node_with_valid_type\")"]}, {"cell_type": "code", "execution_count": 8, "id": "3c7c398e", "metadata": {}, "outputs": [{"data": {"text/plain": ["<Axes: xlabel='percent_node_with_valid_version', ylabel='Count'>"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["sns.histplot(df, x=\"percent_node_with_valid_version\")"]}, {"cell_type": "code", "execution_count": 9, "id": "4c46bdcf", "metadata": {}, "outputs": [{"data": {"text/plain": ["<Axes: xlabel='percent_node_with_valid_structure', ylabel='Count'>"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["sns.histplot(df, x=\"percent_node_with_valid_structure\")"]}, {"cell_type": "code", "execution_count": 10, "id": "44883d67", "metadata": {}, "outputs": [{"data": {"text/plain": ["<Axes: xlabel='percent_node_with_parameters', ylabel='Count'>"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["sns.histplot(df, x=\"percent_node_with_parameters\")"]}, {"cell_type": "code", "execution_count": 11, "id": "048db9e6", "metadata": {}, "outputs": [{"data": {"text/plain": ["<Axes: xlabel='percent_connections_with_valid_structure', ylabel='Count'>"]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["sns.histplot(df, x=\"percent_connections_with_valid_structure\")"]}, {"cell_type": "code", "execution_count": 12, "id": "24225a83", "metadata": {}, "outputs": [{"data": {"text/plain": ["<Axes: xlabel='percent_connections_with_valid_target_node', ylabel='Count'>"]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["sns.histplot(df, x=\"percent_connections_with_valid_target_node\")"]}, {"cell_type": "code", "execution_count": 13, "id": "9bb20feb", "metadata": {}, "outputs": [], "source": ["df_prime = df"]}, {"cell_type": "markdown", "id": "a691c4b9", "metadata": {}, "source": ["## Easy workflows"]}, {"cell_type": "code", "execution_count": 14, "id": "db12a210", "metadata": {}, "outputs": [], "source": ["df = df_prime[df_prime[\"difficulty\"] == \"easy\"]"]}, {"cell_type": "code", "execution_count": 15, "id": "b99f16aa", "metadata": {}, "outputs": [{"data": {"text/plain": ["<Axes: xlabel='llm_score', ylabel='Count'>"]}, "execution_count": 15, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["sns.histplot(df, x=\"llm_score\", bins=20)"]}, {"cell_type": "code", "execution_count": 16, "id": "28199c28", "metadata": {}, "outputs": [{"data": {"text/plain": ["np.float64(1.0)"]}, "execution_count": 16, "metadata": {}, "output_type": "execute_result"}], "source": ["(df[\"llm_score\"]>=6).sum()/len(df)"]}, {"cell_type": "code", "execution_count": 17, "id": "03e96ef0", "metadata": {}, "outputs": [{"data": {"text/plain": ["<Axes: xlabel='top_level_keys_present', ylabel='Count'>"]}, "execution_count": 17, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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*******************************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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["sns.histplot(df, x=\"top_level_keys_present\")"]}, {"cell_type": "code", "execution_count": 18, "id": "0991d8aa", "metadata": {}, "outputs": [{"data": {"text/plain": ["<Axes: xlabel='percent_node_with_valid_type', ylabel='Count'>"]}, "execution_count": 18, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["sns.histplot(df, x=\"percent_node_with_valid_type\")"]}, {"cell_type": "code", "execution_count": 19, "id": "7c475b8e", "metadata": {}, "outputs": [{"data": {"text/plain": ["<Axes: xlabel='percent_node_with_valid_version', ylabel='Count'>"]}, "execution_count": 19, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["sns.histplot(df, x=\"percent_node_with_valid_version\")"]}, {"cell_type": "markdown", "id": "9291756b", "metadata": {}, "source": ["## Medium workflows"]}, {"cell_type": "code", "execution_count": 20, "id": "7c7d96c7", "metadata": {}, "outputs": [], "source": ["df = df_prime[df_prime[\"difficulty\"] == \"medium\"]"]}, {"cell_type": "code", "execution_count": 21, "id": "44472701", "metadata": {}, "outputs": [{"data": {"text/plain": ["<Axes: xlabel='llm_score', ylabel='Count'>"]}, "execution_count": 21, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["sns.histplot(df, x=\"llm_score\", bins=20)"]}, {"cell_type": "code", "execution_count": 22, "id": "09842340", "metadata": {}, "outputs": [{"data": {"text/plain": ["np.float64(1.0)"]}, "execution_count": 22, "metadata": {}, "output_type": "execute_result"}], "source": ["(df[\"llm_score\"]>=6).sum()/len(df)"]}, {"cell_type": "code", "execution_count": 23, "id": "a4688dfe", "metadata": {}, "outputs": [{"data": {"text/plain": ["<Axes: xlabel='top_level_keys_present', ylabel='Count'>"]}, "execution_count": 23, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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*******************************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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["sns.histplot(df, x=\"top_level_keys_present\")"]}, {"cell_type": "code", "execution_count": 24, "id": "8066e818", "metadata": {}, "outputs": [{"data": {"text/plain": ["<Axes: xlabel='percent_node_with_valid_type', ylabel='Count'>"]}, "execution_count": 24, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["sns.histplot(df, x=\"percent_node_with_valid_type\")"]}, {"cell_type": "code", "execution_count": 25, "id": "90a9d9a3", "metadata": {}, "outputs": [{"data": {"text/plain": ["<Axes: xlabel='percent_node_with_valid_version', ylabel='Count'>"]}, "execution_count": 25, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["sns.histplot(df, x=\"percent_node_with_valid_version\")"]}, {"cell_type": "markdown", "id": "4d763954", "metadata": {}, "source": ["## Hard workflows"]}, {"cell_type": "code", "execution_count": 26, "id": "0c4a08d8", "metadata": {}, "outputs": [], "source": ["df = df_prime[df_prime[\"difficulty\"] == \"hard\"]"]}, {"cell_type": "code", "execution_count": 27, "id": "521a045f", "metadata": {}, "outputs": [{"data": {"text/plain": ["<Axes: xlabel='llm_score', ylabel='Count'>"]}, "execution_count": 27, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["sns.histplot(df, x=\"llm_score\", bins=20)"]}, {"cell_type": "code", "execution_count": 28, "id": "19ab5cdf", "metadata": {}, "outputs": [{"data": {"text/plain": ["np.float64(0.6666666666666666)"]}, "execution_count": 28, "metadata": {}, "output_type": "execute_result"}], "source": ["(df[\"llm_score\"]>=6).sum()/len(df)"]}, {"cell_type": "code", "execution_count": 29, "id": "516e335b", "metadata": {}, "outputs": [{"data": {"text/plain": ["<Axes: xlabel='top_level_keys_present', ylabel='Count'>"]}, "execution_count": 29, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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*******************************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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["sns.histplot(df, x=\"top_level_keys_present\")"]}, {"cell_type": "code", "execution_count": 30, "id": "4cd2f1aa", "metadata": {}, "outputs": [{"data": {"text/plain": ["<Axes: xlabel='percent_node_with_valid_type', ylabel='Count'>"]}, "execution_count": 30, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["sns.histplot(df, x=\"percent_node_with_valid_type\")"]}, {"cell_type": "code", "execution_count": 31, "id": "d7940155", "metadata": {}, "outputs": [{"data": {"text/plain": ["<Axes: xlabel='percent_node_with_valid_version', ylabel='Count'>"]}, "execution_count": 31, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["sns.histplot(df, x=\"percent_node_with_valid_version\")"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.9"}}, "nbformat": 4, "nbformat_minor": 5}