[{"prompt": "Create an n8n workflow named 'Multiple trigger node rerun' that can be triggered manually or on a minute-by-minute schedule. Both triggers should initiate an HTTP request to `https://internal.users.n8n.cloud/webhook/random-data-api` to fetch random users, then use a code node to process the data by reversing the 'firstname' field.", "index": 8.0, "llm_score": 9.25, "top_level_keys_present": 4.0, "num_nodes": 4.0, "num_nodes_with_valid_type": 4.0, "num_nodes_with_valid_version": 3.75, "num_nodes_with_valid_structure": 4.0, "num_nodes_with_parameters": 4.0, "num_connections": 3.0, "num_connections_with_valid_structure": 3.0, "num_connections_with_valid_target_node": 3.0, "percent_node_with_valid_type": 100.0, "percent_node_with_valid_version": 93.75, "percent_node_with_valid_structure": 100.0, "percent_connections_with_valid_structure": 100.0, "percent_connections_with_valid_target_node": 100.0, "percent_node_with_parameters": 100.0, "difficulty": "medium"}, {"prompt": "Create an n8n workflow that triggers on a web form submission titled \"Newsletter de UDIA\", collecting a user's name, email, and privacy consent. Upon submission, it should append or update the collected name and email to \"Hoja 1\" in the specified Google Sheet, matching existing entries by email.", "index": 24.0, "llm_score": 9.0, "top_level_keys_present": 4.0, "num_nodes": 6.0, "num_nodes_with_valid_type": 6.0, "num_nodes_with_valid_version": 6.0, "num_nodes_with_valid_structure": 6.0, "num_nodes_with_parameters": 6.0, "num_connections": 5.0, "num_connections_with_valid_structure": 5.0, "num_connections_with_valid_target_node": 5.0, "percent_node_with_valid_type": 100.0, "percent_node_with_valid_version": 100.0, "percent_node_with_valid_structure": 100.0, "percent_connections_with_valid_structure": 100.0, "percent_connections_with_valid_target_node": 100.0, "percent_node_with_parameters": 100.0, "difficulty": "medium"}, {"prompt": "Generate a JSON object for an inactive workflow named '+11 AI-Powered RAG Workflow For Stock Earnings Report Analysis', with `settings.executionOrder` set to 'v1', empty `nodes` and `connections`, and a `triggerCount` of 0.", "index": 20.0, "llm_score": 10.0, "top_level_keys_present": 4.0, "num_nodes": 0.0, "num_nodes_with_valid_type": 0.0, "num_nodes_with_valid_version": 0.0, "num_nodes_with_valid_structure": 0.0, "num_nodes_with_parameters": 0.0, "num_connections": 0.0, "num_connections_with_valid_structure": 0.0, "num_connections_with_valid_target_node": 0.0, "percent_node_with_valid_type": null, "percent_node_with_valid_version": null, "percent_node_with_valid_structure": null, "percent_connections_with_valid_structure": null, "percent_connections_with_valid_target_node": null, "percent_node_with_parameters": null, "difficulty": "easy"}, {"prompt": "Generate a JSON object representing a n8n workflow named \"email extractor in airtable,\" including its ID, creation/update timestamps, status, and basic structure (empty nodes/connections). The workflow is currently inactive and uses execution order \"v1\".\n", "index": 17.0, "llm_score": 7.25, "top_level_keys_present": 4.0, "num_nodes": 0.0, "num_nodes_with_valid_type": 0.0, "num_nodes_with_valid_version": 0.0, "num_nodes_with_valid_structure": 0.0, "num_nodes_with_parameters": 0.0, "num_connections": 0.0, "num_connections_with_valid_structure": 0.0, "num_connections_with_valid_target_node": 0.0, "percent_node_with_valid_type": null, "percent_node_with_valid_version": null, "percent_node_with_valid_structure": null, "percent_connections_with_valid_structure": null, "percent_connections_with_valid_target_node": null, "percent_node_with_parameters": null, "difficulty": "easy"}, {"prompt": "Generate an active n8n workflow named \"AppTest n8n Onboarding Scaling Automation Solutions\". It should start with a Webhook node configured at `onboarding/n8n/tests/ScalingAutomationSolutions` that connects to a \"Respond to Webhook\" node. The \"Respond to Webhook\" node must return an HTML document that renders a \"Scaling Automation Solutions\" quiz, which upon submission or task completion, posts data to `https://auto.crm-s.com/webhook/Onboarding/Update` and `https://auto.crm-s.com/webhook/Onboarding/UpdateTaskTable` respectively. The workflow should also be tagged with \"n8n\", \"Tests\", \"Day5\", and \"Onboarding\".", "index": 1.0, "llm_score": 8.5, "top_level_keys_present": 4.0, "num_nodes": 2.0, "num_nodes_with_valid_type": 1.3333333333, "num_nodes_with_valid_version": 1.3333333333, "num_nodes_with_valid_structure": 2.0, "num_nodes_with_parameters": 2.0, "num_connections": 1.0, "num_connections_with_valid_structure": 1.0, "num_connections_with_valid_target_node": 1.0, "percent_node_with_valid_type": 66.6666666667, "percent_node_with_valid_version": 66.6666666667, "percent_node_with_valid_structure": 100.0, "percent_connections_with_valid_structure": 100.0, "percent_connections_with_valid_target_node": 100.0, "percent_node_with_parameters": 100.0, "difficulty": "easy"}, {"prompt": "Generate an n8n workflow JSON configuration that retrieves tickets from the Linear API for a specific team, handles pagination, applies custom data, and writes the data to a Google Sheet. The workflow should include nodes for scheduling, Linear API interaction, conditional logic, data transformation, and Google Sheets integration.\n", "index": 28.0, "llm_score": 6.5, "top_level_keys_present": 4.0, "num_nodes": 5.5, "num_nodes_with_valid_type": 5.25, "num_nodes_with_valid_version": 5.25, "num_nodes_with_valid_structure": 5.5, "num_nodes_with_parameters": 5.5, "num_connections": 4.75, "num_connections_with_valid_structure": 4.75, "num_connections_with_valid_target_node": 4.75, "percent_node_with_valid_type": 93.75, "percent_node_with_valid_version": 93.75, "percent_node_with_valid_structure": 100.0, "percent_connections_with_valid_structure": 100.0, "percent_connections_with_valid_target_node": 100.0, "percent_node_with_parameters": 100.0, "difficulty": "hard"}, {"prompt": "Generate an n8n workflow JSON for fetching content from a specific Google Docs document (\"https://docs.google.com/document/d/1SWrpxi5jfVL79ftSA1u3PZL_wuzcfYFsA_5g02Kuet0/view\") upon receiving a webhook and responding with the document's text content. Include webhook, Google Docs (using the \"u4aaDpAzm6YKZV9W\" credential), and Respond to Webhook nodes.\n", "index": 5.0, "llm_score": 8.75, "top_level_keys_present": 4.0, "num_nodes": 3.0, "num_nodes_with_valid_type": 2.75, "num_nodes_with_valid_version": 2.5, "num_nodes_with_valid_structure": 3.0, "num_nodes_with_parameters": 3.0, "num_connections": 2.0, "num_connections_with_valid_structure": 2.0, "num_connections_with_valid_target_node": 2.0, "percent_node_with_valid_type": 91.6666666667, "percent_node_with_valid_version": 83.3333333333, "percent_node_with_valid_structure": 100.0, "percent_connections_with_valid_structure": 100.0, "percent_connections_with_valid_target_node": 100.0, "percent_node_with_parameters": 100.0, "difficulty": "medium"}, {"prompt": "Generate an n8n workflow that triggers on a Telegram message. If the message text contains \"lies meine email\", it should fetch the 10 most recent Gmail messages, aggregate their snippets for summarization by the DeepSeek API, and then send the generated summary back to the original Telegram chat.", "index": 44.0, "llm_score": 7.75, "top_level_keys_present": 4.0, "num_nodes": 6.75, "num_nodes_with_valid_type": 4.75, "num_nodes_with_valid_version": 4.75, "num_nodes_with_valid_structure": 6.75, "num_nodes_with_parameters": 6.75, "num_connections": 5.75, "num_connections_with_valid_structure": 5.75, "num_connections_with_valid_target_node": 5.75, "percent_node_with_valid_type": 75.0, "percent_node_with_valid_version": 75.0, "percent_node_with_valid_structure": 100.0, "percent_connections_with_valid_structure": 100.0, "percent_connections_with_valid_target_node": 100.0, "percent_node_with_parameters": 100.0, "difficulty": "hard"}, {"prompt": "Generate an n8n workflow that triggers, then uses an OpenAI `gpt-4o-mini` node to parse an input query for city, state, latitude, longitude, and a 'Length' (Single for current weather, Multiple for a 5-day forecast). An If node should then branch execution: if 'Length' is 'Multiple', fetch a 5-day forecast; otherwise, fetch current weather using the OpenWeatherMap node (both in imperial format, using extracted coordinates), and return the final data.", "index": 46.0, "llm_score": 7.25, "top_level_keys_present": 4.0, "num_nodes": 6.25, "num_nodes_with_valid_type": 4.0, "num_nodes_with_valid_version": 4.0, "num_nodes_with_valid_structure": 6.25, "num_nodes_with_parameters": 6.25, "num_connections": 5.5, "num_connections_with_valid_structure": 5.5, "num_connections_with_valid_target_node": 5.5, "percent_node_with_valid_type": 64.6428571429, "percent_node_with_valid_version": 64.6428571429, "percent_node_with_valid_structure": 100.0, "percent_connections_with_valid_structure": 100.0, "percent_connections_with_valid_target_node": 100.0, "percent_node_with_parameters": 100.0, "difficulty": "hard"}]