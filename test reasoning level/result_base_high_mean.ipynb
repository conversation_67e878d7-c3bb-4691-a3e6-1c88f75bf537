{"cells": [{"cell_type": "code", "execution_count": 1, "id": "cf9d20bd", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import seaborn as sns"]}, {"cell_type": "code", "execution_count": 2, "id": "2a29a094", "metadata": {}, "outputs": [], "source": ["df = pd.read_json(\"sample.json_high_mean.json\")"]}, {"cell_type": "code", "execution_count": 3, "id": "55ed4ffb", "metadata": {}, "outputs": [{"data": {"application/vnd.microsoft.datawrangler.viewer.v0+json": {"columns": [{"name": "index", "rawType": "int64", "type": "integer"}, {"name": "prompt", "rawType": "object", "type": "string"}, {"name": "index", "rawType": "int64", "type": "integer"}, {"name": "llm_score", "rawType": "float64", "type": "float"}, {"name": "top_level_keys_present", "rawType": "int64", "type": "integer"}, {"name": "num_nodes", "rawType": "float64", "type": "float"}, {"name": "num_nodes_with_valid_type", "rawType": "float64", "type": "float"}, {"name": "num_nodes_with_valid_version", "rawType": "float64", "type": "float"}, {"name": "num_nodes_with_valid_structure", "rawType": "float64", "type": "float"}, {"name": "num_nodes_with_parameters", "rawType": "float64", "type": "float"}, {"name": "num_connections", "rawType": "float64", "type": "float"}, {"name": "num_connections_with_valid_structure", "rawType": "float64", "type": "float"}, {"name": "num_connections_with_valid_target_node", "rawType": "float64", "type": "float"}, {"name": "percent_node_with_valid_type", "rawType": "float64", "type": "float"}, {"name": "percent_node_with_valid_version", "rawType": "float64", "type": "float"}, {"name": "percent_node_with_valid_structure", "rawType": "float64", "type": "float"}, {"name": "percent_connections_with_valid_structure", "rawType": "float64", "type": "float"}, {"name": "percent_connections_with_valid_target_node", "rawType": "float64", "type": "float"}, {"name": "percent_node_with_parameters", "rawType": "float64", "type": "float"}, {"name": "difficulty", "rawType": "object", "type": "string"}], "ref": "26b8c902-bac5-4ce9-a339-2e28061f2524", "rows": [["0", "Create an n8n workflow named 'Multiple trigger node rerun' that can be triggered manually or on a minute-by-minute schedule. Both triggers should initiate an HTTP request to `https://internal.users.n8n.cloud/webhook/random-data-api` to fetch random users, then use a code node to process the data by reversing the 'firstname' field.", "8", "9.25", "4", "4.0", "4.0", "3.75", "4.0", "4.0", "3.0", "3.0", "3.0", "100.0", "93.75", "100.0", "100.0", "100.0", "100.0", "medium"], ["1", "Create an n8n workflow that triggers on a web form submission titled \"Newsletter de UDIA\", collecting a user's name, email, and privacy consent. Upon submission, it should append or update the collected name and email to \"Hoja 1\" in the specified Google Sheet, matching existing entries by email.", "24", "9.0", "4", "6.0", "6.0", "6.0", "6.0", "6.0", "5.0", "5.0", "5.0", "100.0", "100.0", "100.0", "100.0", "100.0", "100.0", "medium"], ["2", "Generate a JSON object for an inactive workflow named '+11 AI-Powered RAG Workflow For Stock Earnings Report Analysis', with `settings.executionOrder` set to 'v1', empty `nodes` and `connections`, and a `triggerCount` of 0.", "20", "10.0", "4", "0.0", "0.0", "0.0", "0.0", "0.0", "0.0", "0.0", "0.0", null, null, null, null, null, null, "easy"], ["3", "Generate a JSON object representing a n8n workflow named \"email extractor in airtable,\" including its ID, creation/update timestamps, status, and basic structure (empty nodes/connections). The workflow is currently inactive and uses execution order \"v1\".\n", "17", "7.25", "4", "0.0", "0.0", "0.0", "0.0", "0.0", "0.0", "0.0", "0.0", null, null, null, null, null, null, "easy"], ["4", "Generate an active n8n workflow named \"AppTest n8n Onboarding Scaling Automation Solutions\". It should start with a Webhook node configured at `onboarding/n8n/tests/ScalingAutomationSolutions` that connects to a \"Respond to Webhook\" node. The \"Respond to Webhook\" node must return an HTML document that renders a \"Scaling Automation Solutions\" quiz, which upon submission or task completion, posts data to `https://auto.crm-s.com/webhook/Onboarding/Update` and `https://auto.crm-s.com/webhook/Onboarding/UpdateTaskTable` respectively. The workflow should also be tagged with \"n8n\", \"Tests\", \"Day5\", and \"Onboarding\".", "1", "8.5", "4", "2.0", "1.3333333333000001", "1.3333333333000001", "2.0", "2.0", "1.0", "1.0", "1.0", "66.6666666667", "66.6666666667", "100.0", "100.0", "100.0", "100.0", "easy"]], "shape": {"columns": 19, "rows": 5}}, "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>prompt</th>\n", "      <th>index</th>\n", "      <th>llm_score</th>\n", "      <th>top_level_keys_present</th>\n", "      <th>num_nodes</th>\n", "      <th>num_nodes_with_valid_type</th>\n", "      <th>num_nodes_with_valid_version</th>\n", "      <th>num_nodes_with_valid_structure</th>\n", "      <th>num_nodes_with_parameters</th>\n", "      <th>num_connections</th>\n", "      <th>num_connections_with_valid_structure</th>\n", "      <th>num_connections_with_valid_target_node</th>\n", "      <th>percent_node_with_valid_type</th>\n", "      <th>percent_node_with_valid_version</th>\n", "      <th>percent_node_with_valid_structure</th>\n", "      <th>percent_connections_with_valid_structure</th>\n", "      <th>percent_connections_with_valid_target_node</th>\n", "      <th>percent_node_with_parameters</th>\n", "      <th>difficulty</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>Create an n8n workflow named 'Multiple trigger...</td>\n", "      <td>8</td>\n", "      <td>9.25</td>\n", "      <td>4</td>\n", "      <td>4.0</td>\n", "      <td>4.000000</td>\n", "      <td>3.750000</td>\n", "      <td>4.0</td>\n", "      <td>4.0</td>\n", "      <td>3.0</td>\n", "      <td>3.0</td>\n", "      <td>3.0</td>\n", "      <td>100.000000</td>\n", "      <td>93.750000</td>\n", "      <td>100.0</td>\n", "      <td>100.0</td>\n", "      <td>100.0</td>\n", "      <td>100.0</td>\n", "      <td>medium</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>Create an n8n workflow that triggers on a web ...</td>\n", "      <td>24</td>\n", "      <td>9.00</td>\n", "      <td>4</td>\n", "      <td>6.0</td>\n", "      <td>6.000000</td>\n", "      <td>6.000000</td>\n", "      <td>6.0</td>\n", "      <td>6.0</td>\n", "      <td>5.0</td>\n", "      <td>5.0</td>\n", "      <td>5.0</td>\n", "      <td>100.000000</td>\n", "      <td>100.000000</td>\n", "      <td>100.0</td>\n", "      <td>100.0</td>\n", "      <td>100.0</td>\n", "      <td>100.0</td>\n", "      <td>medium</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>Generate a JSON object for an inactive workflo...</td>\n", "      <td>20</td>\n", "      <td>10.00</td>\n", "      <td>4</td>\n", "      <td>0.0</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>easy</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>Generate a JSON object representing a n8n work...</td>\n", "      <td>17</td>\n", "      <td>7.25</td>\n", "      <td>4</td>\n", "      <td>0.0</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>easy</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>Generate an active n8n workflow named \"AppTest...</td>\n", "      <td>1</td>\n", "      <td>8.50</td>\n", "      <td>4</td>\n", "      <td>2.0</td>\n", "      <td>1.333333</td>\n", "      <td>1.333333</td>\n", "      <td>2.0</td>\n", "      <td>2.0</td>\n", "      <td>1.0</td>\n", "      <td>1.0</td>\n", "      <td>1.0</td>\n", "      <td>66.666667</td>\n", "      <td>66.666667</td>\n", "      <td>100.0</td>\n", "      <td>100.0</td>\n", "      <td>100.0</td>\n", "      <td>100.0</td>\n", "      <td>easy</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                                              prompt  index  llm_score  \\\n", "0  Create an n8n workflow named 'Multiple trigger...      8       9.25   \n", "1  Create an n8n workflow that triggers on a web ...     24       9.00   \n", "2  Generate a JSON object for an inactive workflo...     20      10.00   \n", "3  Generate a JSON object representing a n8n work...     17       7.25   \n", "4  Generate an active n8n workflow named \"AppTest...      1       8.50   \n", "\n", "   top_level_keys_present  num_nodes  num_nodes_with_valid_type  \\\n", "0                       4        4.0                   4.000000   \n", "1                       4        6.0                   6.000000   \n", "2                       4        0.0                   0.000000   \n", "3                       4        0.0                   0.000000   \n", "4                       4        2.0                   1.333333   \n", "\n", "   num_nodes_with_valid_version  num_nodes_with_valid_structure  \\\n", "0                      3.750000                             4.0   \n", "1                      6.000000                             6.0   \n", "2                      0.000000                             0.0   \n", "3                      0.000000                             0.0   \n", "4                      1.333333                             2.0   \n", "\n", "   num_nodes_with_parameters  num_connections  \\\n", "0                        4.0              3.0   \n", "1                        6.0              5.0   \n", "2                        0.0              0.0   \n", "3                        0.0              0.0   \n", "4                        2.0              1.0   \n", "\n", "   num_connections_with_valid_structure  \\\n", "0                                   3.0   \n", "1                                   5.0   \n", "2                                   0.0   \n", "3                                   0.0   \n", "4                                   1.0   \n", "\n", "   num_connections_with_valid_target_node  percent_node_with_valid_type  \\\n", "0                                     3.0                    100.000000   \n", "1                                     5.0                    100.000000   \n", "2                                     0.0                           NaN   \n", "3                                     0.0                           NaN   \n", "4                                     1.0                     66.666667   \n", "\n", "   percent_node_with_valid_version  percent_node_with_valid_structure  \\\n", "0                        93.750000                              100.0   \n", "1                       100.000000                              100.0   \n", "2                              NaN                                NaN   \n", "3                              NaN                                NaN   \n", "4                        66.666667                              100.0   \n", "\n", "   percent_connections_with_valid_structure  \\\n", "0                                     100.0   \n", "1                                     100.0   \n", "2                                       NaN   \n", "3                                       NaN   \n", "4                                     100.0   \n", "\n", "   percent_connections_with_valid_target_node  percent_node_with_parameters  \\\n", "0                                       100.0                         100.0   \n", "1                                       100.0                         100.0   \n", "2                                         NaN                           NaN   \n", "3                                         NaN                           NaN   \n", "4                                       100.0                         100.0   \n", "\n", "  difficulty  \n", "0     medium  \n", "1     medium  \n", "2       easy  \n", "3       easy  \n", "4       easy  "]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["df.head()"]}, {"cell_type": "markdown", "id": "e459bb7b", "metadata": {}, "source": ["## Overall"]}, {"cell_type": "code", "execution_count": 4, "id": "aeaeeef2", "metadata": {}, "outputs": [{"data": {"text/plain": ["<Axes: xlabel='llm_score', ylabel='Count'>"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["sns.histplot(df, x=\"llm_score\", bins=20)"]}, {"cell_type": "code", "execution_count": 5, "id": "8825d978", "metadata": {}, "outputs": [{"data": {"text/plain": ["np.float64(1.0)"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["(df[\"llm_score\"]>=6).sum()/len(df)"]}, {"cell_type": "code", "execution_count": 6, "id": "c1ecdd0c", "metadata": {}, "outputs": [{"data": {"text/plain": ["<Axes: xlabel='top_level_keys_present', ylabel='Count'>"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["sns.histplot(df, x=\"top_level_keys_present\")"]}, {"cell_type": "code", "execution_count": 7, "id": "a18db11e", "metadata": {}, "outputs": [{"data": {"text/plain": ["<Axes: xlabel='percent_node_with_valid_type', ylabel='Count'>"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["sns.histplot(df, x=\"percent_node_with_valid_type\")"]}, {"cell_type": "code", "execution_count": 8, "id": "3c7c398e", "metadata": {}, "outputs": [{"data": {"text/plain": ["<Axes: xlabel='percent_node_with_valid_version', ylabel='Count'>"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["sns.histplot(df, x=\"percent_node_with_valid_version\")"]}, {"cell_type": "code", "execution_count": 9, "id": "4c46bdcf", "metadata": {}, "outputs": [{"data": {"text/plain": ["<Axes: xlabel='percent_node_with_valid_structure', ylabel='Count'>"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["sns.histplot(df, x=\"percent_node_with_valid_structure\")"]}, {"cell_type": "code", "execution_count": 10, "id": "44883d67", "metadata": {}, "outputs": [{"data": {"text/plain": ["<Axes: xlabel='percent_node_with_parameters', ylabel='Count'>"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["sns.histplot(df, x=\"percent_node_with_parameters\")"]}, {"cell_type": "code", "execution_count": 11, "id": "048db9e6", "metadata": {}, "outputs": [{"data": {"text/plain": ["<Axes: xlabel='percent_connections_with_valid_structure', ylabel='Count'>"]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["sns.histplot(df, x=\"percent_connections_with_valid_structure\")"]}, {"cell_type": "code", "execution_count": 12, "id": "24225a83", "metadata": {}, "outputs": [{"data": {"text/plain": ["<Axes: xlabel='percent_connections_with_valid_target_node', ylabel='Count'>"]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["sns.histplot(df, x=\"percent_connections_with_valid_target_node\")"]}, {"cell_type": "code", "execution_count": 13, "id": "9bb20feb", "metadata": {}, "outputs": [], "source": ["df_prime = df"]}, {"cell_type": "markdown", "id": "a691c4b9", "metadata": {}, "source": ["## Easy workflows"]}, {"cell_type": "code", "execution_count": 14, "id": "db12a210", "metadata": {}, "outputs": [], "source": ["df = df_prime[df_prime[\"difficulty\"] == \"easy\"]"]}, {"cell_type": "code", "execution_count": 15, "id": "b99f16aa", "metadata": {}, "outputs": [{"data": {"text/plain": ["<Axes: xlabel='llm_score', ylabel='Count'>"]}, "execution_count": 15, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAjcAAAGxCAYAAACeKZf2AAAAOnRFWHRTb2Z0d2FyZQBNYXRwbG90bGliIHZlcnNpb24zLjEwLjAsIGh0dHBzOi8vbWF0cGxvdGxpYi5vcmcvlHJYcgAAAAlwSFlzAAAPYQAAD2EBqD+naQAAJvVJREFUeJzt3X90VPWd//FXyI9JVBKQlCHBEBJACKLUJlLDjyJFQ8FyXHUFYSVYyC40yo9khRJxyw9dQz0Ws60CugLZKtocBT21pkJEQDC0S2KwKPFHhRLUxGwQSSiQkOTz/YNvph0zATJMMpOPz8c5c07u534+d97zOffiy/tjJsgYYwQAAGCJbv4uAAAAwJcINwAAwCqEGwAAYBXCDQAAsArhBgAAWIVwAwAArEK4AQAAViHcAAAAq4T4u4DO1tzcrC+++ELdu3dXUFCQv8sBAAAXwRijuro6xcbGqlu385+b+daFmy+++EJxcXH+LgMAAHjh6NGjuuqqq87b51sXbrp37y7p3ORERkb6uRoAAHAxamtrFRcX5/rv+Pl868JNy6WoyMhIwg0AAF3MxdxSwg3FAADAKoQbAABgFcINAACwCuEGAABYhXADAACsQrgBAABWIdwAAACrEG4AAIBVCDcAAMAqhBsAAGAVv4abt99+W5MnT1ZsbKyCgoL06quvXnDMrl27lJycrPDwcCUmJmrdunUdXygAAOgy/Bpu/va3v2n48OF68sknL6r/4cOHNWnSJI0ZM0ZlZWV68MEHNX/+fG3evLmDKwUAAF2FX384c+<PERSON><PERSON>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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["sns.histplot(df, x=\"llm_score\", bins=20)"]}, {"cell_type": "code", "execution_count": 16, "id": "28199c28", "metadata": {}, "outputs": [{"data": {"text/plain": ["np.float64(1.0)"]}, "execution_count": 16, "metadata": {}, "output_type": "execute_result"}], "source": ["(df[\"llm_score\"]>=6).sum()/len(df)"]}, {"cell_type": "code", "execution_count": 17, "id": "03e96ef0", "metadata": {}, "outputs": [{"data": {"text/plain": ["<Axes: xlabel='top_level_keys_present', ylabel='Count'>"]}, "execution_count": 17, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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*******************************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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["sns.histplot(df, x=\"top_level_keys_present\")"]}, {"cell_type": "code", "execution_count": 18, "id": "0991d8aa", "metadata": {}, "outputs": [{"data": {"text/plain": ["<Axes: xlabel='percent_node_with_valid_type', ylabel='Count'>"]}, "execution_count": 18, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["sns.histplot(df, x=\"percent_node_with_valid_type\")"]}, {"cell_type": "code", "execution_count": 19, "id": "7c475b8e", "metadata": {}, "outputs": [{"data": {"text/plain": ["<Axes: xlabel='percent_node_with_valid_version', ylabel='Count'>"]}, "execution_count": 19, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAj8AAAGxCAYAAACN/tcCAAAAOnRFWHRTb2Z0d2FyZQBNYXRwbG90bGliIHZlcnNpb24zLjEwLjAsIGh0dHBzOi8vbWF0cGxvdGxpYi5vcmcvlHJYcgAAAAlwSFlzAAAPYQAAD2EBqD+naQAAMahJREFUeJzt3XucjeX+//H3Moc1NIxTxtBiyGkkh0bJKVRGakvtik4O22inwYSoJluY2iYV6eCQHKa+ORXyVU0yKRLaZZDShJCxNZpGZUZpMHP9/vCzvi0zTmOtWTOu1/PxWI9H93Vf931/rsvas977PqzlMMYYAQAAWKKcvwsAAAAoSYQfAABgFcIPAACwCuEHAABYhfADAACsQvgBAABWIfwAAACrEH4AAIBVAv1dQEkrKCjQjz/+qIoVK8rhcPi7HAAAcA6MMcrNzVWtWrVUrtyFnbuxLvz8+OOPcrlc/i4DAAAUw759+3TZZZdd0D6sCz8VK1aUdGLyKlWq5OdqAADAucjJyZHL5XJ/jl8I68LPyUtdlSpVIvwAAFDGeOOWFW54BgAAViH8AAAAqxB+AACAVQg/AADAKoQfAABgFcIPAACwCuEHAABYhfADAACsQvgBAABWIfwAAACr+DX8fPrpp+rRo4dq1aolh8OhZcuWnXWbNWvWKDo6WiEhIapfv75mzJjh+0IBAMBFw6/h5/fff1eLFi30yiuvnFP/PXv26Oabb1bHjh21efNmPfHEE4qPj9eSJUt8XCkAALhY+PWHTbt3767u3bufc/8ZM2aoTp06mjJliiQpKipKGzdu1PPPP6877rjDR1UCAICLSZm652fDhg2KiYnxaOvWrZs2btyoY8eO+akqAABQlvj1zM/5OnDggMLDwz3awsPDdfz4cWVnZysiIqLQNnl5ecrLy3Mv5+Tk+LTGjIwMZWdn+/QYAACUZtWrV1edOnX8XcZplanwI0kOh8Nj2RhTZPtJSUlJGj9+vM/rkk4EnyZNonTkyB8lcjwAAEqj8uUr6Lvv0kttACpT4admzZo6cOCAR1tWVpYCAwNVrVq1IrdJSEjQiBEj3Ms5OTlyuVw+qS87O1tHjvyhNgPGqlJEpE+OAQBAaZaT+YP+M2e8srOzCT/e0LZtW7377rsebStXrlTr1q0VFBRU5DZOp1NOp7MkynOrFBGpqnUal+gxAQDAufHrDc+HDx/Wli1btGXLFkknHmXfsmWLMjIyJJ04a9O3b193/0GDBmnv3r0aMWKE0tPTNWfOHM2ePVsjR470R/kAAKAM8uuZn40bN6pLly7u5ZOXp/r166fk5GRlZma6g5Ak1atXTykpKRo+fLimTp2qWrVq6aWXXuIxdwAAcM78Gn46d+7svmG5KMnJyYXaOnXqpE2bNvmwKgAAcDErU9/zAwAAcKEIPwAAwCqEHwAAYBXCDwAAsArhBwAAWIXwAwAArEL4AQAAViH8AAAAqxB+AACAVQg/AADAKoQfAABgFcIPAACwCuEHAABYhfADAACsQvgBAABWIfwAAACrEH4AAIBVCD8AAMAqhB8AAGAVwg8AALAK4QcAAFiF8AMAAKxC+AEAAFYh/AAAAKsQfgAAgFUIPwAAwCqEHwAAYBXCDwAAsArhBwAAWIXwAwAArEL4AQAAViH8AAAAqxB+AACAVQg/AADAKoQfAABgFcIPAACwCuEHAABYhfADAACsQvgBAABWIfwAAACrEH4AAIBVCD8AAMAqhB8AAGAVwg8AALAK4QcAAFiF8AMAAKxC+AEAAFYh/AAAAKsQfgAAgFUIPwAAwCqEHwAAYBXCDwAAsArhBwAAWIXwAwAArEL4AQAAViH8AAAAqxB+AACAVQg/AADAKoQfAABgFcIPAACwit/Dz7Rp01SvXj2FhIQoOjpaa9euPWP/efPmqUWLFqpQoYIiIiL0j3/8QwcPHiyhagEAQFnn1/CzaNEiDRs2TKNHj9bmzZvVsWNHde/eXRkZGUX2/+yzz9S3b1/FxsZq27Ztevvtt/Xll19q4MCBJVw5AAAoq/wafiZPnqzY2FgNHDhQUVFRmjJlilwul6ZPn15k/88//1yRkZGKj49XvXr11KFDBz344IPauHFjCVcOAADKKr+Fn6NHjyotLU0xMTEe7TExMVq/fn2R27Rr107//e9/lZKSImOMfvrpJy1evFi33HLLaY+Tl5ennJwcjxcAALCX38JPdna28vPzFR4e7tEeHh6uAwcOFLlNu3btNG/ePPXu3VvBwcGqWbOmKleurJdffvm0x0lKSlJYWJj75XK5vDoOAABQtvj9hmeHw+GxbIwp1HbSt99+q/j4eD355JNKS0vTihUrtGfPHg0aNOi0+09ISNChQ4fcr3379nm1fgAAULYE+uvA1atXV0BAQKGzPFlZWYXOBp2UlJSk9u3ba9SoUZKk5s2b65JLLlHHjh319NNPKyIiotA2TqdTTqfT+wMAAABlkt/O/AQHBys6Olqpqake7ampqWrXrl2R2/zxxx8qV86z5ICAAEknzhgBAACcjV8ve40YMUKzZs3SnDlzlJ6eruHDhysjI8N9GSshIUF9+/Z19+/Ro4eWLl2q6dOna/fu3Vq3bp3i4+N1zTXXqFatWv4aBgAAKEP8dtlLknr37q2DBw8qMTFRmZmZatasmVJSUlS3bl1JUmZmpsd3/vTv31+5ubl65ZVX9Mgjj6hy5cq6/vrrNXHiRH8NAQAAlDF+DT+SFBcXp7i4uCLXJScnF2obOnSohg4d6uOqAADAxcrvT3sBAACUJMIPAACwCuEHAABYhfADAACsQvgBAABWIfwAAACrEH4AAIBVCD8AAMAqhB8AAGAVwg8AALAK4QcAAFiF8AMAAKxC+AEAAFYh/AAAAKsQfgAAgFUIPwAAwCqEHwAAYBXCDwAAsArhBwAAWIXwAwAArEL4AQAAViH8AAAAqxB+AACAVQg/AADAKoQfAABgFcIPAACwCuEHAABYhfADAACsQvgBAABWIfwAAACrEH4AAIBVCD8AAMAqhB8AAGAVwg8AALAK4QcAAFiF8AMAAKxC+AEAAFYh/AAAAKsQfgAAgFUIPwAAwCqEHwAAYBXCDwAAsArhBwAAWIXwAwAArEL4AQAAViH8AAAAqxB+AACAVQg/AADAKoQfAABgFcIPAACwCuEHAABYhfADAACsQvgBAABWIfwAAACrEH4AAIBVCD8AAMAqhB8AAGAVwg8AALAK4QcAAFjF7+Fn2rRpqlevnkJCQhQdHa21a9eesX9eXp5Gjx6tunXryul06vLLL9ecOXNKqFoAAFDWBfrz4IsWLdKwYcM0bdo0tW/fXq+++qq6d++ub7/9VnXq1Clym169eumnn37S7Nmz1aBBA2VlZen48eMlXDkAACir/Bp+Jk+erNjYWA0cOFCSNGXKFH344YeaPn26kpKSCvVfsWKF1qxZo927d6tq1aqSpMjIyJIsGQAAlHF+u+x19OhRpaWlKSYmxqM9JiZG69evL3Kb5cuXq3Xr1nr22WdVu3ZtNWrUSCNHjtSRI0dOe5y8vDzl5OR4vAAAgL38duYnOztb+fn5Cg8P92gPDw/XgQMHitxm9+7d+uyzzxQSEqJ33nlH2dnZiouL0y+//HLa+36SkpI0fvx4r9cPAADKJr/f8OxwODyWjTGF2k4qKCiQw+HQvHnzdM011+jmm2/W5MmTlZycfNqzPwkJCTp06JD7tW/fPq+PAQAAlB1+O/NTvXp1BQQEFDrLk5WVVehs0EkRERGqXbu2wsLC3G1RUVEyxui///2vGjZsWGgbp9Mpp9Pp3eIBAECZ5bczP8HBwYqOjlZqaqpHe2pqqtq1a1fkNu3bt9ePP/6ow4cPu9t27NihcuXK6bLLLvNpvQAA4OLg18teI0aM0KxZszRnzhylp6dr+PDhysjI0KBBgySduGTVt29fd/97771X1apV0z/+8Q99++23+vTTTzVq1CgNGDBA5cuX99cwAABAGeLXR9179+6tgwcPKjExUZmZmWrWrJlSUlJUt25dSVJmZqYyMjLc/UNDQ5WamqqhQ4eqdevWqlatmnr16qWnn37aX0MAAABljF/DjyTFxcUpLi6uyHXJycmF2po0aVLoUhkAAMC58vvTXgAAACWpWOGnfv36OnjwYKH23377TfXr17/gogAAAHylWOHnhx9+UH5+fqH2vLw87d+//4KLAgAA8JXzuudn+fLl7v/+8MMPPb5vJz8/X6tWreK3tgAAQKl2XuHntttuk3TiW5n79evnsS4oKEiRkZGaNGmS14oDAADwtvMKPwUFBZKkevXq6csvv1T16tV9UhQAAICvFOtR9z179ni7DgAAgBJR7O/5WbVqlVatWqWsrCz3GaGTTvcL6wAAAP5WrPAzfvx4JSYmqnXr1oqIiDjtr7ADAACUNsUKPzNmzFBycrL69Onj7XoAAAB8qljf83P06NHT/vI6AABAaVas8DNw4EDNnz/f27UAAAD4XLEue/3555+aOXOmPvroIzVv3lxBQUEe6ydPnuyV4gAAALytWOFn69atatmypSTpm2++8VjHzc8AAKA0K1b4+eSTT7xdBwAAQIko1j0/AAAAZVWxzvx06dLljJe3Pv7442IXBAAA4EvFCj8n7/c56dixY9qyZYu++eabQj94CgAAUJoUK/y88MILRbaPGzdOhw8fvqCCAAAAfMmr9/zcf//9/K4XAAAo1bwafjZs2KCQkBBv7hIAAMCrinXZ6+9//7vHsjFGmZmZ2rhxo8aMGeOVwgAAAHyhWOEnLCzMY7lcuXJq3LixEhMTFRMT45XCAAAAfKFY4Wfu3LnergMAAKBEFCv8nJSWlqb09HQ5HA41bdpUrVq18lZdAAAAPlGs8JOVlaW7775bq1evVuXKlWWM0aFDh9SlSxctXLhQl156qbfrBAAA8IpiPe01dOhQ5eTkaNu2bfrll1/066+/6ptvvlFOTo7i4+O9XSMAAIDXFOvMz4oVK/TRRx8pKirK3da0aVNNnTqVG54BAECpVqwzPwUFBQoKCirUHhQUpIKCggsuCgAAwFeKFX6uv/56Pfzww/rxxx/dbfv379fw4cN1ww03eK04AAAAbytW+HnllVeUm5uryMhIXX755WrQoIHq1aun3Nxcvfzyy96uEQAAwGuKdc+Py+XSpk2blJqaqu+++07GGDVt2lQ33nijt+sDAADwqvM68/Pxxx+radOmysnJkSR17dpVQ4cOVXx8vK6++mpdccUVWrt2rU8KBQAA8IbzCj9TpkzRAw88oEqVKhVaFxYWpgcffFCTJ0/2WnEAAADedl7h56uvvtJNN9102vUxMTFKS0u74KIAAAB85bzCz08//VTkI+4nBQYG6ueff77gogAAAHzlvMJP7dq19fXXX592/datWxUREXHBRQEAAPjKeYWfm2++WU8++aT+/PPPQuuOHDmisWPH6m9/+5vXigMAAPC283rU/V//+peWLl2qRo0aaciQIWrcuLEcDofS09M1depU5efna/To0b6qFQAA4IKdV/gJDw/X+vXr9dBDDykhIUHGGEmSw+FQt27dNG3aNIWHh/ukUAAAAG847y85rFu3rlJSUvTrr7/q+++/lzFGDRs2VJUqVXxRHwAAgFcV6xueJalKlSq6+uqrvVkLAACAzxXrt70AAADKKsIPAACwCuEHAABYhfADAACsQvgBAABWIfwAAACrEH4AAIBVCD8AAMAqhB8AAGAVwg8AALAK4QcAAFiF8AMAAKxC+AEAAFYh/AAAAKsQfgAAgFUIPwAAwCqEHwAAYBW/h59p06apXr16CgkJUXR0tNauXXtO261bt06BgYFq2bKlbwsEAAAXFb+Gn0WLFmnYsGEaPXq0Nm/erI4dO6p79+7KyMg443aHDh1S3759dcMNN5RQpQAA4GLh1/AzefJkxcbGauDAgYqKitKUKVPkcrk0ffr0M2734IMP6t5771Xbtm1LqFIAAHCx8Fv4OXr0qNLS0hQTE+PRHhMTo/Xr1592u7lz52rXrl0aO3asr0sEAAAXoUB/HTg7O1v5+fkKDw/3aA8PD9eBAweK3Gbnzp16/PHHtXbtWgUGnlvpeXl5ysvLcy/n5OQUv2gAAFDm+f2GZ4fD4bFsjCnUJkn5+fm69957NX78eDVq1Oic95+UlKSwsDD3y+VyXXDNAACg7PJb+KlevboCAgIKneXJysoqdDZIknJzc7Vx40YNGTJEgYGBCgwMVGJior766isFBgbq448/LvI4CQkJOnTokPu1b98+n4wHAACUDX677BUcHKzo6Gilpqbq9ttvd7enpqaqZ8+ehfpXqlRJX3/9tUfbtGnT9PHHH2vx4sWqV69ekcdxOp1yOp3eLR4AAJRZfgs/kjRixAj16dNHrVu3Vtu2bTVz5kxlZGRo0KBBkk6ctdm/f7/eeOMNlStXTs2aNfPYvkaNGgoJCSnUDgAAcDp+DT+9e/fWwYMHlZiYqMzMTDVr1kwpKSmqW7euJCkzM/Os3/kDAABwPvwafiQpLi5OcXFxRa5LTk4+47bjxo3TuHHjvF8UAAC4aPn9aS8AAICSRPgBAABWIfwAAACrEH4AAIBVCD8AAMAqhB8AAGAVwg8AALAK4QcAAFiF8AMAAKxC+AEAAFYh/AAAAKsQfgAAgFUIPwAAwCqEHwAAYBXCDwAAsArhBwAAWIXwAwAArEL4AQAAViH8AAAAqxB+AACAVQg/AADAKoQfAABgFcIPAACwCuEHAABYhfADAACsQvgBAABWIfwAAACrEH4AAIBVCD8AAMAqhB8AAGAVwg8AALAK4QcAAFiF8AMAAKxC+AEAAFYh/AAAAKsQfgAAgFUIPwAAwCqEHwAAYBXCDwAAsArhBwAAWIXwAwAArEL4AQAAViH8AAAAqxB+AACAVQg/AADAKoQfAABgFcIPAACwCuEHAABYhfADAACsQvgBAABWIfwAAACrEH4AAIBVCD8AAMAqhB8AAGAVwg8AALAK4QcAAFiF8AMAAKxC+AEAAFYh/AAAAKsQfgAAgFX8Hn6mTZumevXqKSQkRNHR0Vq7du1p+y5dulRdu3bVpZdeqkqVKqlt27b68MMPS7BaAABQ1vk1/CxatEjDhg3T6NGjtXnzZnXs2FHdu3dXRkZGkf0//fRTde3aVSkpKUpLS1OXLl3Uo0cPbd68uYQrBwAAZZVfw8/kyZMVGxurgQMHKioqSlOmTJHL5dL06dOL7D9lyhQ9+uijuvrqq9WwYUNNmDBBDRs21LvvvlvClQMAgLLKb+Hn6NGjSktLU0xMjEd7TEyM1q9ff077KCgoUG5urqpWrXraPnl5ecrJyfF4AQAAe/kt/GRnZys/P1/h4eEe7eHh4Tpw4MA57WPSpEn6/fff1atXr9P2SUpKUlhYmPvlcrkuqG4AAFC2+f2GZ4fD4bFsjCnUVpQFCxZo3LhxWrRokWrUqHHafgkJCTp06JD7tW/fvguuGQAAlF2B/jpw9erVFRAQUOgsT1ZWVqGzQadatGiRYmNj9fbbb+vGG288Y1+n0ymn03nB9QIAgIuD3878BAcHKzo6WqmpqR7tqampateu3Wm3W7Bggfr376/58+frlltu8XWZAADgIuO3Mz+SNGLECPXp00etW7dW27ZtNXPmTGVkZGjQoEGSTlyy2r9/v9544w1JJ4JP37599eKLL+raa691nzUqX768wsLC/DYOAABQdvg1/PTu3VsHDx5UYmKiMjMz1axZM6WkpKhu3bqSpMzMTI/v/Hn11Vd1/PhxDR48WIMHD3a39+vXT8nJySVdPgAAKIP8Gn4kKS4uTnFxcUWuOzXQrF692vcFAQCAi5rfn/YCAAAoSYQfAABgFcIPAACwCuEHAABYhfADAACsQvgBAABWIfwAAACrEH4AAIBVCD8AAMAqhB8AAGAVwg8AALAK4QcAAFiF8AMAAKxC+AEAAFYh/AAAAKsQfgAAgFUIPwAAwCqEHwAAYBXCDwAAsArhBwAAWIXwAwAArEL4AQAAViH8AAAAqxB+AACAVQg/AADAKoQfAABgFcIPAACwCuEHAABYhfADAACsQvgBAABWIfwAAACrEH4AAIBVCD8AAMAqhB8AAGAVwg8AALAK4QcAAFiF8AMAAKxC+AEAAFYh/AAAAKsQfgAAgFUIPwAAwCqEHwAAYBXCDwAAsArhBwAAWIXwAwAArEL4AQAAViH8AAAAqxB+AACAVQg/AADAKoQfAABgFcIPAACwCuEHAABYhfADAACsQvgBAABWIfwAAACrEH4AAIBVCD8AAMAqhB8AAGAVwg8AALCK38PPtGnTVK9ePYWEhCg6Olpr1649Y/81a9YoOjpaISEhql+/vmbMmFFClQIAgIuBX8PPokWLNGzYMI0ePVqbN29Wx44d1b17d2VkZBTZf8+ePbr55pvVsWNHbd68WU888YTi4+O1ZMmSEq4cAACUVX4NP5MnT1ZsbKwGDhyoqKgoTZkyRS6XS9OnTy+y/4wZM1SnTh1NmTJFUVFRGjhwoAYMGKDnn3++hCsHAABlld/Cz9GjR5WWlqaYmBiP9piYGK1fv77IbTZs2FCof7du3bRx40YdO3bMZ7UCAICLR6C/Dpydna38/HyFh4d7tIeHh+vAgQNFbnPgwIEi+x8/flzZ2dmKiIgotE1eXp7y8vLcy4cOHZIk5eTkXOgQCjl8+LAk6Ze923U874jX9w8AQGmXc+DErSuHDx/26mftyX0ZYy54X34LPyc5HA6PZWNMobaz9S+q/aSkpCSNHz++ULvL5TrfUs9Z2pvP+GzfAACUBZ06dfLJfnNzcxUWFnZB+/Bb+KlevboCAgIKneXJysoqdHbnpJo1axbZPzAwUNWqVStym4SEBI0YMcK9XFBQoF9++UXVqlU7Y8gqaTk5OXK5XNq3b58qVark73IuWsxzyWCeSw5zXTKY55Jxpnk2xig3N1e1atW64OP4LfwEBwcrOjpaqampuv32293tqamp6tmzZ5HbtG3bVu+++65H28qVK9W6dWsFBQUVuY3T6ZTT6fRoq1y58oUV70OVKlXif1glgHkuGcxzyWGuSwbzXDJON88XesbnJL8+7TVixAjNmjVLc+bMUXp6uoYPH66MjAwNGjRI0omzNn379nX3HzRokPbu3asRI0YoPT1dc+bM0ezZszVy5Eh/DQEAAJQxfr3np3fv3jp48KASExOVmZmpZs2aKSUlRXXr1pUkZWZmenznT7169ZSSkqLhw4dr6tSpqlWrll566SXdcccd/hoCAAAoY/x+w3NcXJzi4uKKXJecnFyorVOnTtq0aZOPqyp5TqdTY8eOLXSJDt7FPJcM5rnkMNclg3kuGSU1zw7jjWfGAAAAygi//7YXAABASSL8AAAAqxB+AACAVQg/PrB//37df//9qlatmipUqKCWLVsqLS3No096erpuvfVWhYWFqWLFirr22mtP+2v2kvTaa6+pY8eOqlKliqpUqaIbb7xRX3zxha+HUur5Yq7/auHChXI4HLrtttt8UH3Z4at5/u233zR48GBFREQoJCREUVFRSklJ8eVQSjVfzfOUKVPUuHFjlS9fXi6XS8OHD9eff/7py6GUamebZ4fDUeTrueeeO+N+lyxZoqZNm8rpdKpp06Z65513fD2UUs0X8+ytz0K/P+11sfn111/Vvn17denSRR988IFq1KihXbt2eXyx4q5du9ShQwfFxsZq/PjxCgsLU3p6ukJCQk6739WrV+uee+5Ru3btFBISomeffVYxMTHatm2bateuXQIjK318Ndcn7d27VyNHjlTHjh19OIrSz1fzfPToUXXt2lU1atTQ4sWLddlll2nfvn2qWLFiCYyq9PHVPM+bN0+PP/645syZo3bt2mnHjh3q37+/JOmFF17w8ahKn3OZ58zMTI9tPvjgA8XGxp7xa1U2bNig3r1766mnntLtt9+ud955R7169dJnn32mNm3a+Go4pZav5tlrn4UGXvXYY4+ZDh06nLFP7969zf33339Bxzl+/LipWLGief311y9oP2WZL+f6+PHjpn379mbWrFmmX79+pmfPnsWssuzz1TxPnz7d1K9f3xw9evRCyrto+GqeBw8ebK6//nqPthEjRpz1WBerc5nnU/Xs2bPQHJ6qV69e5qabbvJo69atm7n77rvPu8aLga/m+VTF/SzkspeXLV++XK1bt9Zdd92lGjVqqFWrVnrttdfc6wsKCvT++++rUaNG6tatm2rUqKE2bdpo2bJl53WcP/74Q8eOHVPVqlW9PIKyw5dznZiYqEsvvVSxsbE+HEHZ4Kt5Xr58udq2bavBgwcrPDxczZo104QJE5Sfn+/jEZVOvprnDh06KC0tzX1pYPfu3UpJSdEtt9ziy+GUWmeb51P99NNPev/998/6t2DDhg2KiYnxaOvWrZvWr1/vlbrLGl/N86mK/Vl4XlEJZ+V0Oo3T6TQJCQlm06ZNZsaMGSYkJMSdSjMzM40kU6FCBTN58mSzefNmk5SUZBwOh1m9evU5HycuLs5cfvnl5siRI74aSqnnq7n+7LPPTO3atc3PP/9sjDHWn/nx1Tw3btzYOJ1OM2DAALNx40azYMECU7VqVTN+/PiSGlqp4su/HS+99JIJCgoygYGBRpJ56KGHSmJIpdLZ5vlUEydONFWqVDnr39qgoCAzb948j7Z58+aZ4OBgr9Velvhqnk9V3M9Cwo+XBQUFmbZt23q0DR061Fx77bXGGGP2799vJJl77rnHo0+PHj3O+fToyTfJV1995Z2iyyhfzHVOTo6JjIw0KSkp7jbbw4+v3tMNGzY0LpfLHD9+3N02adIkU7NmTS9WX3b4ap4/+eQTEx4ebl577TWzdetWs3TpUuNyuUxiYqL3B1EGnG2eT9W4cWMzZMiQc9rv/PnzPdrefPNN43Q6i19sGearef6rC/ks5LKXl0VERKhp06YebVFRUe6nMapXr67AwMAz9jmT559/XhMmTNDKlSvVvHlz7xVeBvlirnft2qUffvhBPXr0UGBgoAIDA/XGG29o+fLlCgwM1K5du3wzmFLMV+/piIgINWrUSAEBAR7bHDhwQEePHvXiCMoGX83zmDFj1KdPHw0cOFBXXnmlbr/9dk2YMEFJSUkqKCjw/kBKubPN81+tXbtW27dv18CBA8+635o1a+rAgQMebVlZWQoPD7+wgssoX83zSRf6WUj48bL27dtr+/btHm07duxw/1hrcHCwrr766jP2OZ3nnntOTz31lFasWKHWrVt7t/AyyBdz3aRJE3399dfasmWL+3XrrbeqS5cu2rJli1wul28GU4r56j3dvn17ff/99x4fwDt27FBERISCg4O9OIKywVfz/Mcff6hcOc8/9QEBATInzvx7qfqy42zz/FezZ89WdHS0WrRocdb9tm3bVqmpqR5tK1euVLt27S6s4DLKV/Mseemz8LzPFeGMvvjiCxMYGGj+/e9/m507d5p58+aZChUqmDfffNPdZ+nSpSYoKMjMnDnT7Ny507z88ssmICDArF271t2nT58+5vHHH3cvT5w40QQHB5vFixebzMxM9ys3N7dEx1ea+GquT2X7ZS9fzXNGRoYJDQ01Q4YMMdu3bzfvvfeeqVGjhnn66adLdHylha/meezYsaZixYpmwYIFZvfu3WblypXm8ssvN7169SrR8ZUW5zLPxhhz6NAhU6FCBTN9+vQi93PqPK9bt84EBASYZ555xqSnp5tnnnnGBAYGms8//9yn4ymtfDXP3vosJPz4wLvvvmuaNWtmnE6nadKkiZk5c2ahPrNnzzYNGjQwISEhpkWLFmbZsmUe6zt16mT69evnXq5bt66RVOg1duxYH4+mdPPFXJ/K9vBjjO/mef369aZNmzbG6XSa+vXrm3//+98e9wDZxhfzfOzYMTNu3Dhz+eWXm5CQEONyuUxcXJz59ddffTya0utc5vnVV1815cuXN7/99luR+yjq/fz222+bxo0bm6CgINOkSROzZMkSX5RfZvhinr31WcivugMAAKtwzw8AALAK4QcAAFiF8AMAAKxC+AEAAFYh/AAAAKsQfgAAgFUIPwAAwCqEHwAAYBXCDwCvWr16tRwOh3777bcSP/a4cePUsmXLM/b54Ycf5HA4tGXLlhKpqSin1tm/f3/ddtttZ9ymc+fOGjZsmE/ruhD+/HcHzhfhB7hIOBwOLVu2zN9l+NXIkSO1atUq9/K5hIrS4MUXX1RycrK/y7gg7dq1U2ZmpsLCwvxdCnBWgf4uALiY5efny+FwFPpVbfhGaGioQkND/V3GeSsLgeHYsWMKCgo67frg4GDVrFmzBCsCio+/yMBfdO7cWUOGDNGQIUNUuXJlVatWTf/617908ifwjh49qkcffVS1a9fWJZdcojZt2mj16tXu7ZOTk1W5cmW99957atq0qZxOp/bu3au8vDw9+uijcrlccjqdatiwoWbPnu3e7ttvv9XNN9+s0NBQhYeHq0+fPsrOzvaoKz4+Xo8++qiqVq2qmjVraty4ce71kZGRkqTbb79dDofDvXwmJy+9/M///I8iIyMVFhamu+++W7m5ue4+eXl5io+PV40aNRQSEqIOHTroyy+/9NhPSkqKGjVqpPLly6tLly764YcfCh1r/fr1uu6661S+fHm5XC7Fx8fr999/P2uNL7/8sq688kr38rJly+RwODR16lR3W7du3ZSQkOAxppP//frrr+t///d/5XA45HA4PP6tdu/erS5duqhChQpq0aKFNmzYcNZ6Dh06pPLly2vFihUe7UuXLtUll1yiw4cPS5Iee+wxNWrUSBUqVFD9+vU1ZswYHTt27LT7PfUM1e+//66+ffsqNDRUERERmjRp0llrOykhIUHXXnttofbmzZtr7Nix7uW5c+cqKipKISEhatKkiaZNm+Zed/LS4FtvvaXOnTsrJCREb775pvbu3asePXqoSpUquuSSS3TFFVcoJSVFUtGXvZYsWaIrrrhCTqdTkZGRhcYRGRmpCRMmaMCAAapYsaLq1KmjmTNnnvNYgWI7r59BBS5ynTp1MqGhoebhhx823333nXnzzTdNhQoV3L9GfO+995p27dqZTz/91Hz//ffmueeeM06n0+zYscMYY8zcuXNNUFCQadeunVm3bp357rvvzOHDh02vXr2My+UyS5cuNbt27TIfffSRWbhwoTHGmB9//NFUr17dJCQkmPT0dLNp0ybTtWtX06VLF4+6KlWqZMaNG2d27NhhXn/9deNwOMzKlSuNMcZkZWUZSWbu3LkmMzPTZGVlnXWsY8eONaGhoebvf/+7+frrr82nn35qatasaZ544gl3n/j4eFOrVi2TkpJitm3bZvr162eqVKliDh48aIwxJiMjwzidTo/5Cg8PN5Lcvxq+detWExoaal544QWzY8cOs27dOtOqVSvTv3//s9a4detW43A4zM8//2yMMWbYsGGmevXq5q677jLGnPjF8tDQUPPBBx+4x9SiRQtjjDG5ubmmV69e5qabbjKZmZkmMzPT5OXlmT179hhJpkmTJua9994z27dvN3feeaepW7euOXbs2FlruuOOO8z9999fqO2ee+5xLz/11FNm3bp1Zs+ePWb58uUmPDzcTJw40WPuT9ZpjDH9+vUzPXv2dC8/9NBD5rLLLjMrV640W7duNX/729/c78uz+frrr40k8/3337vbvvnmGyPJbN++3RhjzMyZM01ERIRZsmSJ2b17t1myZImpWrWqSU5ONsYY9xxFRka6++zfv9/ccsstpmvXrmbr1q1m165d5t133zVr1qwxxhjzySefePy7b9y40ZQrV84kJiaa7du3m7lz55ry5cubuXPnuuuqW7euqVq1qpk6darZuXOnSUpKMuXKlTPp6elnHSdwIQg/wF906tTJREVFmYKCAnfbY489ZqKiosz3339vHA6H2b9/v8c2N9xwg0lISDDGnAg/ksyWLVvc67dv324kmdTU1CKPOWbMGBMTE+PRtm/fPo8Pq06dOpkOHTp49Ln66qvNY4895l6WZN55551zHuvYsWNNhQoVTE5Ojrtt1KhRpk2bNsYYYw4fPmyCgoLMvHnz3OuPHj1qatWqZZ599lljjDEJCQlFztdfPwT79Olj/vnPf3oce+3ataZcuXLmyJEjZ6yxoKDAVK9e3SxevNgYY0zLli1NUlKSqVGjhjHGmPXr15vAwECTm5vrHtOZQoUx//fBPmvWLHfbtm3bjKRz+tBdunSpCQ0NNb///rsxxphDhw6ZkJAQ8/777592m2effdZER0e7l89UZ25urgkODnaHY2OMOXjwoClfvvw5hR9jjGnevLlJTEx0LyckJJirr77avexyucz8+fM9tnnqqadM27ZtjTH/N0dTpkzx6HPllVeacePGFXnMU8PPvffea7p27erRZ9SoUaZp06bu5bp163oEyYKCAlOjRg0zffr0cxonUFxc9gJOce2118rhcLiX27Ztq507d2rjxo0yxqhRo0bue0tCQ0O1Zs0a7dq1y90/ODhYzZs3dy9v2bJFAQEB6tSpU5HHS0tL0yeffOKxzyZNmkiSx37/uk9JioiIUFZW1gWNNTIyUhUrVixyn7t27dKxY8fUvn179/qgoCBdc801Sk9PlySlp6cXOV+nji85OdljfN26dVNBQYH27NlzxvocDoeuu+46rV69Wr/99pu2bdumQYMGKT8/X+np6Vq9erWuuuqqYt3n89f5jIiIkKRzms9bbrlFgYGBWr58uaQTl3YqVqyomJgYd5/FixerQ4cOqlmzpkJDQzVmzBhlZGScU127du3S0aNHPeaxatWqaty48TltL0n33Xef5s2bJ0kyxmjBggW67777JEk///yz9u3bp9jYWI9/k6efftrj/SZJrVu39liOj4/X008/rfbt22vs2LHaunXraWtIT0/3eO9IUvv27bVz507l5+e72/767+BwOFSzZs0Lfl8DZ8MNz8B5CAgIUFpamgICAjza//rhW758eY8wUL58+TPus6CgQD169NDEiRMLrTv5oSyp0M2mDodDBQUF51X/qc60T/P/73P661hOtp9sO9nnTAoKCvTggw8qPj6+0Lo6deqcdfvOnTtr5syZWrt2rVq0aKHKlSvruuuu05o1a7R69Wp17tz5rPsoyl/HfnI85zKfwcHBuvPOOzV//nzdfffdmj9/vnr37q3AwBN/Tj///HPdfffdGj9+vLp166awsDAtXLjwnO/bOZc5PZt7771Xjz/+uDZt2qQjR45o3759uvvuuyX93xhfe+01tWnTxmO7U9/Xl1xyicfywIED1a1bN73//vtauXKlkpKSNGnSJA0dOrTIcRT13jmVL97XwNlw5gc4xeeff15ouWHDhmrVqpXy8/OVlZWlBg0aeLzO9JTLlVdeqYKCAq1Zs6bI9VdddZW2bdumyMjIQvs99cPnTIKCgjz+H/WFatCggYKDg/XZZ5+5244dO6aNGzcqKipKktS0adMi5+uvTo7v1LGd3P/ZdO7cWdu2bdPixYvdQadTp0766KOPtH79+tOeUZNOBBVvzslJ9913n1asWKFt27bpk08+cZ9VkaR169apbt26Gj16tFq3bq2GDRtq796957zvBg0aKCgoyGMef/31V+3YseOc93HZZZfpuuuu07x58zRv3jzdeOONCg8PlySFh4erdu3a2r17d6F/j3r16p113y6XS4MGDdLSpUv1yCOP6LXXXiuyX9OmTT3eO9KJG98bNWpUKGQBJY3wA5xi3759GjFihLZv364FCxbo5Zdf1sMPP6xGjRrpvvvuU9++fbV06VLt2bNHX375pSZOnOh+4qUokZGR6tevnwYMGKBly5Zpz549Wr16td566y1J0uDBg/XLL7/onnvu0RdffKHdu3dr5cqVGjBgwHl9cEdGRmrVqlU6cOCAfv311wueh0suuUQPPfSQRo0apRUrVujbb7/VAw88oD/++EOxsbGSpEGDBmnXrl3u+Zo/f36h76t57LHHtGHDBg0ePFhbtmzRzp07tXz58iLPFhSlWbNmqlatmubNm+cOP507d9ayZct05MgRdejQ4bTbRkZGauvWrdq+fbuys7PP+MTV+ejUqZPCw8N13333KTIy0uPpqgYNGigjI0MLFy7Url279NJLL+mdd945532HhoYqNjZWo0aN0qpVq/TNN9+of//+5/11Cffdd58WLlyot99+W/fff7/HunHjxikpKUkvvviiduzYoa+//lpz587V5MmTz7jPYcOG6cMPP9SePXu0adMmffzxx+4gfKpHHnlEq1at0lNPPaUdO3bo9ddf1yuvvKKRI0ee1zgAXyD8AKfo27evjhw5omuuuUaDBw/W0KFD9c9//lPSiceD+/btq0ceeUSNGzfWrbfeqv/85z9yuVxn3Of06dN15513Ki4uTk2aNNEDDzzgftS7Vq1aWrdunfLz89WtWzc1a9ZMDz/8sMLCws7rA2/SpElKTU2Vy+VSq1atij8Bf/HMM8/ojjvuUJ8+fXTVVVfp+++/14cffqgqVapIOnHZasmSJXr33XfVokULzZgxQxMmTPDYR/PmzbVmzRrt3LlTHTt2VKtWrTRmzBiPS3pn4nA43Gd3Onbs6N5nWFiYWrVqpUqVKp122wceeECNGzdW69atdemll2rdunXFmYYia7rnnnv01VdfeZz1kaSePXtq+PDhGjJkiFq2bKn169drzJgx57X/5557Ttddd51uvfVW3XjjjerQoYOio6PPax933XWXDh48qD/++KPQFz0OHDhQs2bNUnJysq688kp16tRJycnJZz3zk5+fr8GDBysqKko33XSTGjdu7PGI/F9dddVVeuutt7Rw4UI1a9ZMTz75pBITE9W/f//zGgfgCw7jjQvMwEWic+fOatmypaZMmeLvUgAAPsKZHwAAYBXCD3CRuuKKKzweZf7r6+Rj0P62du3a09bor5+p6N69+2nrOfWSnj+UxjkDyhouewEXqb179572Bt/w8HCP7/fxlyNHjmj//v2nXd+gQYMSrOaE/fv368iRI0Wuq1q1qqpWrVrCFXkqjXMGlDWEHwAAYBUuewEAAKsQfgAAgFUIPwAAwCqEHwAAYBXCDwAAsArhBwAAWIXwAwAArEL4AQAAVvl/A9k0Kgk8kTcAAAAASUVORK5CYII=", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["sns.histplot(df, x=\"percent_node_with_valid_version\")"]}, {"cell_type": "markdown", "id": "9291756b", "metadata": {}, "source": ["## Medium workflows"]}, {"cell_type": "code", "execution_count": 20, "id": "7c7d96c7", "metadata": {}, "outputs": [], "source": ["df = df_prime[df_prime[\"difficulty\"] == \"medium\"]"]}, {"cell_type": "code", "execution_count": 21, "id": "44472701", "metadata": {}, "outputs": [{"data": {"text/plain": ["<Axes: xlabel='llm_score', ylabel='Count'>"]}, "execution_count": 21, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["sns.histplot(df, x=\"llm_score\", bins=20)"]}, {"cell_type": "code", "execution_count": 22, "id": "09842340", "metadata": {}, "outputs": [{"data": {"text/plain": ["np.float64(1.0)"]}, "execution_count": 22, "metadata": {}, "output_type": "execute_result"}], "source": ["(df[\"llm_score\"]>=6).sum()/len(df)"]}, {"cell_type": "code", "execution_count": 23, "id": "a4688dfe", "metadata": {}, "outputs": [{"data": {"text/plain": ["<Axes: xlabel='top_level_keys_present', ylabel='Count'>"]}, "execution_count": 23, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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*******************************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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["sns.histplot(df, x=\"top_level_keys_present\")"]}, {"cell_type": "code", "execution_count": 24, "id": "8066e818", "metadata": {}, "outputs": [{"data": {"text/plain": ["<Axes: xlabel='percent_node_with_valid_type', ylabel='Count'>"]}, "execution_count": 24, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["sns.histplot(df, x=\"percent_node_with_valid_type\")"]}, {"cell_type": "code", "execution_count": 25, "id": "90a9d9a3", "metadata": {}, "outputs": [{"data": {"text/plain": ["<Axes: xlabel='percent_node_with_valid_version', ylabel='Count'>"]}, "execution_count": 25, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["sns.histplot(df, x=\"percent_node_with_valid_version\")"]}, {"cell_type": "markdown", "id": "4d763954", "metadata": {}, "source": ["## Hard workflows"]}, {"cell_type": "code", "execution_count": 26, "id": "0c4a08d8", "metadata": {}, "outputs": [], "source": ["df = df_prime[df_prime[\"difficulty\"] == \"hard\"]"]}, {"cell_type": "code", "execution_count": 27, "id": "521a045f", "metadata": {}, "outputs": [{"data": {"text/plain": ["<Axes: xlabel='llm_score', ylabel='Count'>"]}, "execution_count": 27, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["sns.histplot(df, x=\"llm_score\", bins=20)"]}, {"cell_type": "code", "execution_count": 28, "id": "19ab5cdf", "metadata": {}, "outputs": [{"data": {"text/plain": ["np.float64(1.0)"]}, "execution_count": 28, "metadata": {}, "output_type": "execute_result"}], "source": ["(df[\"llm_score\"]>=6).sum()/len(df)"]}, {"cell_type": "code", "execution_count": 29, "id": "516e335b", "metadata": {}, "outputs": [{"data": {"text/plain": ["<Axes: xlabel='top_level_keys_present', ylabel='Count'>"]}, "execution_count": 29, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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*******************************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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["sns.histplot(df, x=\"top_level_keys_present\")"]}, {"cell_type": "code", "execution_count": 30, "id": "4cd2f1aa", "metadata": {}, "outputs": [{"data": {"text/plain": ["<Axes: xlabel='percent_node_with_valid_type', ylabel='Count'>"]}, "execution_count": 30, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["sns.histplot(df, x=\"percent_node_with_valid_type\")"]}, {"cell_type": "code", "execution_count": 31, "id": "d7940155", "metadata": {}, "outputs": [{"data": {"text/plain": ["<Axes: xlabel='percent_node_with_valid_version', ylabel='Count'>"]}, "execution_count": 31, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["sns.histplot(df, x=\"percent_node_with_valid_version\")"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.9"}}, "nbformat": 4, "nbformat_minor": 5}