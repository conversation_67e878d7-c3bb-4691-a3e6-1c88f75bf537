import json
import re

from openai import OpenAI

# ---------------------
# Parameters
# ---------------------
JSON_FILE = "sample.json"  # Read and update in place
reasoning_level = "high"
REQUESTY_API_KEY = "sk-PcLyYtnhSm2UntRhL6aECcw1ZGxqbDoeU8midASAgsSwAuh/Sy5cX0YF6/O/tkyvDFvdKJLrmJMFY4z7ha7obgkcsoPHiwQnO4icZabGGg4="
t = 3
# ---------------------
# Setup OpenAI Client (Requesty)
# ---------------------

client = OpenAI(
    api_key=REQUESTY_API_KEY,
    base_url="https://router.requesty.ai/v1",
)

# ---------------------
# Function to Generate JSON for Prompt
# ---------------------
def generate_json_from_prompt(prompt: str) -> str:
    SYSTEM_PROMPT ="""
Generate a valid n8n workflow JSON in the following format:

<Thinking>
Step-by-step reasoning:
1. Goal: What is the user trying to achieve?
2. Nodes: Which nodes will you use? (name + type) Why are they needed?
3. Connections: How are nodes connected? List flow and order.
</Thinking>

<Json>
{Valid JSON here — strictly no comments}
</Json>

=============== JSON Structure ===============

{
  "name": "Workflow Name",
  "nodes": [NODE_OBJECTS],
  "connections": CONNECTIONS_OBJECT,
  "active": false
}

Each node:
{
  "parameters": { ... },
  "name": "NodeName",           // Unique
  "type": "NODE_TYPE",          // Must be valid
  "typeVersion": INTEGER,       // Must be valid
  "position": [X, Y]            // Canvas position
}

Connections:
{
  "SourceNode": {
    "main": [
      [
        { "node": "TargetNode", "type": "main", "index": 0 }
      ]
    ]
  }
}

=============== Key Rules ===============

- All strings in double quotes
- Indent JSON with 2 spaces
- No comments in JSON
- JSON must be directly parsable (e.g. `json.loads`)

=============== Validation ===============

- The <Thinking> section is required and must follow: Goal → Nodes → Connections
- Node names must be unique
- Each node must have:
  - Valid `parameters` (non-empty if required)
  - Unique `name`
  - Valid `type` and `typeVersion`
  - `position`: two integers in an array
- Connections must:
  - Reference correct node names
  - Use "main" as connection type unless otherwise needed
  - Use 2D array format: "main": [[{ ... }]]
  - Use "index": 0 unless another index is required
- Final JSON must be valid with no trailing commasappropriate.
"""+f"""
Reasoning level: {reasoning_level}
""" # Same as before (truncated for brevity)

    response = client.chat.completions.create(
        model="groq/openai/gpt-oss-20b",
        messages=[
            {"role": "system", "content": SYSTEM_PROMPT},
            {"role": "user", "content": prompt},
        ],
    )

    content = response.choices[0].message.content.strip()

    # Extract the <Json> section
    json_match = re.search(
        r"<Json>\s*```(?:json)?\s*(.*?)\s*```\s*</Json>", content, re.DOTALL
    )
    if not json_match:
        json_match = re.search(r"<Json>\s*(.*?)\s*</Json>", content, re.DOTALL)

    if json_match:
        cleaned_json = json_match.group(1).strip()
        return cleaned_json
    else:
        raise ValueError("JSON output not found in model response.")


# ---------------------
# Function to Score Generated JSON
# ---------------------
def verify_prompt_json_match(prompt: str, json_str: str) -> float:
    verification_prompt = f"""Evaluate if the JSON fulfills the given PROMPT.

PROMPT: {prompt}
JSON: {json_str}

Score 0-10 for:
1. Prompt-JSON Match: Does JSON fulfill the prompt?

Respond only with JSON:
{{"prompt_json_score": <score>, "summary": "<brief_assessment>"}}"""

    response = client.chat.completions.create(
        model="google/gemini-2.5-flash",
        messages=[
            {
                "role": "system",
                "content": "You are an expert evaluator. Respond only with valid JSON.",
            },
            {"role": "user", "content": verification_prompt},
        ],
    )

    result_text = response.choices[0].message.content.strip()

    try:
        return json.loads(result_text).get("prompt_json_score", 0.0)
    except json.JSONDecodeError:
        if "```json" in result_text:
            json_start = result_text.find("```json") + 7
            json_end = result_text.find("```", json_start)
            try:
                cleaned = result_text[json_start:json_end].strip()
                return json.loads(cleaned).get("prompt_json_score", 0.0)
            except:
                pass
        print("Invalid JSON returned by LLM.")
        return 0.0


# ---------------------
# Main Logic with Retry
# ---------------------
def main():
    with open(JSON_FILE, "r") as f:
        dataset = json.load(f)

    for i, item in enumerate(dataset):
        prompt = item.get("prompt", "")
        generated_json = None
        max_attempts = 3

        for attempt in range(1, max_attempts + 1):
            try:
                generated_json = generate_json_from_prompt(prompt)
                if generated_json:
                    break
            except Exception as e:
                print(f"[{i + 1}] Attempt {attempt} failed: {e}")

        if not generated_json:
            print(f"[{i + 1}] Skipping: Failed to generate JSON after {max_attempts} attempts.")
            item["generated_json"] = ""
            item["llm_score"] = 0
            continue

        item["generated_json"] = generated_json
        score = verify_prompt_json_match(prompt, generated_json)
        item["llm_score"] = score

        print(f"[{i + 1}/{len(dataset)}] Scored: {score}")

    with open(f"{JSON_FILE}_{reasoning_level}_{t}.json", "w") as f:
        json.dump(dataset, f, indent=2)


if __name__ == "__main__":
    main()
