{"cells": [{"cell_type": "code", "execution_count": 1, "id": "cf9d20bd", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import seaborn as sns"]}, {"cell_type": "code", "execution_count": 2, "id": "2a29a094", "metadata": {}, "outputs": [], "source": ["df = pd.read_json(\"sample.json_low_mean.json\")"]}, {"cell_type": "code", "execution_count": 3, "id": "55ed4ffb", "metadata": {}, "outputs": [{"data": {"application/vnd.microsoft.datawrangler.viewer.v0+json": {"columns": [{"name": "index", "rawType": "int64", "type": "integer"}, {"name": "prompt", "rawType": "object", "type": "string"}, {"name": "index", "rawType": "int64", "type": "integer"}, {"name": "llm_score", "rawType": "float64", "type": "float"}, {"name": "top_level_keys_present", "rawType": "float64", "type": "float"}, {"name": "num_nodes", "rawType": "float64", "type": "float"}, {"name": "num_nodes_with_valid_type", "rawType": "float64", "type": "float"}, {"name": "num_nodes_with_valid_version", "rawType": "float64", "type": "float"}, {"name": "num_nodes_with_valid_structure", "rawType": "float64", "type": "float"}, {"name": "num_nodes_with_parameters", "rawType": "float64", "type": "float"}, {"name": "num_connections", "rawType": "float64", "type": "float"}, {"name": "num_connections_with_valid_structure", "rawType": "float64", "type": "float"}, {"name": "num_connections_with_valid_target_node", "rawType": "float64", "type": "float"}, {"name": "percent_node_with_valid_type", "rawType": "float64", "type": "float"}, {"name": "percent_node_with_valid_version", "rawType": "float64", "type": "float"}, {"name": "percent_node_with_valid_structure", "rawType": "float64", "type": "float"}, {"name": "percent_connections_with_valid_structure", "rawType": "float64", "type": "float"}, {"name": "percent_connections_with_valid_target_node", "rawType": "float64", "type": "float"}, {"name": "percent_node_with_parameters", "rawType": "float64", "type": "float"}, {"name": "difficulty", "rawType": "object", "type": "string"}], "ref": "b530abc7-dc5e-4e62-bb5d-c3f2513ca<PERSON>", "rows": [["0", "Create an n8n workflow named 'Multiple trigger node rerun' that can be triggered manually or on a minute-by-minute schedule. Both triggers should initiate an HTTP request to `https://internal.users.n8n.cloud/webhook/random-data-api` to fetch random users, then use a code node to process the data by reversing the 'firstname' field.", "8", "9.25", "4.0", "4.25", "4.25", "4.25", "4.25", "4.25", "3.25", "3.25", "3.25", "100.0", "100.0", "100.0", "100.0", "100.0", "100.0", "medium"], ["1", "Create an n8n workflow that triggers on a web form submission titled \"Newsletter de UDIA\", collecting a user's name, email, and privacy consent. Upon submission, it should append or update the collected name and email to \"Hoja 1\" in the specified Google Sheet, matching existing entries by email.", "24", "6.75", "4.0", "5.0", "5.0", "5.0", "5.0", "5.0", "4.0", "4.0", "4.0", "100.0", "100.0", "100.0", "100.0", "100.0", "100.0", "medium"], ["2", "Generate a JSON object for an inactive workflow named '+11 AI-Powered RAG Workflow For Stock Earnings Report Analysis', with `settings.executionOrder` set to 'v1', empty `nodes` and `connections`, and a `triggerCount` of 0.", "20", "10.0", "4.0", "0.0", "0.0", "0.0", "0.0", "0.0", "0.0", "0.0", "0.0", null, null, null, null, null, null, "easy"], ["3", "Generate a JSON object representing a n8n workflow named \"email extractor in airtable,\" including its ID, creation/update timestamps, status, and basic structure (empty nodes/connections). The workflow is currently inactive and uses execution order \"v1\".\n", "17", "10.0", "2.6666666667000003", "0.0", "0.0", "0.0", "0.0", "0.0", "0.0", "0.0", "0.0", null, null, null, null, null, null, "easy"], ["4", "Generate an active n8n workflow named \"AppTest n8n Onboarding Scaling Automation Solutions\". It should start with a Webhook node configured at `onboarding/n8n/tests/ScalingAutomationSolutions` that connects to a \"Respond to Webhook\" node. The \"Respond to Webhook\" node must return an HTML document that renders a \"Scaling Automation Solutions\" quiz, which upon submission or task completion, posts data to `https://auto.crm-s.com/webhook/Onboarding/Update` and `https://auto.crm-s.com/webhook/Onboarding/UpdateTaskTable` respectively. The workflow should also be tagged with \"n8n\", \"Tests\", \"Day5\", and \"Onboarding\".", "1", "8.0", "4.0", "2.5", "1.75", "1.75", "2.5", "2.5", "1.5", "1.5", "1.5", "75.0", "75.0", "100.0", "100.0", "100.0", "100.0", "easy"]], "shape": {"columns": 19, "rows": 5}}, "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>prompt</th>\n", "      <th>index</th>\n", "      <th>llm_score</th>\n", "      <th>top_level_keys_present</th>\n", "      <th>num_nodes</th>\n", "      <th>num_nodes_with_valid_type</th>\n", "      <th>num_nodes_with_valid_version</th>\n", "      <th>num_nodes_with_valid_structure</th>\n", "      <th>num_nodes_with_parameters</th>\n", "      <th>num_connections</th>\n", "      <th>num_connections_with_valid_structure</th>\n", "      <th>num_connections_with_valid_target_node</th>\n", "      <th>percent_node_with_valid_type</th>\n", "      <th>percent_node_with_valid_version</th>\n", "      <th>percent_node_with_valid_structure</th>\n", "      <th>percent_connections_with_valid_structure</th>\n", "      <th>percent_connections_with_valid_target_node</th>\n", "      <th>percent_node_with_parameters</th>\n", "      <th>difficulty</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>Create an n8n workflow named 'Multiple trigger...</td>\n", "      <td>8</td>\n", "      <td>9.25</td>\n", "      <td>4.000000</td>\n", "      <td>4.25</td>\n", "      <td>4.25</td>\n", "      <td>4.25</td>\n", "      <td>4.25</td>\n", "      <td>4.25</td>\n", "      <td>3.25</td>\n", "      <td>3.25</td>\n", "      <td>3.25</td>\n", "      <td>100.0</td>\n", "      <td>100.0</td>\n", "      <td>100.0</td>\n", "      <td>100.0</td>\n", "      <td>100.0</td>\n", "      <td>100.0</td>\n", "      <td>medium</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>Create an n8n workflow that triggers on a web ...</td>\n", "      <td>24</td>\n", "      <td>6.75</td>\n", "      <td>4.000000</td>\n", "      <td>5.00</td>\n", "      <td>5.00</td>\n", "      <td>5.00</td>\n", "      <td>5.00</td>\n", "      <td>5.00</td>\n", "      <td>4.00</td>\n", "      <td>4.00</td>\n", "      <td>4.00</td>\n", "      <td>100.0</td>\n", "      <td>100.0</td>\n", "      <td>100.0</td>\n", "      <td>100.0</td>\n", "      <td>100.0</td>\n", "      <td>100.0</td>\n", "      <td>medium</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>Generate a JSON object for an inactive workflo...</td>\n", "      <td>20</td>\n", "      <td>10.00</td>\n", "      <td>4.000000</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>easy</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>Generate a JSON object representing a n8n work...</td>\n", "      <td>17</td>\n", "      <td>10.00</td>\n", "      <td>2.666667</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>easy</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>Generate an active n8n workflow named \"AppTest...</td>\n", "      <td>1</td>\n", "      <td>8.00</td>\n", "      <td>4.000000</td>\n", "      <td>2.50</td>\n", "      <td>1.75</td>\n", "      <td>1.75</td>\n", "      <td>2.50</td>\n", "      <td>2.50</td>\n", "      <td>1.50</td>\n", "      <td>1.50</td>\n", "      <td>1.50</td>\n", "      <td>75.0</td>\n", "      <td>75.0</td>\n", "      <td>100.0</td>\n", "      <td>100.0</td>\n", "      <td>100.0</td>\n", "      <td>100.0</td>\n", "      <td>easy</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                                              prompt  index  llm_score  \\\n", "0  Create an n8n workflow named 'Multiple trigger...      8       9.25   \n", "1  Create an n8n workflow that triggers on a web ...     24       6.75   \n", "2  Generate a JSON object for an inactive workflo...     20      10.00   \n", "3  Generate a JSON object representing a n8n work...     17      10.00   \n", "4  Generate an active n8n workflow named \"AppTest...      1       8.00   \n", "\n", "   top_level_keys_present  num_nodes  num_nodes_with_valid_type  \\\n", "0                4.000000       4.25                       4.25   \n", "1                4.000000       5.00                       5.00   \n", "2                4.000000       0.00                       0.00   \n", "3                2.666667       0.00                       0.00   \n", "4                4.000000       2.50                       1.75   \n", "\n", "   num_nodes_with_valid_version  num_nodes_with_valid_structure  \\\n", "0                          4.25                            4.25   \n", "1                          5.00                            5.00   \n", "2                          0.00                            0.00   \n", "3                          0.00                            0.00   \n", "4                          1.75                            2.50   \n", "\n", "   num_nodes_with_parameters  num_connections  \\\n", "0                       4.25             3.25   \n", "1                       5.00             4.00   \n", "2                       0.00             0.00   \n", "3                       0.00             0.00   \n", "4                       2.50             1.50   \n", "\n", "   num_connections_with_valid_structure  \\\n", "0                                  3.25   \n", "1                                  4.00   \n", "2                                  0.00   \n", "3                                  0.00   \n", "4                                  1.50   \n", "\n", "   num_connections_with_valid_target_node  percent_node_with_valid_type  \\\n", "0                                    3.25                         100.0   \n", "1                                    4.00                         100.0   \n", "2                                    0.00                           NaN   \n", "3                                    0.00                           NaN   \n", "4                                    1.50                          75.0   \n", "\n", "   percent_node_with_valid_version  percent_node_with_valid_structure  \\\n", "0                            100.0                              100.0   \n", "1                            100.0                              100.0   \n", "2                              NaN                                NaN   \n", "3                              NaN                                NaN   \n", "4                             75.0                              100.0   \n", "\n", "   percent_connections_with_valid_structure  \\\n", "0                                     100.0   \n", "1                                     100.0   \n", "2                                       NaN   \n", "3                                       NaN   \n", "4                                     100.0   \n", "\n", "   percent_connections_with_valid_target_node  percent_node_with_parameters  \\\n", "0                                       100.0                         100.0   \n", "1                                       100.0                         100.0   \n", "2                                         NaN                           NaN   \n", "3                                         NaN                           NaN   \n", "4                                       100.0                         100.0   \n", "\n", "  difficulty  \n", "0     medium  \n", "1     medium  \n", "2       easy  \n", "3       easy  \n", "4       easy  "]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["df.head()"]}, {"cell_type": "markdown", "id": "e459bb7b", "metadata": {}, "source": ["## Overall"]}, {"cell_type": "code", "execution_count": 4, "id": "aeaeeef2", "metadata": {}, "outputs": [{"data": {"text/plain": ["<Axes: xlabel='llm_score', ylabel='Count'>"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["sns.histplot(df, x=\"llm_score\", bins=20)"]}, {"cell_type": "code", "execution_count": 5, "id": "8825d978", "metadata": {}, "outputs": [{"data": {"text/plain": ["np.float64(0.8888888888888888)"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["(df[\"llm_score\"]>=6).sum()/len(df)"]}, {"cell_type": "code", "execution_count": 6, "id": "c1ecdd0c", "metadata": {}, "outputs": [{"data": {"text/plain": ["<Axes: xlabel='top_level_keys_present', ylabel='Count'>"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["sns.histplot(df, x=\"top_level_keys_present\")"]}, {"cell_type": "code", "execution_count": 7, "id": "a18db11e", "metadata": {}, "outputs": [{"data": {"text/plain": ["<Axes: xlabel='percent_node_with_valid_type', ylabel='Count'>"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["sns.histplot(df, x=\"percent_node_with_valid_type\")"]}, {"cell_type": "code", "execution_count": 8, "id": "3c7c398e", "metadata": {}, "outputs": [{"data": {"text/plain": ["<Axes: xlabel='percent_node_with_valid_version', ylabel='Count'>"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["sns.histplot(df, x=\"percent_node_with_valid_version\")"]}, {"cell_type": "code", "execution_count": 9, "id": "4c46bdcf", "metadata": {}, "outputs": [{"data": {"text/plain": ["<Axes: xlabel='percent_node_with_valid_structure', ylabel='Count'>"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["sns.histplot(df, x=\"percent_node_with_valid_structure\")"]}, {"cell_type": "code", "execution_count": 10, "id": "44883d67", "metadata": {}, "outputs": [{"data": {"text/plain": ["<Axes: xlabel='percent_node_with_parameters', ylabel='Count'>"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAioAAAGxCAYAAABMeZ2uAAAAOnRFWHRTb2Z0d2FyZQBNYXRwbG90bGliIHZlcnNpb24zLjEwLjAsIGh0dHBzOi8vbWF0cGxvdGxpYi5vcmcvlHJYcgAAAAlwSFlzAAAPYQAAD2EBqD+naQAALZ5JREFUeJzt3Xl0VFW+9vGnzFAEQsKYkEhIGGSSeVIGmQmi0qCiF7UR2lYbDZPYSqeBBrQlaDdIi4oXr6LtQOyLgFwuggENIAhCIDKIYSZpBiMIJGGoQLLfP3xTl4KEISQ5G/l+1qq1PPvss89v75hVD+ecSrmMMUYAAAAWusnpAgAAAIpCUAEAANYiqAAAAGsRVAAAgLUIKgAAwFoEFQAAYC2CCgAAsBZBBQAAWMvf6QKuRX5+vg4ePKiKFSvK5XI5XQ4AALgCxhhlZ2crMjJSN9106Wsm13VQOXjwoKKiopwuAwAAFENGRoZq1qx5yT7XdVCpWLGipF8mGhIS4nA1AADgSmRlZSkqKsr7Pn4p13VQKbjdExISQlABAOA6cyWPbfAwLQAAsBZBBQAAWIugAgAArEVQAQAA1iKoAAAAaxFUAACAtQgqAADAWgQVAABgLYIKAACwFkEFAABYy9GgEhMTI5fLddErLi7OybIAAIAlHP2un/Xr1ysvL8+7vXXrVvXq1UsPPPCAg1UBAABbOBpUqlev7rM9ZcoU1a1bV126dHGoIgAAYBNrnlHJzc3Vhx9+qMcee+yKvk0RAAD8+jl6ReV8CxYs0PHjxzVkyJAi+3g8Hnk8Hu92VlZWqdaUnp6uI0eOlOo5AACwWbVq1VSrVi3Hzm9NUHnnnXfUp08fRUZGFtknISFBkyZNKpN60tPT1bBhI50+fapMzgcAgI2Cgsrrhx+2OxZWrAgq+/fv17JlyzRv3rxL9ouPj9fo0aO921lZWYqKiiqVmo4cOaLTp0/ptscmKCQiplTOAQCAzbIO7dO6dyfpyJEjN3ZQmT17tsLCwnT33Xdfsp/b7Zbb7S6jqn4REhGjKrUalOk5AQDALxx/mDY/P1+zZ8/W4MGD5e9vRW4CAACWcDyoLFu2TOnp6XrsscecLgUAAFjG8UsYsbGxMsY4XQYAALCQ41dUAAAAikJQAQAA1iKoAAAAaxFUAACAtQgqAADAWgQVAABgLYIKAACwFkEFAABYi6ACAACsRVABAADWIqgAAABrEVQAAIC1CCoAAMBaBBUAAGAtggoAALAWQQUAAFiLoAIAAKxFUAEAANYiqAAAAGsRVAAAgLUIKgAAwFoEFQAAYC2CCgAAsBZBBQAAWIugAgAArEVQAQAA1iKoAAAAaxFUAACAtQgqAADAWgQVAABgLYIKAACwFkEFAABYi6ACAACsRVABAADWIqgAAABrEVQAAIC1CCoAAMBaBBUAAGAtggoAALCW40HlwIED+u1vf6uqVauqfPnyatGihVJSUpwuCwAAWMDfyZMfO3ZMHTt2VLdu3fT5558rLCxMu3fvVqVKlZwsCwAAWMLRoPLyyy8rKipKs2fP9rbFxMQ4VxAAALCKo7d+Fi5cqDZt2uiBBx5QWFiYWrZsqbffftvJkgAAgEUcDSp79uzRzJkzdcstt2jp0qUaOnSoRowYoX/+85+F9vd4PMrKyvJ5AQCAXy9Hb/3k5+erTZs2mjx5siSpZcuW2rZtm2bOnKlHH330ov4JCQmaNGlSWZcJAAAc4ugVlYiICDVu3NinrVGjRkpPTy+0f3x8vE6cOOF9ZWRklEWZAADAIY5eUenYsaPS0tJ82nbs2KHo6OhC+7vdbrnd7rIoDQAAWMDRKyrPPPOM1q5dq8mTJ2vXrl36+OOPNWvWLMXFxTlZFgAAsISjQaVt27aaP3++5syZoyZNmujFF1/U9OnT9cgjjzhZFgAAsISjt34k6Z577tE999zjdBkAAMBCjv8JfQAAgKIQVAAAgLUIKgAAwFoEFQAAYC2CCgAAsBZBBQAAWIugAgAArEVQAQAA1iKoAAAAaxFUAACAtQgqAADAWgQVAABgLYIKAACwFkEFAABYi6ACAACsRVABAADWIqgAAABrEVQAAIC1CCoAAMBaBBUAAGAtggoAALAWQQUAAFiLoAIAAKxFUAEAANYiqAAAAGsRVAAAgLUIKgAAwFoEFQAAYC2CCgAAsBZBBQAAWIugAgAArEVQAQAA1iKoAAAAaxFUAACAtQgqAADAWgQVAABgLYIKAACwFkEFAABYi6ACAACsRVABAADWcjSoTJw4US6Xy+dVo0YNJ0sCAAAW8Xe6gFtvvVXLli3zbvv5+TlYDQAAsInjQcXf35+rKAAAoFCOP6Oyc+dORUZGqnbt2ho4cKD27NlTZF+Px6OsrCyfFwAA+PVyNKjcdttt+uc//6mlS5fq7bff1uHDh9WhQwcdPXq00P4JCQkKDQ31vqKiosq4YgAAUJYcDSp9+vTR/fffr6ZNm6pnz5763//9X0nS+++/X2j/+Ph4nThxwvvKyMgoy3IBAEAZc/wZlfNVqFBBTZs21c6dOwvd73a75Xa7y7gqAADgFMefUTmfx+PR9u3bFRER4XQpAADAAo4GlT/+8Y9asWKF9u7dq3Xr1mnAgAHKysrS4MGDnSwLAABYwtFbP//+97/10EMP6ciRI6pevbpuv/12rV27VtHR0U6WBQAALOFoUElMTHTy9AAAwHJWPaMCAABwPoIKAACwFkEFAABYi6ACAACsRVABAADWIqgAAABrEVQAAIC1CCoAAMBaBBUAAGAtggoAALAWQQUAAFiLoAIAAKxFUAEAANYiqAAAAGsRVAAAgLUIKgAAwFoEFQAAYC2CCgAAsBZBBQAAWIugAgAArEVQAQAA1iKoAAAAaxFUAACAtQgqAADAWgQVAABgLYIKAACwFkEFAABYi6ACAACsRVABAADWIqgAAABrEVQAAIC1CCoAAMBaBBUAAGAtggoAALAWQQUAAFiLoAIAAKxFUAEAANYiqAAAAGsRVAAAgLWsCSoJCQlyuVwaNWqU06UAAABLWBFU1q9fr1mzZqlZs2ZOlwIAACxSrKBSp04dHT169KL248ePq06dOlc1Vk5Ojh555BG9/fbbqly5cnHKAQAAv1LFCir79u1TXl7eRe0ej0cHDhy4qrHi4uJ09913q2fPnpft6/F4lJWV5fMCAAC/Xv5X03nhwoXe/166dKlCQ0O923l5eVq+fLliYmKueLzExERt3LhR69evv6L+CQkJmjRp0hWPDwAArm9XFVT69+8vSXK5XBo8eLDPvoCAAMXExGjq1KlXNFZGRoZGjhypL774QuXKlbuiY+Lj4zV69GjvdlZWlqKioq6seAAAcN25qqCSn58vSapdu7bWr1+vatWqFfvEKSkpyszMVOvWrb1teXl5WrlypV5//XV5PB75+fn5HON2u+V2u4t9TgAAcH25qqBSYO/evdd84h49emjLli0+bb/73e/UsGFDjRkz5qKQAgAAbjzFCiqStHz5ci1fvlyZmZneKy0F3n333cseX7FiRTVp0sSnrUKFCqpatepF7QAA4MZUrKAyadIkvfDCC2rTpo0iIiLkcrlKui4AAIDiBZW33npL7733ngYNGlSixSQnJ5foeAAA4PpWrL+jkpubqw4dOpR0LQAAAD6KFVQef/xxffzxxyVdCwAAgI9i3fo5c+aMZs2apWXLlqlZs2YKCAjw2T9t2rQSKQ4AANzYihVUNm/erBYtWkiStm7d6rOPB2sBAEBJKVZQ+eqrr0q6DgAAgIsU6xkVAACAslCsKyrdunW75C2eL7/8stgFAQAAFChWUCl4PqXA2bNnlZqaqq1bt170ZYUAAADFVayg8uqrrxbaPnHiROXk5FxTQQAAAAVK9BmV3/72t1f0PT8AAABXokSDyjfffKNy5cqV5JAAAOAGVqxbP/fdd5/PtjFGhw4d0oYNGzR+/PgSKQwAAKBYQSU0NNRn+6abblKDBg30wgsvKDY2tkQKAwAAKFZQmT17dknXAQAAcJFiBZUCKSkp2r59u1wulxo3bqyWLVuWVF0AAADFCyqZmZkaOHCgkpOTValSJRljdOLECXXr1k2JiYmqXr16SdcJAABuQMX61M/w4cOVlZWlbdu26eeff9axY8e0detWZWVlacSIESVdIwAAuEEV64rKkiVLtGzZMjVq1Mjb1rhxY73xxhs8TAsAAEpMsa6o5OfnKyAg4KL2gIAA5efnX3NRAAAAUjGDSvfu3TVy5EgdPHjQ23bgwAE988wz6tGjR4kVBwAAbmzFCiqvv/66srOzFRMTo7p166pevXqqXbu2srOzNWPGjJKuEQAA3KCK9YxKVFSUNm7cqKSkJP3www8yxqhx48bq2bNnSdcHAABuYFd1ReXLL79U48aNlZWVJUnq1auXhg8frhEjRqht27a69dZbtWrVqlIpFAAA3HiuKqhMnz5dTzzxhEJCQi7aFxoaqj/84Q+aNm1aiRUHAABubFcVVL777jvdeeedRe6PjY1VSkrKNRcFAAAgXWVQ+fHHHwv9WHIBf39//fTTT9dcFAAAgHSVQeXmm2/Wli1bity/efNmRUREXHNRAAAA0lUGlbvuukt/+ctfdObMmYv2nT59WhMmTNA999xTYsUBAIAb21V9PHncuHGaN2+e6tevr2HDhqlBgwZyuVzavn273njjDeXl5Wns2LGlVSsAALjBXFVQCQ8P15o1a/TUU08pPj5exhhJksvlUu/evfXmm28qPDy8VAoFAAA3nqv+g2/R0dFavHixjh07pl27dskYo1tuuUWVK1cujfoAAMANrFh/mVaSKleurLZt25ZkLQAAAD6K9V0/AAAAZYGgAgAArEVQAQAA1iKoAAAAaxFUAACAtQgqAADAWgQVAABgLUeDysyZM9WsWTOFhIQoJCRE7du31+eff+5kSQAAwCKOBpWaNWtqypQp2rBhgzZs2KDu3burX79+2rZtm5NlAQAASxT7L9OWhL59+/psv/TSS5o5c6bWrl2rW2+91aGqAACALRwNKufLy8vTf//3f+vkyZNq37690+UAAAALOB5UtmzZovbt2+vMmTMKDg7W/Pnz1bhx40L7ejweeTwe73ZWVlZZlQkAABzg+Kd+GjRooNTUVK1du1ZPPfWUBg8erO+//77QvgkJCQoNDfW+oqKiyrhaAABQlhwPKoGBgapXr57atGmjhIQENW/eXP/4xz8K7RsfH68TJ054XxkZGWVcLQAAKEuO3/q5kDHG5/bO+dxut9xudxlXBAAAnOJoUPnzn/+sPn36KCoqStnZ2UpMTFRycrKWLFniZFkAAMASjgaVH3/8UYMGDdKhQ4cUGhqqZs2aacmSJerVq5eTZQEAAEs4GlTeeecdJ08PAAAs5/jDtAAAAEUhqAAAAGsRVAAAgLUIKgAAwFoEFQAAYC2CCgAAsBZBBQAAWIugAgAArEVQAQAA1iKoAAAAaxFUAACAtQgqAADAWgQVAABgLYIKAACwFkEFAABYi6ACAACsRVABAADWIqgAAABrEVQAAIC1CCoAAMBaBBUAAGAtggoAALAWQQUAAFiLoAIAAKxFUAEAANYiqAAAAGsRVAAAgLUIKgAAwFoEFQAAYC2CCgAAsBZBBQAAWIugAgAArEVQAQAA1iKoAAAAaxFUAACAtQgqAADAWgQVAABgLYIKAACwFkEFAABYi6ACAACs5WhQSUhIUNu2bVWxYkWFhYWpf//+SktLc7IkAABgEUeDyooVKxQXF6e1a9cqKSlJ586dU2xsrE6ePOlkWQAAwBL+Tp58yZIlPtuzZ89WWFiYUlJS1LlzZ4eqAgAAtnA0qFzoxIkTkqQqVaoUut/j8cjj8Xi3s7KyyqQuAADgDGsepjXGaPTo0erUqZOaNGlSaJ+EhASFhoZ6X1FRUWVcJQAAKEvWBJVhw4Zp8+bNmjNnTpF94uPjdeLECe8rIyOjDCsEAABlzYpbP8OHD9fChQu1cuVK1axZs8h+brdbbre7DCsDAABOcjSoGGM0fPhwzZ8/X8nJyapdu7aT5QAAAMs4GlTi4uL08ccf67PPPlPFihV1+PBhSVJoaKiCgoKcLA0AAFjA0WdUZs6cqRMnTqhr166KiIjwvj755BMnywIAAJZw/NYPAABAUaz51A8AAMCFCCoAAMBaBBUAAGAtggoAALAWQQUAAFiLoAIAAKxFUAEAANYiqAAAAGsRVAAAgLUIKgAAwFoEFQAAYC2CCgAAsBZBBQAAWIugAgAArEVQAQAA1iKoAAAAaxFUAACAtQgqAADAWgQVAABgLYIKAACwFkEFAABYi6ACAACsRVABAADWIqgAAABrEVQAAIC1CCoAAMBaBBUAAGAtggoAALAWQQUAAFiLoAIAAKxFUAEAANYiqAAAAGsRVAAAgLUIKgAAwFoEFQAAYC2CCgAAsBZBBQAAWIugAgAArEVQAQAA1nI0qKxcuVJ9+/ZVZGSkXC6XFixY4GQ5AADAMo4GlZMnT6p58+Z6/fXXnSwDAABYyt/Jk/fp00d9+vRxsgQAAGAxR4PK1fJ4PPJ4PN7trKwsB6sBAACl7bp6mDYhIUGhoaHeV1RUlNMlAQCAUnRdBZX4+HidOHHC+8rIyHC6JAAAUIquq1s/brdbbrfb6TIAAEAZua6uqAAAgBuLo1dUcnJytGvXLu/23r17lZqaqipVqqhWrVoOVgYAAGzgaFDZsGGDunXr5t0ePXq0JGnw4MF67733HKoKAADYwtGg0rVrVxljnCwBAABYjGdUAACAtQgqAADAWgQVAABgLYIKAACwFkEFAABYi6ACAACsRVABAADWIqgAAABrEVQAAIC1CCoAAMBaBBUAAGAtggoAALAWQQUAAFiLoAIAAKxFUAEAANYiqAAAAGsRVAAAgLUIKgAAwFoEFQAAYC2CCgAAsBZBBQAAWIugAgAArEVQAQAA1iKoAAAAaxFUAACAtQgqAADAWgQVAABgLYIKAACwFkEFAABYi6ACAACsRVABAADWIqgAAABrEVQAAIC1CCoAAMBaBBUAAGAtggoAALAWQQUAAFiLoAIAAKxFUAEAANZyPKi8+eabql27tsqVK6fWrVtr1apVTpcEAAAs4WhQ+eSTTzRq1CiNHTtWmzZt0h133KE+ffooPT3dybIAAIAlHA0q06ZN0+9//3s9/vjjatSokaZPn66oqCjNnDnTybIAAIAlHAsqubm5SklJUWxsrE97bGys1qxZ41BVAADAJv5OnfjIkSPKy8tTeHi4T3t4eLgOHz5c6DEej0cej8e7feLECUlSVlZWideXk5MjSfp5f5rOeU6X+PgAANgu6/Avj2Lk5OSU6HttwVjGmMv2dSyoFHC5XD7bxpiL2gokJCRo0qRJF7VHRUWVSm2SlPLhlFIbGwCA60GXLl1KZdzs7GyFhoZeso9jQaVatWry8/O76OpJZmbmRVdZCsTHx2v06NHe7fz8fP3888+qWrVqkeHmRpKVlaWoqChlZGQoJCTE6XJ+tVjnssE6lw3Wueyw1v/HGKPs7GxFRkZetq9jQSUwMFCtW7dWUlKS7r33Xm97UlKS+vXrV+gxbrdbbrfbp61SpUqlWeZ1KSQk5Ib/JSgLrHPZYJ3LButcdljrX1zuSkoBR2/9jB49WoMGDVKbNm3Uvn17zZo1S+np6Ro6dKiTZQEAAEs4GlT+4z/+Q0ePHtULL7ygQ4cOqUmTJlq8eLGio6OdLAsAAFjC8Ydpn376aT399NNOl/Gr4Ha7NWHChItuj6Fksc5lg3UuG6xz2WGti8dlruSzQQAAAA5w/Lt+AAAAikJQAQAA1iKoAAAAaxFULJWdna1Ro0YpOjpaQUFB6tChg9avX+/d/+OPP2rIkCGKjIxU+fLldeedd2rnzp2XHff48eOKi4tTRESEypUrp0aNGmnx4sWlORWrldY6T58+XQ0aNFBQUJCioqL0zDPP6MyZM6U5FausXLlSffv2VWRkpFwulxYsWOCz3xijiRMnKjIyUkFBQeratau2bdvm08fj8Wj48OGqVq2aKlSooN/85jf697//fdlzv/nmm6pdu7bKlSun1q1ba9WqVSU5Nas4tc4JCQlq27atKlasqLCwMPXv319paWklPT1rOPn/c4GEhAS5XC6NGjWqBGZ0fSGoWOrxxx9XUlKSPvjgA23ZskWxsbHq2bOnDhw4IGOM+vfvrz179uizzz7Tpk2bFB0drZ49e+rkyZNFjpmbm6tevXpp3759mjt3rtLS0vT222/r5ptvLsOZ2aU01vmjjz7Sn/70J02YMEHbt2/XO++8o08++UTx8fFlODNnnTx5Us2bN9frr79e6P5XXnlF06ZN0+uvv67169erRo0a6tWrl7Kzs719Ro0apfnz5ysxMVFff/21cnJydM899ygvL6/I837yyScaNWqUxo4dq02bNumOO+5Qnz59lJ6eXuJztIFT67xixQrFxcVp7dq1SkpK0rlz5xQbG3vJ34vrmVPrXGD9+vWaNWuWmjVrVmJzuq4YWOfUqVPGz8/PLFq0yKe9efPmZuzYsSYtLc1IMlu3bvXuO3funKlSpYp5++23ixx35syZpk6dOiY3N7fUar+elNY6x8XFme7du/u0jR492nTq1KlkJ3CdkGTmz5/v3c7Pzzc1atQwU6ZM8badOXPGhIaGmrfeessYY8zx48dNQECASUxM9PY5cOCAuemmm8ySJUuKPFe7du3M0KFDfdoaNmxo/vSnP5XQbOxVlut8oczMTCPJrFix4tonYrmyXufs7Gxzyy23mKSkJNOlSxczcuTIEp3P9YArKhY6d+6c8vLyVK5cOZ/2oKAgff31195vkD5/v5+fnwIDA/X1118XOe7ChQvVvn17xcXFKTw8XE2aNNHkyZOvKNH/GpXWOnfq1EkpKSn69ttvJUl79uzR4sWLdffdd5fCLK4/e/fu1eHDhxUbG+ttc7vd6tKli9asWSNJSklJ0dmzZ336REZGqkmTJt4+F8rNzVVKSorPMZIUGxtb5DG/ZqW1zoUp+Cb7KlWqlFD114/SXue4uDjdfffd6tmzZ+lM4DpAULFQxYoV1b59e7344os6ePCg8vLy9OGHH2rdunU6dOiQGjZsqOjoaMXHx+vYsWPKzc3VlClTdPjwYR06dKjIcffs2aO5c+cqLy9Pixcv1rhx4zR16lS99NJLZTg7e5TWOg8cOFAvvviiOnXqpICAANWtW1fdunXTn/70pzKcnb0Kvoj0wi8fDQ8P9+47fPiwAgMDVbly5SL7XOjIkSPKy8u75Lg3ktJa5wsZYzR69Gh16tRJTZo0KYHKry+luc6JiYnauHGjEhISSrjq6wtBxVIffPCBjDG6+eab5Xa79dprr+nhhx+Wn5+fAgIC9Omnn2rHjh2qUqWKypcvr+TkZPXp00d+fn5Fjpmfn6+wsDDNmjVLrVu31sCBAzV27FjNnDmzDGdml9JY5+TkZL300kt68803tXHjRs2bN0+LFi3Siy++WIYzs9+F33hujLnst6BfSZ/ijPtrVlrrXGDYsGHavHmz5syZU+wafw1Kep0zMjI0cuRIffjhhxdd9b3REFQsVbduXa1YsUI5OTnKyMjQt99+q7Nnz6p27dqSpNatWys1NVXHjx/XoUOHtGTJEh09etS7vzARERGqX7++z5tso0aNdPjwYeXm5pb6nGxUGus8fvx4DRo0SI8//riaNm2qe++9V5MnT1ZCQoLy8/PLamrWqlGjhiRd9C/JzMxM779Ka9SoodzcXB07dqzIPheqVq2a/Pz8LjnujaS01vl8w4cP18KFC/XVV1+pZs2aJVT59aW01jklJUWZmZlq3bq1/P395e/vrxUrVui1116Tv7//DXXLnqBiuQoVKigiIkLHjh3T0qVL1a9fP5/9oaGhql69unbu3KkNGzZctP98HTt21K5du3zeLHfs2KGIiAgFBgaW2hyuByW5zqdOndJNN/n+avn5+ckYI8M3Vqh27dqqUaOGkpKSvG25ublasWKFOnToIOmXgBgQEODT59ChQ9q6dau3z4UCAwPVunVrn2MkKSkpqchjfs1Ka52lX64EDBs2TPPmzdOXX355yeD+a1da69yjRw9t2bJFqamp3lebNm30yCOPKDU19ZJXdX91HHmEF5e1ZMkS8/nnn5s9e/aYL774wjRv3ty0a9fO+4mdf/3rX+arr74yu3fvNgsWLDDR0dHmvvvu8xlj0KBBPp92SE9PN8HBwWbYsGEmLS3NLFq0yISFhZm//vWvZTo3m5TGOk+YMMFUrFjRzJkzxztu3bp1zYMPPlimc3NSdna22bRpk9m0aZORZKZNm2Y2bdpk9u/fb4wxZsqUKSY0NNTMmzfPbNmyxTz00EMmIiLCZGVleccYOnSoqVmzplm2bJnZuHGj6d69u2nevLk5d+6ct0/37t3NjBkzvNuJiYkmICDAvPPOO+b77783o0aNMhUqVDD79u0ru8mXIafW+amnnjKhoaEmOTnZHDp0yPs6depU2U2+DDm1zhe6UT/1Q1Cx1CeffGLq1KljAgMDTY0aNUxcXJw5fvy4d/8//vEPU7NmTRMQEGBq1aplxo0bZzwej88YXbp0MYMHD/ZpW7NmjbntttuM2+02derUMS+99JLPL8qNpjTW+ezZs2bixImmbt26ply5ciYqKso8/fTT5tixY2U0K+d99dVXRtJFr4J1ys/PNxMmTDA1atQwbrfbdO7c2WzZssVnjNOnT5thw4aZKlWqmKCgIHPPPfeY9PR0nz7R0dFmwoQJPm1vvPGGiY6ONoGBgaZVq1a/6o/MOrXOhZ1Tkpk9e3Ypz9gZTv7/fL4bNajw7ckAAMBaPKMCAACsRVABAADWIqgAAABrEVQAAIC1CCoAAMBaBBUAAGAtggoAALAWQQUAAFiLoALgqiQnJ8vlcun48eNlfu6JEyeqRYsWl+yzb98+uVwupaamlklNAEoXQQW4TrhcLi1YsMDpMhz1xz/+UcuXL/duDxkyRP3793euoOvIlYQ8wEYEFeAa5OXl+XwbNUpXcHCwqlat6nQZxZKbm+t0CSXi7NmzTpeAGwxBBTeUrl27atiwYRo2bJgqVaqkqlWraty4cSr4yqvc3Fw9//zzuvnmm1WhQgXddtttSk5O9h7/3nvvqVKlSlq0aJEaN24st9ut/fv3y+Px6Pnnn1dUVJTcbrduueUWvfPOO97jvv/+e911110KDg5WeHi4Bg0apCNHjvjUNWLECD3//POqUqWKatSooYkTJ3r3x8TESJLuvfdeuVwu7/alFPwL+oMPPlBMTIxCQ0M1cOBAZWdne/t4PB6NGDFCYWFhKleunDp16qT169f7jLN48WLVr19fQUFB6tatm/bt23fRudasWaPOnTsrKChIUVFRGjFihE6ePHnZGmfMmKGmTZt6txcsWCCXy6U33njD29a7d2/Fx8f7zKngv99//3199tlncrlccrlcPj+rPXv2qFu3bipfvryaN2+ub7755rL1SP/3M16wYIHq16+vcuXKqVevXsrIyPD22b17t/r166fw8HAFBwerbdu2WrZsmc84MTEx+utf/6ohQ4YoNDRUTzzxhCRpzJgxql+/vsqXL686depo/PjxPm/+BXN89913VatWLQUHB+upp55SXl6eXnnlFdWoUUNhYWF66aWXfM534sQJPfnkkwoLC1NISIi6d++u7777zjunSZMm6bvvvvOu1XvvvXfZ4y6sp06dOnK73TLGaO7cuWratKmCgoJUtWpV9ezZ84p+5sBVc/Y7EYGy1aVLFxMcHGxGjhxpfvjhB/Phhx+a8uXLm1mzZhljjHn44YdNhw4dzMqVK82uXbvM3/72N+N2u82OHTuMMcbMnj3bBAQEmA4dOpjVq1ebH374weTk5JgHH3zQREVFmXnz5pndu3ebZcuWmcTERGOMMQcPHjTVqlUz8fHxZvv27Wbjxo2mV69eplu3bj51hYSEmIkTJ5odO3aY999/37hcLvPFF18YY4zJzMz0fjvtoUOHTGZm5mXnOmHCBBMcHGzuu+8+s2XLFrNy5UpTo0YN8+c//9nbZ8SIESYyMtIsXrzYbNu2zQwePNhUrlzZHD161BhjTHp6unG73T7rFR4ebiR5vw168+bNJjg42Lz66qtmx44dZvXq1aZly5ZmyJAhl61x8+bNxuVymZ9++skYY8yoUaNMtWrVzAMPPGCM+eWbqIODg83nn3/unVPz5s2NMcZkZ2ebBx980Nx5553m0KFD5tChQ8bj8Zi9e/caSaZhw4Zm0aJFJi0tzQwYMMBER0ebs2fPXramgp9xmzZtzJo1a8yGDRtMu3btTIcOHbx9UlNTzVtvvWU2b95sduzYYcaOHWvKlStn9u/f7+0THR1tQkJCzN/+9jezc+dOs3PnTmOMMS+++KJZvXq12bt3r1m4cKEJDw83L7/88kU/twEDBpht27aZhQsXmsDAQNO7d28zfPhw88MPP5h3333XSDLffPONMeaXb+/t2LGj6du3r1m/fr3ZsWOHefbZZ03VqlXN0aNHzalTp8yzzz5rbr31Vu9anTp16rLHFdRToUIF07t3b7Nx40bz3XffmYMHDxp/f38zbdo0s3fvXrN582bzxhtvmOzs7MuuL3C1CCq4oXTp0sU0atTI5Ofne9vGjBljGjVqZHbt2mVcLpc5cOCAzzE9evQw8fHxxphf3sQkmdTUVO/+tLQ0I8kkJSUVes7x48eb2NhYn7aMjAwjyaSlpXnr6tSpk0+ftm3bmjFjxni3JZn58+df8VwnTJhgypcvb7Kysrxtzz33nLntttuMMcbk5OSYgIAA89FHH3n35+bmmsjISPPKK68YY4yJj48vdL3ODyqDBg0yTz75pM+5V61aZW666SZz+vTpS9aYn59vqlWrZubOnWuMMaZFixYmISHBhIWFGWOMWbNmjfH39/e+AZ4fVIwxZvDgwaZfv34+YxYElf/6r//ytm3bts1IMtu3b79kPcb838947dq13rbt27cbSWbdunVFHte4cWMzY8YM73Z0dLTp37//Zc/3yiuvmNatW3u3C/u59e7d28TExJi8vDxvW4MGDUxCQoIxxpjly5ebkJAQc+bMGZ+x69ata/7zP//TO+75a3c1xwUEBPiE45SUFCPJ7Nu377LzA66VvxNXcQAn3X777XK5XN7t9u3ba+rUqdqwYYOMMapfv75Pf4/H4/NcRGBgoJo1a+bdTk1NlZ+fn7p06VLo+VJSUvTVV18pODj4on27d+/2nu/8MSUpIiJCmZmZVz/B88TExKhixYqFjrl7926dPXtWHTt29O4PCAhQu3bttH37dknS9u3bC12vC+e3a9cuffTRR942Y4zy8/O1d+9eNWrUqMj6XC6XOnfurOTkZPXo0UPbtm3T0KFD9fe//13bt29XcnKyWrVqVejaXc756xkRESFJyszMVMOGDS97rL+/v9q0aePdbtiwoSpVqqTt27erXbt2OnnypCZNmqRFixbp4MGDOnfunE6fPq309HSfcc4fo8DcuXM1ffp07dq1Szk5OTp37pxCQkJ8+lz4cwsPD5efn59uuukmn7aCn2VKSopycnIuen7n9OnT2r17d5HzvNLjoqOjVb16de928+bN1aNHDzVt2lS9e/dWbGysBgwYoMqVKxd5LqC4CCrAefz8/JSSkiI/Pz+f9vPfKIOCgnzeuIOCgi45Zn5+vvr27auXX375on0Fb6DSLyHhfC6X65of1L3UmOb/P5dz/lwK2gvaCvpcSn5+vv7whz9oxIgRF+2rVavWZY/v2rWrZs2apVWrVql58+aqVKmSOnfurBUrVig5OVldu3a97BiFOX/uBfO5mvW8cF3Ob3vuuee0dOlS/f3vf1e9evUUFBSkAQMGXPTAbIUKFXy2165dq4EDB2rSpEnq3bu3QkNDlZiYqKlTpxZZe8F5L/WzzM/PV0REhM8zOgUqVapU5Byv9LgL5+Hn56ekpCStWbNGX3zxhWbMmKGxY8dq3bp1ql27dpHnA4qDoIIbztq1ay/avuWWW9SyZUvl5eUpMzNTd9xxxxWP17RpU+Xn52vFihXq2bPnRftbtWqlTz/9VDExMfL3L/6vXEBAgPLy8op9/IXq1aunwMBAff3113r44Ycl/fKJjg0bNmjUqFGSpMaNG1/0kegL169Vq1batm2b6tWrV6w6unbtqpEjR2ru3LneUNKlSxctW7ZMa9as0ciRI4s8NjAwsETXpMC5c+e0YcMGtWvXTpKUlpam48ePe6/GrFq1SkOGDNG9994rScrJySn0IeMLrV69WtHR0Ro7dqy3bf/+/ddcb6tWrXT48GH5+/sX+aB1YWt1JccVxeVyqWPHjurYsaP+8pe/KDo6WvPnz9fo0aOLOQugcHzqBzecjIwMjR49WmlpaZozZ45mzJihkSNHqn79+nrkkUf06KOPat68edq7d6/Wr1+vl19+WYsXLy5yvJiYGA0ePFiPPfaYFixYoL179yo5OVn/+te/JElxcXH6+eef9dBDD+nbb7/Vnj179MUXX+ixxx67qjfZmJgYLV++XIcPH9axY8eueR0qVKigp556Ss8995yWLFmi77//Xk888YROnTql3//+95KkoUOHavfu3d71+vjjj72fFikwZswYffPNN4qLi1Nqaqp27typhQsXavjw4VdUR5MmTVS1alV99NFH3qDStWtXLViwQKdPn1anTp2KPDYmJkabN29WWlqajhw5UmIfnQ0ICNDw4cO1bt06bdy4Ub/73e90++23e4NLvXr1NG/ePKWmpuq7777Tww8/fEVXa+rVq6f09HQlJiZq9+7deu211zR//vxrrrdnz55q3769+vfvr6VLl2rfvn1as2aNxo0bpw0bNkj6Za327t2r1NRUHTlyRB6P54qOK8y6des0efJkbdiwQenp6Zo3b55++umnS97mA4qLoIIbzqOPPqrTp0+rXbt2iouL0/Dhw/Xkk09KkmbPnq1HH31Uzz77rBo0aKDf/OY3WrdunaKioi455syZMzVgwAA9/fTTatiwoZ544gnvRzUjIyO1evVq5eXlqXfv3mrSpIlGjhyp0NBQn2cOLmfq1KlKSkpSVFSUWrZsWfwFOM+UKVN0//33a9CgQWrVqpV27dqlpUuXep81qFWrlj799FP9z//8j5o3b6633npLkydP9hmjWbNmWrFihXbu3Kk77rhDLVu21Pjx431ua12Ky+XyPt9TcCWrWbNmCg0NVcuWLS96fuN8TzzxhBo0aKA2bdqoevXqWr16dXGW4SLly5fXmDFj9PDDD6t9+/YKCgpSYmKid/+rr76qypUrq0OHDurbt6969+6tVq1aXXbcfv366ZlnntGwYcPUokULrVmzRuPHj7/mel0ulxYvXqzOnTvrscceU/369TVw4EDt27dP4eHhkqT7779fd955p7p166bq1atrzpw5V3RcYUJCQrRy5Urdddddql+/vsaNG6epU6eqT58+1zwX4EIucyU3oYFfia5du6pFixaaPn2606XAUu+9955GjRrlyFcEALgYV1QAAIC1CCrAderWW29VcHBwoa/zPyrspFWrVhVZY3E+clwS+vTpU2Q9F97WAuA8bv0A16n9+/cX+fBoeHi4z9/hcMrp06d14MCBIvcX95NC1+LAgQM6ffp0ofuqVKmiKlWqlHFFAC6FoAIAAKzFrR8AAGAtggoAALAWQQUAAFiLoAIAAKxFUAEAANYiqAAAAGsRVAAAgLUIKgAAwFr/D5aqUIuuiTf+AAAAAElFTkSuQmCC", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["sns.histplot(df, x=\"percent_node_with_parameters\")"]}, {"cell_type": "code", "execution_count": 11, "id": "048db9e6", "metadata": {}, "outputs": [{"data": {"text/plain": ["<Axes: xlabel='percent_connections_with_valid_structure', ylabel='Count'>"]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["sns.histplot(df, x=\"percent_connections_with_valid_structure\")"]}, {"cell_type": "code", "execution_count": 12, "id": "24225a83", "metadata": {}, "outputs": [{"data": {"text/plain": ["<Axes: xlabel='percent_connections_with_valid_target_node', ylabel='Count'>"]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["sns.histplot(df, x=\"percent_connections_with_valid_target_node\")"]}, {"cell_type": "code", "execution_count": 13, "id": "9bb20feb", "metadata": {}, "outputs": [], "source": ["df_prime = df"]}, {"cell_type": "markdown", "id": "a691c4b9", "metadata": {}, "source": ["## Easy workflows"]}, {"cell_type": "code", "execution_count": 14, "id": "db12a210", "metadata": {}, "outputs": [], "source": ["df = df_prime[df_prime[\"difficulty\"] == \"easy\"]"]}, {"cell_type": "code", "execution_count": 15, "id": "b99f16aa", "metadata": {}, "outputs": [{"data": {"text/plain": ["<Axes: xlabel='llm_score', ylabel='Count'>"]}, "execution_count": 15, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["sns.histplot(df, x=\"llm_score\", bins=20)"]}, {"cell_type": "code", "execution_count": 16, "id": "28199c28", "metadata": {}, "outputs": [{"data": {"text/plain": ["np.float64(1.0)"]}, "execution_count": 16, "metadata": {}, "output_type": "execute_result"}], "source": ["(df[\"llm_score\"]>=6).sum()/len(df)"]}, {"cell_type": "code", "execution_count": 17, "id": "03e96ef0", "metadata": {}, "outputs": [{"data": {"text/plain": ["<Axes: xlabel='top_level_keys_present', ylabel='Count'>"]}, "execution_count": 17, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["sns.histplot(df, x=\"top_level_keys_present\")"]}, {"cell_type": "code", "execution_count": 18, "id": "0991d8aa", "metadata": {}, "outputs": [{"data": {"text/plain": ["<Axes: xlabel='percent_node_with_valid_type', ylabel='Count'>"]}, "execution_count": 18, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["sns.histplot(df, x=\"percent_node_with_valid_type\")"]}, {"cell_type": "code", "execution_count": 19, "id": "7c475b8e", "metadata": {}, "outputs": [{"data": {"text/plain": ["<Axes: xlabel='percent_node_with_valid_version', ylabel='Count'>"]}, "execution_count": 19, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["sns.histplot(df, x=\"percent_node_with_valid_version\")"]}, {"cell_type": "markdown", "id": "9291756b", "metadata": {}, "source": ["## Medium workflows"]}, {"cell_type": "code", "execution_count": 20, "id": "7c7d96c7", "metadata": {}, "outputs": [], "source": ["df = df_prime[df_prime[\"difficulty\"] == \"medium\"]"]}, {"cell_type": "code", "execution_count": 21, "id": "44472701", "metadata": {}, "outputs": [{"data": {"text/plain": ["<Axes: xlabel='llm_score', ylabel='Count'>"]}, "execution_count": 21, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["sns.histplot(df, x=\"llm_score\", bins=20)"]}, {"cell_type": "code", "execution_count": 22, "id": "09842340", "metadata": {}, "outputs": [{"data": {"text/plain": ["np.float64(1.0)"]}, "execution_count": 22, "metadata": {}, "output_type": "execute_result"}], "source": ["(df[\"llm_score\"]>=6).sum()/len(df)"]}, {"cell_type": "code", "execution_count": 23, "id": "a4688dfe", "metadata": {}, "outputs": [{"data": {"text/plain": ["<Axes: xlabel='top_level_keys_present', ylabel='Count'>"]}, "execution_count": 23, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAjcAAAGxCAYAAACeKZf2AAAAOnRFWHRTb2Z0d2FyZQBNYXRwbG90bGliIHZlcnNpb24zLjEwLjAsIGh0dHBzOi8vbWF0cGxvdGxpYi5vcmcvlHJYcgAAAAlwSFlzAAAPYQAAD2EBqD+naQAALD5JREFUeJzt3X1cVWW+///3VgjsKJgWuEkUKEW0402QP3cN3kTCQzqdmpzTvVpmZSqaHI+K1ZidG+uMGVmmeVKZxrtmIhsb0WRMSFNLEG9GkbFCYQjyoAXqKAqs7x/+3KcdyJ3A3ly9no/Hejy8rnVda33W1Ti+H2utvbfNsixLAAAAhmjn7gIAAACaE+EGAAAYhXADAACMQrgBAABGIdwAAACjEG4AAIBRCDcAAMAohBsAAGAUL3cX0Nqqq6v17bffqlOnTrLZbO4uBwAANIBlWTp9+rSCgoLUrl3d92Z+duHm22+/VXBwsLvLAAAATVBYWKju3bvXOeZnF246deok6dLi+Pn5ubkaAADQEOXl5QoODnb+O16Xn124ufwoys/Pj3ADAEAb05BXSnihGAAAGIVwAwAAjEK4AQAARiHcAAAAoxBuAACAUQg3AADAKIQbAABgFMINAAAwCuEGAAAYhXADAACM4tZws2TJEvXv39/5UwgOh0ObNm2qc05mZqYiIyPl6+ursLAwLV26tJWqBQAAbYFbw0337t31yiuvKCsrS1lZWbrzzjt177336tChQ7WOz8/PV3x8vKKjo5WTk6M5c+Zo6tSpSk1NbeXKAQCAp7JZlmW5u4gf69Kli37zm9/oySefrLFv1qxZ2rBhg3Jzc519EydO1P79+7Vr164GHb+8vFz+/v4qKyvjhzMBAGgjGvPvt8e8c1NVVaV169bp7NmzcjgctY7ZtWuXYmNjXfri4uKUlZWlixcvtkaZAADAw3m5u4CDBw/K4XDo/Pnz6tixo9avX6++ffvWOrakpESBgYEufYGBgaqsrFRpaansdnuNORUVFaqoqHC2y8vLm/cCfqKgoEClpaUteg4AADzZ9ddfrx49erjt/G4PN+Hh4dq3b59++OEHpaamaty4ccrMzLxiwLHZbC7ty0/Vftp/2fz58zVv3rzmLfoKCgoK1KdPhM6d+3urnA8AAE/UocO1OnIk120Bx+3h5pprrtHNN98sSYqKitKePXv0xhtv6J133qkxtlu3biopKXHpO3HihLy8vNS1a9daj5+UlKTExERnu7y8XMHBwc14Bf+ntLRU5879Xf/f+Lnys4e0yDkAAPBk5cXH9MWKeSotLf35hpufsizL5THSjzkcDn388ccufVu2bFFUVJS8vb1rnePj4yMfH59mr7MufvYQdekR3qrnBAAAl7j1heI5c+Zo+/btOnbsmA4ePKjnn39eGRkZevTRRyVduusyduxY5/iJEyfq+PHjSkxMVG5urlasWKHly5drxowZ7roEAADgYdx65+a7777TmDFjVFxcLH9/f/Xv31+bN2/WyJEjJUnFxcUqKChwjg8NDVVaWpqmT5+uxYsXKygoSIsWLdLo0aPddQkAAMDDuDXcLF++vM79KSkpNfqGDRumvXv3tlBFAACgrfOY77kBAABoDoQbAABgFMINAAAwCuEGAAAYhXADAACMQrgBAABGIdwAAACjEG4AAIBRCDcAAMAohBsAAGAUwg0AADAK4QYAABiFcAMAAIxCuAEAAEYh3AAAAKMQbgAAgFEINwAAwCiEGwAAYBTCDQAAMArhBgAAGIVwAwAAjEK4AQAARiHcAAAAoxBuAACAUQg3AADAKIQbAABgFMINAAAwCuEGAAAYhXADAACMQrgBAABGIdwAAACjEG4AAIBRCDcAAMAohBsAAGAUwg0AADAK4QYAABiFcAMAAIxCuAEAAEYh3AAAAKMQbgAAgFEINwAAwCiEGwAAYBTCDQAAMArhBgAAGIVwAwAAjEK4AQAARiHcAAAAoxBuAACAUdwabubPn6/bbrtNnTp1UkBAgO677z7l5eXVOScjI0M2m63GduTIkVaqGgAAeDK3hpvMzExNnjxZu3fvVnp6uiorKxUbG6uzZ8/WOzcvL0/FxcXOrVevXq1QMQAA8HRe7jz55s2bXdorV65UQECAsrOzNXTo0DrnBgQEqHPnzi1YHQAAaIs86p2bsrIySVKXLl3qHTto0CDZ7XbFxMRo27ZtVxxXUVGh8vJylw0AAJjLY8KNZVlKTEzUL37xC91yyy1XHGe327Vs2TKlpqbqww8/VHh4uGJiYvTZZ5/VOn7+/Pny9/d3bsHBwS11CQAAwAO49bHUj02ZMkUHDhzQjh076hwXHh6u8PBwZ9vhcKiwsFALFiyo9VFWUlKSEhMTne3y8nICDgAABvOIOzcJCQnasGGDtm3bpu7duzd6/pAhQ3T06NFa9/n4+MjPz89lAwAA5nLrnRvLspSQkKD169crIyNDoaGhTTpOTk6O7HZ7M1cHAADaIreGm8mTJ2vNmjX64x//qE6dOqmkpESS5O/vrw4dOki69FipqKhI7733niQpOTlZISEh6tevny5cuKBVq1YpNTVVqampbrsOAADgOdwabpYsWSJJGj58uEv/ypUr9fjjj0uSiouLVVBQ4Nx34cIFzZgxQ0VFRerQoYP69eunjRs3Kj4+vrXKBgAAHsztj6Xqk5KS4tKeOXOmZs6c2UIVAQCAts4jXigGAABoLoQbAABgFMINAAAwCuEGAAAYhXADAACMQrgBAABGIdwAAACjEG4AAIBRCDcAAMAohBsAAGAUwg0AADAK4QYAABiFcAMAAIxCuAEAAEYh3AAAAKMQbgAAgFEINwAAwCiEGwAAYBTCDQAAMArhBgAAGIVwAwAAjEK4AQAARiHcAAAAoxBuAACAUQg3AADAKIQbAABgFMINAAAwCuEGAAAYhXADAACMQrgBAABGIdwAAACjEG4AAIBRCDcAAMAohBsAAGAUwg0AADAK4QYAABiFcAMAAIxCuAEAAEYh3AAAAKMQbgAAgFEINwAAwCiEGwAAYBTCDQAAMArhBgAAGIVwAwAAjEK4AQAARiHcAAAAo7g13MyfP1+33XabOnXqpICAAN13333Ky8urd15mZqYiIyPl6+ursLAwLV26tBWqBQAAbYFbw01mZqYmT56s3bt3Kz09XZWVlYqNjdXZs2evOCc/P1/x8fGKjo5WTk6O5syZo6lTpyo1NbUVKwcAAJ7Ky50n37x5s0t75cqVCggIUHZ2toYOHVrrnKVLl6pHjx5KTk6WJEVERCgrK0sLFizQ6NGjW7pkAADg4TzqnZuysjJJUpcuXa44ZteuXYqNjXXpi4uLU1ZWli5evNii9QEAAM/n1js3P2ZZlhITE/WLX/xCt9xyyxXHlZSUKDAw0KUvMDBQlZWVKi0tld1ud9lXUVGhiooKZ7u8vLx5CwcAAB7FY+7cTJkyRQcOHNDatWvrHWuz2VzalmXV2i9demnZ39/fuQUHBzdPwQAAwCN5RLhJSEjQhg0btG3bNnXv3r3Osd26dVNJSYlL34kTJ+Tl5aWuXbvWGJ+UlKSysjLnVlhY2Ky1AwAAz+LWx1KWZSkhIUHr169XRkaGQkND653jcDj08ccfu/Rt2bJFUVFR8vb2rjHex8dHPj4+zVYzAADwbG69czN58mStWrVKa9asUadOnVRSUqKSkhKdO3fOOSYpKUljx451tidOnKjjx48rMTFRubm5WrFihZYvX64ZM2a44xIAAICHcWu4WbJkicrKyjR8+HDZ7Xbn9v777zvHFBcXq6CgwNkODQ1VWlqaMjIyNHDgQP37v/+7Fi1axMfAAQCAJA94LFWflJSUGn3Dhg3T3r17W6AiAADQ1nnEC8UAAADNhXADAACMQrgBAABGIdwAAACjEG4AAIBRCDcAAMAohBsAAGAUwg0AADAK4QYAABiFcAMAAIxCuAEAAEYh3AAAAKMQbgAAgFEINwAAwCiEGwAAYBTCDQAAMArhBgAAGIVwAwAAjEK4AQAARiHcAAAAoxBuAACAUQg3AADAKIQbAABgFMINAAAwCuEGAAAYpUnhJiwsTCdPnqzR/8MPPygsLOyqiwIAAGiqJoWbY8eOqaqqqkZ/RUWFioqKrrooAACApvJqzOANGzY4//zJJ5/I39/f2a6qqtLWrVsVEhLSbMUBAAA0VqPCzX333SdJstlsGjdunMs+b29vhYSE6LXXXmu24gAAABqrUeGmurpakhQaGqo9e/bo+uuvb5GiAAAAmqpR4eay/Pz85q4DAACgWTQp3EjS1q1btXXrVp04ccJ5R+eyFStWXHVhAAAATdGkcDNv3jy9/PLLioqKkt1ul81ma+66AAAAmqRJ4Wbp0qVKSUnRmDFjmrseAACAq9Kk77m5cOGCbr/99uauBQAA4Ko1KdxMmDBBa9asae5aAAAArlqTHkudP39ey5Yt05///Gf1799f3t7eLvsXLlzYLMUBAAA0VpPCzYEDBzRw4EBJ0l/+8heXfbxcDAAA3KlJ4Wbbtm3NXQcAAECzaNI7NwAAAJ6qSXduRowYUefjp08//bTJBQEAAFyNJoWby+/bXHbx4kXt27dPf/nLX2r8oCYAAEBralK4ef3112vtf+mll3TmzJmrKggAAOBqNOs7N4899hi/KwUAANyqWcPNrl275Ovr25yHBAAAaJQmPZa6//77XdqWZam4uFhZWVl68cUXm6UwAACApmhSuPH393dpt2vXTuHh4Xr55ZcVGxvbLIUBAAA0RZPCzcqVK5u7DgAAgGZxVe/cZGdna9WqVVq9erVycnIaPf+zzz7TPffco6CgINlsNn300Ud1js/IyJDNZquxHTlypIlXAAAATNOkOzcnTpzQQw89pIyMDHXu3FmWZamsrEwjRozQunXrdMMNNzToOGfPntWAAQP0xBNPaPTo0Q0+f15envz8/Jzthp4PAACYr0nhJiEhQeXl5Tp06JAiIiIkSYcPH9a4ceM0depUrV27tkHHGTVqlEaNGtXo8wcEBKhz586NngcAAMzXpMdSmzdv1pIlS5zBRpL69u2rxYsXa9OmTc1W3JUMGjRIdrtdMTEx/IgnAABw0aQ7N9XV1fL29q7R7+3trerq6qsu6krsdruWLVumyMhIVVRU6He/+51iYmKUkZGhoUOH1jqnoqJCFRUVznZ5eXmL1QcAANyvSeHmzjvv1LRp07R27VoFBQVJkoqKijR9+nTFxMQ0a4E/Fh4ervDwcGfb4XCosLBQCxYsuGK4mT9/vubNm9diNQEAAM/SpMdSb731lk6fPq2QkBDddNNNuvnmmxUaGqrTp0/rzTffbO4a6zRkyBAdPXr0ivuTkpJUVlbm3AoLC1uxOgAA0NqadOcmODhYe/fuVXp6uo4cOSLLstS3b1/dddddzV1fvXJycmS326+438fHRz4+Pq1YEQAAcKdGhZtPP/1UU6ZM0e7du+Xn56eRI0dq5MiRkqSysjL169dPS5cuVXR0dIOOd+bMGX311VfOdn5+vvbt26cuXbqoR48eSkpKUlFRkd577z1JUnJyskJCQtSvXz9duHBBq1atUmpqqlJTUxtzGQAAwGCNCjfJycl66qmnXL5j5jJ/f38988wzWrhwYYPDTVZWlkaMGOFsJyYmSpLGjRunlJQUFRcXq6CgwLn/woULmjFjhoqKitShQwf169dPGzduVHx8fGMuAwAAGKxR4Wb//v169dVXr7g/NjZWCxYsaPDxhg8fLsuyrrg/JSXFpT1z5kzNnDmzwccHAAA/P416ofi7776r9SPgl3l5eel///d/r7ooAACApmpUuLnxxht18ODBK+4/cOBAnS/3AgAAtLRGhZv4+Hj9+te/1vnz52vsO3funObOnat/+qd/arbiAAAAGqtR79y88MIL+vDDD9W7d29NmTJF4eHhstlsys3N1eLFi1VVVaXnn3++pWoFAACoV6PCTWBgoHbu3Klnn31WSUlJzpeBbTab4uLi9PbbbyswMLBFCgUAAGiIRn+JX8+ePZWWlqbvv/9eX331lSzLUq9evXTddde1RH0AAACN0qRvKJak6667Trfddltz1gIAAHDVmvTbUgAAAJ6KcAMAAIxCuAEAAEYh3AAAAKMQbgAAgFEINwAAwCiEGwAAYBTCDQAAMArhBgAAGIVwAwAAjEK4AQAARiHcAAAAoxBuAACAUQg3AADAKIQbAABgFMINAAAwCuEGAAAYhXADAACMQrgBAABGIdwAAACjEG4AAIBRCDcAAMAohBsAAGAUwg0AADAK4QYAABiFcAMAAIxCuAEAAEYh3AAAAKMQbgAAgFEINwAAwCiEGwAAYBTCDQAAMArhBgAAGIVwAwAAjEK4AQAARiHcAAAAoxBuAACAUQg3AADAKIQbAABgFMINAAAwCuEGAAAYxa3h5rPPPtM999yjoKAg2Ww2ffTRR/XOyczMVGRkpHx9fRUWFqalS5e2fKEAAKDNcGu4OXv2rAYMGKC33nqrQePz8/MVHx+v6Oho5eTkaM6cOZo6dapSU1NbuFIAANBWeLnz5KNGjdKoUaMaPH7p0qXq0aOHkpOTJUkRERHKysrSggULNHr06BaqEgAAtCVt6p2bXbt2KTY21qUvLi5OWVlZunjxYq1zKioqVF5e7rIBAABztalwU1JSosDAQJe+wMBAVVZWqrS0tNY58+fPl7+/v3MLDg5ujVIBAICbtKlwI0k2m82lbVlWrf2XJSUlqayszLkVFha2eI0AAMB93PrOTWN169ZNJSUlLn0nTpyQl5eXunbtWuscHx8f+fj4tEZ5AADAA7SpOzcOh0Pp6ekufVu2bFFUVJS8vb3dVBUAAPAkbg03Z86c0b59+7Rv3z5Jlz7qvW/fPhUUFEi69Ehp7NixzvETJ07U8ePHlZiYqNzcXK1YsULLly/XjBkz3FE+AADwQG59LJWVlaURI0Y424mJiZKkcePGKSUlRcXFxc6gI0mhoaFKS0vT9OnTtXjxYgUFBWnRokV8DBwAADi5NdwMHz7c+UJwbVJSUmr0DRs2THv37m3BqgAAQFvWpt65AQAAqA/hBgAAGIVwAwAAjEK4AQAARiHcAAAAoxBuAACAUQg3AADAKIQbAABgFMINAAAwCuEGAAAYhXADAACMQrgBAABGIdwAAACjEG4AAIBRCDcAAMAohBsAAGAUwg0AADAK4QYAABiFcAMAAIxCuAEAAEYh3AAAAKMQbgAAgFEINwAAwCiEGwAAYBTCDQAAMArhBgAAGIVwAwAAjEK4AQAARiHcAAAAoxBuAACAUQg3AADAKIQbAABgFMINAAAwCuEGAAAYhXADAACMQrgBAABGIdwAAACjEG4AAIBRCDcAAMAohBsAAGAUwg0AADAK4QYAABiFcAMAAIxCuAEAAEYh3AAAAKMQbgAAgFEINwAAwChuDzdvv/22QkND5evrq8jISG3fvv2KYzMyMmSz2WpsR44cacWKAQCAJ3NruHn//ff13HPP6fnnn1dOTo6io6M1atQoFRQU1DkvLy9PxcXFzq1Xr16tVDEAAPB0bg03Cxcu1JNPPqkJEyYoIiJCycnJCg4O1pIlS+qcFxAQoG7dujm39u3bt1LFAADA07kt3Fy4cEHZ2dmKjY116Y+NjdXOnTvrnDto0CDZ7XbFxMRo27ZtLVkmAABoY7zcdeLS0lJVVVUpMDDQpT8wMFAlJSW1zrHb7Vq2bJkiIyNVUVGh3/*******************************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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["sns.histplot(df, x=\"top_level_keys_present\")"]}, {"cell_type": "code", "execution_count": 24, "id": "8066e818", "metadata": {}, "outputs": [{"data": {"text/plain": ["<Axes: xlabel='percent_node_with_valid_type', ylabel='Count'>"]}, "execution_count": 24, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["sns.histplot(df, x=\"percent_node_with_valid_type\")"]}, {"cell_type": "code", "execution_count": 25, "id": "90a9d9a3", "metadata": {}, "outputs": [{"data": {"text/plain": ["<Axes: xlabel='percent_node_with_valid_version', ylabel='Count'>"]}, "execution_count": 25, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["sns.histplot(df, x=\"percent_node_with_valid_version\")"]}, {"cell_type": "markdown", "id": "4d763954", "metadata": {}, "source": ["## Hard workflows"]}, {"cell_type": "code", "execution_count": 26, "id": "0c4a08d8", "metadata": {}, "outputs": [], "source": ["df = df_prime[df_prime[\"difficulty\"] == \"hard\"]"]}, {"cell_type": "code", "execution_count": 27, "id": "521a045f", "metadata": {}, "outputs": [{"data": {"text/plain": ["<Axes: xlabel='llm_score', ylabel='Count'>"]}, "execution_count": 27, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["sns.histplot(df, x=\"llm_score\", bins=20)"]}, {"cell_type": "code", "execution_count": 28, "id": "19ab5cdf", "metadata": {}, "outputs": [{"data": {"text/plain": ["np.float64(0.6666666666666666)"]}, "execution_count": 28, "metadata": {}, "output_type": "execute_result"}], "source": ["(df[\"llm_score\"]>=6).sum()/len(df)"]}, {"cell_type": "code", "execution_count": 29, "id": "516e335b", "metadata": {}, "outputs": [{"data": {"text/plain": ["<Axes: xlabel='top_level_keys_present', ylabel='Count'>"]}, "execution_count": 29, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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*******************************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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["sns.histplot(df, x=\"top_level_keys_present\")"]}, {"cell_type": "code", "execution_count": 30, "id": "4cd2f1aa", "metadata": {}, "outputs": [{"data": {"text/plain": ["<Axes: xlabel='percent_node_with_valid_type', ylabel='Count'>"]}, "execution_count": 30, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["sns.histplot(df, x=\"percent_node_with_valid_type\")"]}, {"cell_type": "code", "execution_count": 31, "id": "d7940155", "metadata": {}, "outputs": [{"data": {"text/plain": ["<Axes: xlabel='percent_node_with_valid_version', ylabel='Count'>"]}, "execution_count": 31, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAkAAAAGxCAYAAACKvAkXAAAAOnRFWHRTb2Z0d2FyZQBNYXRwbG90bGliIHZlcnNpb24zLjEwLjAsIGh0dHBzOi8vbWF0cGxvdGxpYi5vcmcvlHJYcgAAAAlwSFlzAAAPYQAAD2EBqD+naQAAO61JREFUeJzt3XtYVWX+///X9gSUQqEjh0TQUhSPhCZgnkbF0TKbybSpMEetcbTUmLQhMw/1lXEmFU3TccYkMw/N4LEsRUfQkppU0HI8R8EQjOGoiBoeuH9/+HN/2nJGcIPr+biudV1z3+te937fe5G8Zq212TZjjBEAAICF1HJ2AQAAALcaAQgAAFgOAQgAAFgOAQgAAFgOAQgAAFgOAQgAAFgOAQgAAFgOAQgAAFhOHWcXUB0VFBTo+++/V4MGDWSz2ZxdDgAAKANjjM6dOydfX1/VqlXyNR4CUBG+//57+fn5ObsMAABQARkZGWrSpEmJYwhARWjQoIGka2+gu7u7k6sBAABlkZubKz8/P/vv8ZIQgIpw/baXu7s7AQgAgBqmLI+v8BA0AACwHAIQAACwHAIQAACwHAIQAACwHAIQAACwHAIQAACwHAIQAACwHAIQAACwHAIQAACwHAIQAACwHKcGoJiYGHXu3FkNGjRQ48aN9eijj+rIkSOlHpeUlKSQkBC5urqqefPmWrx4caEx8fHxCgoKkouLi4KCgrRu3bqqWAIAAKiBnBqAkpKSNHbsWH3++edKSEjQlStXFBERofPnzxd7TFpamgYMGKBu3bopJSVFr7zyisaNG6f4+Hj7mOTkZA0dOlSRkZHav3+/IiMjNWTIEH3xxRe3YlkAAKCasxljjLOLuO6HH35Q48aNlZSUpO7duxc55uWXX9bGjRt16NAhe9/o0aO1f/9+JScnS5KGDh2q3Nxcffzxx/Yxv/jFL3T33Xdr1apVpdaRm5srDw8PnT17li9DBQCghijP7+9q9QzQ2bNnJUmenp7FjklOTlZERIRDX79+/bRnzx5dvny5xDG7d++u5IoBAEBNVMfZBVxnjFFUVJQefPBBtW3btthx2dnZ8vLycujz8vLSlStXlJOTIx8fn2LHZGdnFzlnfn6+8vPz7e3c3NybWAkA3D7S09OVk5Pj7DJwG2rUqJGaNm3qtNevNgHo+eef14EDB/Tpp5+WOtZmszm0r9/F+2l/UWNu7LsuJiZG06dPL2/JAHBbS09PV6tWrXXx4gVnl4LbkJvbHTp8+JDTQlC1CEAvvPCCNm7cqJ07d6pJkyYljvX29i50JefkyZOqU6eOGjZsWOKYG68KXRcdHa2oqCh7Ozc3V35+fhVZCgDcNnJycnTx4gV1GTFV7j4Bzi4Ht5HcrG/1xTvTlZOTY80AZIzRCy+8oHXr1ikxMVHNmjUr9ZiwsDBt2rTJoW/r1q3q1KmT6tatax+TkJCgF1980WFMeHh4kXO6uLjIxcXlJlYCALcvd58AeTYNdHYZQKVy6kPQY8eO1YoVK7Ry5Uo1aNBA2dnZys7O1sWLF+1joqOjNWzYMHt79OjR+u677xQVFaVDhw7pnXfe0dKlS/XSSy/Zx4wfP15bt27VrFmzdPjwYc2aNUvbtm3ThAkTbuXyAABANeXUALRo0SKdPXtWPXv2lI+Pj31bs2aNfUxWVpbS09Pt7WbNmmnz5s1KTExUx44d9frrr2v+/Pl67LHH7GPCw8O1evVqLVu2TO3bt1dcXJzWrFmjLl263NL1AQCA6snpt8BKExcXV6ivR48e2rdvX4nHDR48WIMHD65oaQAA4DZWrf4OEAAAwK1AAAIAAJZDAAIAAJZDAAIAAJZDAAIAAJZDAAIAAJZDAAIAAJZDAAIAAJZDAAIAAJZDAAIAAJZDAAIAAJZDAAIAAJZDAAIAAJZDAAIAAJZDAAIAAJZDAAIAAJZDAAIAAJZDAAIAAJZDAAIAAJZDAAIAAJZDAAIAAJZDAAIAAJZDAAIAAJZDAAIAAJZDAAIAAJZDAAIAAJZDAAIAAJZDAAIAAJZDAAIAAJZDAAIAAJZDAAIAAJZDAAIAAJZDAAIAAJbj1AC0c+dODRw4UL6+vrLZbFq/fn2J44cPHy6bzVZoa9OmjX1MXFxckWN+/PHHKl4NAACoKZwagM6fP68OHTpowYIFZRo/b948ZWVl2beMjAx5enrq8ccfdxjn7u7uMC4rK0uurq5VsQQAAFAD1XHmi/fv31/9+/cv83gPDw95eHjY2+vXr9fp06f1m9/8xmGczWaTt7d3pdUJAABuLzX6GaClS5eqT58+8vf3d+jPy8uTv7+/mjRpoocfflgpKSlOqhAAAFRHTr0CdDOysrL08ccfa+XKlQ79rVq1UlxcnNq1a6fc3FzNmzdPXbt21f79+9WiRYsi58rPz1d+fr69nZubW6W1AwAA56qxV4Di4uJ011136dFHH3XoDw0N1dNPP60OHTqoW7du+uCDD9SyZUu99dZbxc4VExNjv73m4eEhPz+/Kq4eAAA4U40MQMYYvfPOO4qMjFS9evVKHFurVi117txZx44dK3ZMdHS0zp49a98yMjIqu2QAAFCN1MhbYElJSTp+/LhGjhxZ6lhjjFJTU9WuXbtix7i4uMjFxaUySwQAANWYUwNQXl6ejh8/bm+npaUpNTVVnp6eatq0qaKjo5WZmanly5c7HLd06VJ16dJFbdu2LTTn9OnTFRoaqhYtWig3N1fz589XamqqFi5cWOXrAQAANYNTA9CePXvUq1cvezsqKkqS9MwzzyguLk5ZWVlKT093OObs2bOKj4/XvHnzipzzzJkzeu6555SdnS0PDw8FBwdr586deuCBB6puIQAAoEZxagDq2bOnjDHF7o+LiyvU5+HhoQsXLhR7zNy5czV37tzKKA8AANymauRD0AAAADeDAAQAACyHAAQAACyHAAQAACyHAAQAACyHAAQAACyHAAQAACyHAAQAACyHAAQAACyHAAQAACyHAAQAACyHAAQAACyHAAQAACyHAAQAACyHAAQAACyHAAQAACyHAAQAACyHAAQAACyHAAQAACyHAAQAACyHAAQAACyHAAQAACyHAAQAACyHAAQAACyHAAQAACyHAAQAACyHAAQAACyHAAQAACyHAAQAACyHAAQAACyHAAQAACyHAAQAACyHAAQAACzHqQFo586dGjhwoHx9fWWz2bR+/foSxycmJspmsxXaDh8+7DAuPj5eQUFBcnFxUVBQkNatW1eFqwAAADWNUwPQ+fPn1aFDBy1YsKBcxx05ckRZWVn2rUWLFvZ9ycnJGjp0qCIjI7V//35FRkZqyJAh+uKLLyq7fAAAUEPVceaL9+/fX/379y/3cY0bN9Zdd91V5L7Y2Fj17dtX0dHRkqTo6GglJSUpNjZWq1atuplyAQDAbaJGPgMUHBwsHx8f9e7dWzt27HDYl5ycrIiICIe+fv36affu3beyRAAAUI059QpQefn4+GjJkiUKCQlRfn6+3nvvPfXu3VuJiYnq3r27JCk7O1teXl4Ox3l5eSk7O7vYefPz85Wfn29v5+bmVs0CAABAtVCjAlBgYKACAwPt7bCwMGVkZOjNN9+0ByBJstlsDscZYwr1/VRMTIymT59e+QUDAIBqqUbeAvup0NBQHTt2zN729vYudLXn5MmTha4K/VR0dLTOnj1r3zIyMqqsXgAA4Hw1PgClpKTIx8fH3g4LC1NCQoLDmK1btyo8PLzYOVxcXOTu7u6wAQCA25dTb4Hl5eXp+PHj9nZaWppSU1Pl6emppk2bKjo6WpmZmVq+fLmka5/wCggIUJs2bXTp0iWtWLFC8fHxio+Pt88xfvx4de/eXbNmzdKgQYO0YcMGbdu2TZ9++uktXx8AAKienBqA9uzZo169etnbUVFRkqRnnnlGcXFxysrKUnp6un3/pUuX9NJLLykzM1Nubm5q06aNPvroIw0YMMA+Jjw8XKtXr9arr76qKVOm6N5779WaNWvUpUuXW7cwAABQrTk1APXs2VPGmGL3x8XFObQnTZqkSZMmlTrv4MGDNXjw4JstDwAA3KZq/DNAAAAA5UUAAgAAlkMAAgAAlkMAAgAAlkMAAgAAlkMAAgAAlkMAAgAAlkMAAgAAlkMAAgAAlkMAAgAAlkMAAgAAlkMAAgAAlkMAAgAAlkMAAgAAlkMAAgAAlkMAAgAAlkMAAgAAlkMAAgAAlkMAAgAAlkMAAgAAlkMAAgAAlkMAAgAAlkMAAgAAlkMAAgAAlkMAAgAAlkMAAgAAlkMAAgAAlkMAAgAAlkMAAgAAlkMAAgAAlkMAAgAAlkMAAgAAlkMAAgAAluPUALRz504NHDhQvr6+stlsWr9+fYnj165dq759++pnP/uZ3N3dFRYWpi1btjiMiYuLk81mK7T9+OOPVbgSAABQkzg1AJ0/f14dOnTQggULyjR+586d6tu3rzZv3qy9e/eqV69eGjhwoFJSUhzGubu7Kysry2FzdXWtiiUAAIAaqI4zX7x///7q379/mcfHxsY6tGfOnKkNGzZo06ZNCg4OtvfbbDZ5e3tXVpkAAOA2U6OfASooKNC5c+fk6enp0J+Xlyd/f381adJEDz/8cKErRAAAwNpqdACaPXu2zp8/ryFDhtj7WrVqpbi4OG3cuFGrVq2Sq6urunbtqmPHjhU7T35+vnJzcx02AABw+3LqLbCbsWrVKk2bNk0bNmxQ48aN7f2hoaEKDQ21t7t27ar7779fb731lubPn1/kXDExMZo+fXqV1wwAAKqHGnkFaM2aNRo5cqQ++OAD9enTp8SxtWrVUufOnUu8AhQdHa2zZ8/at4yMjMouGQAAVCM17grQqlWrNGLECK1atUoPPfRQqeONMUpNTVW7du2KHePi4iIXF5fKLBMAAFRjTg1AeXl5On78uL2dlpam1NRUeXp6qmnTpoqOjlZmZqaWL18u6Vr4GTZsmObNm6fQ0FBlZ2dLktzc3OTh4SFJmj59ukJDQ9WiRQvl5uZq/vz5Sk1N1cKFC2/9AgEAQLXk1Ftge/bsUXBwsP0j7FFRUQoODtZrr70mScrKylJ6erp9/F/+8hdduXJFY8eOlY+Pj30bP368fcyZM2f03HPPqXXr1oqIiFBmZqZ27typBx544NYuDgAAVFtOvQLUs2dPGWOK3R8XF+fQTkxMLHXOuXPnau7cuTdZGQAAuJ3VyIegAQAAbgYBCAAAWA4BCAAAWA4BCAAAWA4BCAAAWA4BCAAAWA4BCAAAWA4BCAAAWA4BCAAAWA4BCAAAWE6FAlDz5s116tSpQv1nzpxR8+bNb7ooAACAqlShAPTtt9/q6tWrhfrz8/OVmZl500UBAABUpXJ9GerGjRvt/3vLli3y8PCwt69evart27crICCg0ooDAACoCuUKQI8++qgkyWaz6ZlnnnHYV7duXQUEBGj27NmVVhwAAEBVKFcAKigokCQ1a9ZMX375pRo1alQlRQEAAFSlcgWg69LS0iq7DgAAgFumQgFIkrZv367t27fr5MmT9itD173zzjs3XRgAAEBVqVAAmj59umbMmKFOnTrJx8dHNputsusCAACoMhUKQIsXL1ZcXJwiIyMrux4AAIAqV6G/A3Tp0iWFh4dXdi0AAAC3RIUC0KhRo7Ry5crKrgUAAOCWqNAtsB9//FFLlizRtm3b1L59e9WtW9dh/5w5cyqlOAAAgKpQoQB04MABdezYUZL09ddfO+zjgWgAAFDdVSgA7dixo7LrAAAAuGUq9AwQAABATVahK0C9evUq8VbXP//5zwoXBAAAUNUqFICuP/9z3eXLl5Wamqqvv/660JekAgAAVDcVCkBz584tsn/atGnKy8u7qYIAAACqWqU+A/T000/zPWAAAKDaq9QAlJycLFdX18qcEgAAoNJV6BbYr371K4e2MUZZWVnas2ePpkyZUimFAQAAVJUKBSAPDw+Hdq1atRQYGKgZM2YoIiKiUgoDAACoKhUKQMuWLavsOgAAAG6Zm3oGaO/evVqxYoXef/99paSklPv4nTt3auDAgfL19ZXNZtP69etLPSYpKUkhISFydXVV8+bNtXjx4kJj4uPjFRQUJBcXFwUFBWndunXlrg0AANy+KhSATp48qZ///Ofq3Lmzxo0bp+eff14hISHq3bu3fvjhhzLPc/78eXXo0EELFiwo0/i0tDQNGDBA3bp1U0pKil555RWNGzdO8fHx9jHJyckaOnSoIiMjtX//fkVGRmrIkCH64osvyr1OAABwe6pQAHrhhReUm5urgwcP6n//+59Onz6tr7/+Wrm5uRo3blyZ5+nfv7/eeOONQg9VF2fx4sVq2rSpYmNj1bp1a40aNUojRozQm2++aR8TGxurvn37Kjo6Wq1atVJ0dLR69+6t2NjY8i4TAADcpioUgD755BMtWrRIrVu3tvcFBQVp4cKF+vjjjyutuBslJycXesi6X79+2rNnjy5fvlzimN27d1dZXQAAoGap0EPQBQUFqlu3bqH+unXrqqCg4KaLKk52dra8vLwc+ry8vHTlyhXl5OTIx8en2DHZ2dnFzpufn6/8/Hx7Ozc3t3ILv0F6erpycnKq9DVgPY0aNVLTpk2dXQYA1AgVCkA///nPNX78eK1atUq+vr6SpMzMTL344ovq3bt3pRZ4oxu/hNUYU6i/qDElfXlrTEyMpk+fXolVFi89PV2tWrXWxYsXbsnrwTrc3O7Q4cOHCEEAUAYVCkALFizQoEGDFBAQID8/P9lsNqWnp6tdu3ZasWJFZddo5+3tXehKzsmTJ1WnTh01bNiwxDE3XhX6qejoaEVFRdnbubm58vPzq8TK/09OTo4uXrygLiOmyt0noEpeA9aTm/WtvnhnunJycghAAFAGFQpAfn5+2rdvnxISEnT48GEZYxQUFKQ+ffpUdn0OwsLCtGnTJoe+rVu3qlOnTvZbcmFhYUpISNCLL77oMCY8PLzYeV1cXOTi4lI1RRfD3SdAnk0Db+lrAgCAa8r1EPQ///lPBQUF2Z+R6du3r1544QWNGzdOnTt3Vps2bbRr164yz5eXl6fU1FSlpqZKuvYx99TUVKWnp0u6dmVm2LBh9vGjR4/Wd999p6ioKB06dEjvvPOOli5dqpdeesk+Zvz48dq6datmzZqlw4cPa9asWdq2bZsmTJhQnqUCAIDbWLkCUGxsrJ599lm5u7sX2ufh4aHf/va3mjNnTpnn27Nnj4KDgxUcHCxJioqKUnBwsF577TVJUlZWlj0MSVKzZs20efNmJSYmqmPHjnr99dc1f/58PfbYY/Yx4eHhWr16tZYtW6b27dsrLi5Oa9asUZcuXcqzVAAAcBsr1y2w/fv3a9asWcXuj4iIcPibPKXp2bOn/SHmosTFxRXq69Gjh/bt21fivIMHD9bgwYPLXAcAALCWcl0B+u9//1vkx9+vq1OnTrn+EjQAAIAzlCsA3XPPPfrqq6+K3X/gwAH5+PjcdFEAAABVqVwBaMCAAXrttdf0448/Ftp38eJFTZ06VQ8//HClFQcAAFAVyvUM0Kuvvqq1a9eqZcuWev755xUYGCibzaZDhw5p4cKFunr1qiZPnlxVtQIAAFSKcgUgLy8v7d69W7/73e8UHR3t8FeY+/Xrp7fffrvEPzgIAABQHZT7DyH6+/tr8+bNOn36tI4fPy5jjFq0aKG77767KuoDAACodBX6S9CSdPfdd6tz586VWQsAAMAtUa6HoAEAAG4HBCAAAGA5BCAAAGA5BCAAAGA5BCAAAGA5BCAAAGA5BCAAAGA5BCAAAGA5BCAAAGA5BCAAAGA5BCAAAGA5BCAAAGA5BCAAAGA5BCAAAGA5BCAAAGA5BCAAAGA5BCAAAGA5BCAAAGA5BCAAAGA5BCAAAGA5BCAAAGA5BCAAAGA5BCAAAGA5BCAAAGA5BCAAAGA5BCAAAGA5Tg9Ab7/9tpo1ayZXV1eFhIRo165dxY4dPny4bDZboa1Nmzb2MXFxcUWO+fHHH2/FcgAAQA3g1AC0Zs0aTZgwQZMnT1ZKSoq6deum/v37Kz09vcjx8+bNU1ZWln3LyMiQp6enHn/8cYdx7u7uDuOysrLk6up6K5YEAABqAKcGoDlz5mjkyJEaNWqUWrdurdjYWPn5+WnRokVFjvfw8JC3t7d927Nnj06fPq3f/OY3DuNsNpvDOG9v71uxHAAAUEM4LQBdunRJe/fuVUREhEN/RESEdu/eXaY5li5dqj59+sjf39+hPy8vT/7+/mrSpIkefvhhpaSkVFrdAACg5qvjrBfOycnR1atX5eXl5dDv5eWl7OzsUo/PysrSxx9/rJUrVzr0t2rVSnFxcWrXrp1yc3M1b948de3aVfv371eLFi2KnCs/P1/5+fn2dm5ubgVWBAAAagqnPwRts9kc2saYQn1FiYuL01133aVHH33UoT80NFRPP/20OnTooG7duumDDz5Qy5Yt9dZbbxU7V0xMjDw8POybn59fhdYCAABqBqcFoEaNGql27dqFrvacPHmy0FWhGxlj9M477ygyMlL16tUrcWytWrXUuXNnHTt2rNgx0dHROnv2rH3LyMgo+0IAAECN47QAVK9ePYWEhCghIcGhPyEhQeHh4SUem5SUpOPHj2vkyJGlvo4xRqmpqfLx8Sl2jIuLi9zd3R02AABw+3LaM0CSFBUVpcjISHXq1ElhYWFasmSJ0tPTNXr0aEnXrsxkZmZq+fLlDsctXbpUXbp0Udu2bQvNOX36dIWGhqpFixbKzc3V/PnzlZqaqoULF96SNQEAgOrPqQFo6NChOnXqlGbMmKGsrCy1bdtWmzdvtn+qKysrq9DfBDp79qzi4+M1b968Iuc8c+aMnnvuOWVnZ8vDw0PBwcHauXOnHnjggSpfDwAAqBmcGoAkacyYMRozZkyR++Li4gr1eXh46MKFC8XON3fuXM2dO7eyygMAALchp38KDAAA4FYjAAEAAMshAAEAAMshAAEAAMshAAEAAMshAAEAAMshAAEAAMshAAEAAMshAAEAAMshAAEAAMshAAEAAMshAAEAAMshAAEAAMshAAEAAMshAAEAAMshAAEAAMshAAEAAMshAAEAAMshAAEAAMshAAEAAMshAAEAAMshAAEAAMshAAEAAMshAAEAAMshAAEAAMshAAEAAMshAAEAAMshAAEAAMshAAEAAMshAAEAAMshAAEAAMshAAEAAMshAAEAAMtxegB6++231axZM7m6uiokJES7du0qdmxiYqJsNluh7fDhww7j4uPjFRQUJBcXFwUFBWndunVVvQwAAFCDODUArVmzRhMmTNDkyZOVkpKibt26qX///kpPTy/xuCNHjigrK8u+tWjRwr4vOTlZQ4cOVWRkpPbv36/IyEgNGTJEX3zxRVUvBwAA1BBODUBz5szRyJEjNWrUKLVu3VqxsbHy8/PTokWLSjyucePG8vb2tm+1a9e274uNjVXfvn0VHR2tVq1aKTo6Wr1791ZsbGwVrwYAANQUTgtAly5d0t69exUREeHQHxERod27d5d4bHBwsHx8fNS7d2/t2LHDYV9ycnKhOfv161fqnAAAwDrqOOuFc3JydPXqVXl5eTn0e3l5KTs7u8hjfHx8tGTJEoWEhCg/P1/vvfeeevfurcTERHXv3l2SlJ2dXa45JSk/P1/5+fn2dm5ubkWXBQAAagCnBaDrbDabQ9sYU6jvusDAQAUGBtrbYWFhysjI0JtvvmkPQOWdU5JiYmI0ffr0ipQPAABqIKfdAmvUqJFq165d6MrMyZMnC13BKUloaKiOHTtmb3t7e5d7zujoaJ09e9a+ZWRklPn1AQBAzeO0AFSvXj2FhIQoISHBoT8hIUHh4eFlniclJUU+Pj72dlhYWKE5t27dWuKcLi4ucnd3d9gAAMDty6m3wKKiohQZGalOnTopLCxMS5YsUXp6ukaPHi3p2pWZzMxMLV++XNK1T3gFBASoTZs2unTpklasWKH4+HjFx8fb5xw/fry6d++uWbNmadCgQdqwYYO2bdumTz/91ClrBAAA1Y9TA9DQoUN16tQpzZgxQ1lZWWrbtq02b94sf39/SVJWVpbD3wS6dOmSXnrpJWVmZsrNzU1t2rTRRx99pAEDBtjHhIeHa/Xq1Xr11Vc1ZcoU3XvvvVqzZo26dOlyy9cHAACqJ6c/BD1mzBiNGTOmyH1xcXEO7UmTJmnSpEmlzjl48GANHjy4MsoDAAC3Iad/FQYAAMCtRgACAACWQwACAACWQwACAACWQwACAACWQwACAACWQwACAACWQwACAACWQwACAACWQwACAACWQwACAACWQwACAACWQwACAACWQwACAACWQwACAACWQwACAACWQwACAACWQwACAACWQwACAACWQwACAACWQwACAACWQwACAACWQwACAACWQwACAACWQwACAACWQwACAACWQwACAACWQwACAACWQwACAACWQwACAACWQwACAACWQwACAACWQwACAACW4/QA9Pbbb6tZs2ZydXVVSEiIdu3aVezYtWvXqm/fvvrZz34md3d3hYWFacuWLQ5j4uLiZLPZCm0//vhjVS8FAADUEE4NQGvWrNGECRM0efJkpaSkqFu3burfv7/S09OLHL9z50717dtXmzdv1t69e9WrVy8NHDhQKSkpDuPc3d2VlZXlsLm6ut6KJQEAgBqgjjNffM6cORo5cqRGjRolSYqNjdWWLVu0aNEixcTEFBofGxvr0J45c6Y2bNigTZs2KTg42N5vs9nk7e1dpbUDAICay2lXgC5duqS9e/cqIiLCoT8iIkK7d+8u0xwFBQU6d+6cPD09Hfrz8vLk7++vJk2a6OGHHy50hQgAAFib0wJQTk6Orl69Ki8vL4d+Ly8vZWdnl2mO2bNn6/z58xoyZIi9r1WrVoqLi9PGjRu1atUqubq6qmvXrjp27Fix8+Tn5ys3N9dhAwAAty+n3gKTrt2u+iljTKG+oqxatUrTpk3Thg0b1LhxY3t/aGioQkND7e2uXbvq/vvv11tvvaX58+cXOVdMTIymT59ewRUAAICaxmlXgBo1aqTatWsXutpz8uTJQleFbrRmzRqNHDlSH3zwgfr06VPi2Fq1aqlz584lXgGKjo7W2bNn7VtGRkbZFwIAAGocpwWgevXqKSQkRAkJCQ79CQkJCg8PL/a4VatWafjw4Vq5cqUeeuihUl/HGKPU1FT5+PgUO8bFxUXu7u4OGwAAuH059RZYVFSUIiMj1alTJ4WFhWnJkiVKT0/X6NGjJV27MpOZmanly5dLuhZ+hg0bpnnz5ik0NNR+9cjNzU0eHh6SpOnTpys0NFQtWrRQbm6u5s+fr9TUVC1cuNA5iwQAANWOUwPQ0KFDderUKc2YMUNZWVlq27atNm/eLH9/f0lSVlaWw98E+stf/qIrV65o7NixGjt2rL3/mWeeUVxcnCTpzJkzeu6555SdnS0PDw8FBwdr586deuCBB27p2gAAQPXl9Iegx4wZozFjxhS573qouS4xMbHU+ebOnau5c+dWQmUAAOB25fSvwgAAALjVCEAAAMByCEAAAMByCEAAAMByCEAAAMByCEAAAMByCEAAAMByCEAAAMByCEAAAMByCEAAAMByCEAAAMByCEAAAMByCEAAAMByCEAAAMByCEAAAMByCEAAAMByCEAAAMByCEAAAMByCEAAAMByCEAAAMByCEAAAMByCEAAAMByCEAAAMByCEAAAMByCEAAAMByCEAAAMByCEAAAMByCEAAAMByCEAAAMByCEAAAMByCEAAAMByCEAAAMByCEAAAMBynB6A3n77bTVr1kyurq4KCQnRrl27ShyflJSkkJAQubq6qnnz5lq8eHGhMfHx8QoKCpKLi4uCgoK0bt26qiofAADUQE4NQGvWrNGECRM0efJkpaSkqFu3burfv7/S09OLHJ+WlqYBAwaoW7duSklJ0SuvvKJx48YpPj7ePiY5OVlDhw5VZGSk9u/fr8jISA0ZMkRffPHFrVoWAACo5pwagObMmaORI0dq1KhRat26tWJjY+Xn56dFixYVOX7x4sVq2rSpYmNj1bp1a40aNUojRozQm2++aR8TGxurvn37Kjo6Wq1atVJ0dLR69+6t2NjYW7QqAABQ3TktAF26dEl79+5VRESEQ39ERIR2795d5DHJycmFxvfr10979uzR5cuXSxxT3JwAAMB66jjrhXNycnT16lV5eXk59Ht5eSk7O7vIY7Kzs4scf+XKFeXk5MjHx6fYMcXNKUn5+fnKz8+3t8+ePStJys3NLdeayiIvL0+S9L/vjuhK/sVKnx/WlJt97bbx3r177T9jwM06cuSIJP69QuW7/m9WXl5epf6uvT6XMabUsU4LQNfZbDaHtjGmUF9p42/sL++cMTExmj59eqF+Pz+/4gu/SXtX/LHK5oZ1Pffcc84uAbch/r1CVenRo0eVzHvu3Dl5eHiUOMZpAahRo0aqXbt2oSszJ0+eLHQF5zpvb+8ix9epU0cNGzYscUxxc0pSdHS0oqKi7O2CggL973//U8OGDUsMTlaXm5srPz8/ZWRkyN3d3dnl4Cc4N9UX56Z64/xUX2U5N8YYnTt3Tr6+vqXO57QAVK9ePYWEhCghIUG//OUv7f0JCQkaNGhQkceEhYVp06ZNDn1bt25Vp06dVLduXfuYhIQEvfjiiw5jwsPDi63FxcVFLi4uDn133XVXeZdkWe7u7vxDUU1xbqovzk31xvmpvko7N6Vd+bnOqbfAoqKiFBkZqU6dOiksLExLlixRenq6Ro8eLenalZnMzEwtX75ckjR69GgtWLBAUVFRevbZZ5WcnKylS5dq1apV9jnHjx+v7t27a9asWRo0aJA2bNigbdu26dNPP3XKGgEAQPXj1AA0dOhQnTp1SjNmzFBWVpbatm2rzZs3y9/fX5KUlZXl8DeBmjVrps2bN+vFF1/UwoUL5evrq/nz5+uxxx6zjwkPD9fq1av16quvasqUKbr33nu1Zs0adenS5ZavDwAAVE82U5ZHpYEi5OfnKyYmRtHR0YVuIcK5ODfVF+emeuP8VF+VfW4IQAAAwHKc/l1gAAAAtxoBCAAAWA4BCAAAWA4BCCW6cuWKXn31VTVr1kxubm5q3ry5ZsyYoYKCAodxhw4d0iOPPCIPDw81aNBAoaGhDp/gQ9Uoy/nJy8vT888/ryZNmsjNzU2tW7cu9guHUXnOnTunCRMmyN/fX25ubgoPD9eXX35p32+M0bRp0+Tr6ys3Nzf17NlTBw8edGLF1lLS+bl8+bJefvlltWvXTnfeead8fX01bNgwff/9906u2hpK+2/np37729/KZrNV7AvPDVCCN954wzRs2NB8+OGHJi0tzfz973839evXN7GxsfYxx48fN56enmbixIlm37595sSJE+bDDz80//3vf51YuTWU5fyMGjXK3HvvvWbHjh0mLS3N/OUvfzG1a9c269evd2Llt78hQ4aYoKAgk5SUZI4dO2amTp1q3N3dzX/+8x9jjDF//OMfTYMGDUx8fLz56quvzNChQ42Pj4/Jzc11cuXWUNL5OXPmjOnTp49Zs2aNOXz4sElOTjZdunQxISEhzi7bEkr7b+e6devWmQ4dOhhfX18zd+7ccr8OAQgleuihh8yIESMc+n71q1+Zp59+2t4eOnSoQxu3TlnOT5s2bcyMGTMcxtx///3m1VdfvSU1WtGFCxdM7dq1zYcffujQ36FDBzN58mRTUFBgvL29zR//+Ef7vh9//NF4eHiYxYsX3+pyLae081OUf/3rX0aS+e67725FiZZV1nPzn//8x9xzzz3m66+/Nv7+/hUKQNwCQ4kefPBBbd++XUePHpUk7d+/X59++qkGDBgg6dr3pn300Udq2bKl+vXrp8aNG6tLly5av369E6u2jtLOz/UxGzduVGZmpowx2rFjh44ePap+/fo5q+zb3pUrV3T16lW5uro69Lu5uenTTz9VWlqasrOzFRERYd/n4uKiHj16aPfu3be6XMsp7fwU5ezZs7LZbHxNUhUry7kpKChQZGSkJk6cqDZt2lT8xW4yrOE2V1BQYP7whz8Ym81m6tSpY2w2m5k5c6Z9f1ZWlpFk7rjjDjNnzhyTkpJiYmJijM1mM4mJiU6s3BpKOz/GGJOfn2+GDRtmJJk6deqYevXqmeXLlzupYusICwszPXr0MJmZmebKlSvmvffeMzabzbRs2dJ89tlnRpLJzMx0OObZZ581ERERTqrYWko6Pze6ePGiCQkJMU899ZQTKrWe0s7NzJkzTd++fU1BQYExxnAFCFVjzZo1WrFihVauXKl9+/bp3Xff1Ztvvql3331XkuwP2w4aNEgvvviiOnbsqD/84Q96+OGHtXjxYmeWbgmlnR9Jmj9/vj7//HNt3LhRe/fu1ezZszVmzBht27bNiZXf/t577z0ZY3TPPffIxcVF8+fP15NPPqnatWvbx9hsNodjjDGF+lA1ynJ+pGsPRD/xxBMqKCjQ22+/7aRqraWkc7N3717NmzdPcXFxN//fSmUlNtyemjRpYhYsWODQ9/rrr5vAwEBjzLWrC3Xq1DGvv/66w5hJkyaZ8PDwW1anVZV2fi5cuGDq1q1b6H76yJEjTb9+/W5ZnVaWl5dnvv/+e2PMtYc7BwwYYE6cOGEkmX379jmMfeSRR8ywYcOcUaZlFXV+rrt06ZJ59NFHTfv27U1OTo6zSrSsos7N3Llzjc1mM7Vr17ZvkkytWrWMv79/uebnChBKdOHCBdWq5fhjUrt2bfuVn3r16qlz5846cuSIw5ijR4/av9QWVae083P58mVdvny5xDGoWnfeead8fHx0+vRpbdmyRYMGDVKzZs3k7e2thIQE+7hLly4pKSlJ4eHhTqzWeoo6P9K1/3aGDBmiY8eOadu2bWrYsKGTK7Weos5NZGSkDhw4oNTUVPvm6+uriRMnasuWLeWa36nfBo/qb+DAgfp//+//qWnTpmrTpo1SUlI0Z84cjRgxwj5m4sSJGjp0qLp3765evXrpk08+0aZNm5SYmOi8wi2itPPj7u6uHj16aOLEiXJzc5O/v7+SkpK0fPlyzZkzx8nV3962bNkiY4wCAwN1/PhxTZw4UYGBgfrNb34jm82mCRMmaObMmWrRooVatGihmTNn6o477tCTTz7p7NItoaTzc+XKFQ0ePFj79u3Thx9+qKtXryo7O1uS5OnpqXr16jm5+ttbSeembt26hcJo3bp15e3trcDAwPK9UGVfssLtJTc314wfP940bdrUuLq6mubNm5vJkyeb/Px8h3FLly419913n3F1dTUdOnTgb8zcImU5P1lZWWb48OHG19fXuLq6msDAQDN79mz7A4SoGmvWrDHNmzc39erVM97e3mbs2LHmzJkz9v0FBQVm6tSpxtvb27i4uJju3bubr776yokVW0tJ5yctLc1IKnLbsWOHcwu3gNL+27lRRR+C5tvgAQCA5fAMEAAAsBwCEAAAsBwCEAAAsBwCEAAAsBwCEAAAsBwCEAAAsBwCEAAAsBwCEAAAsBwCEIBbLjExUTabTWfOnLnlrz1t2jR17NixxDHffvutbDabUlNTb0lNRbmxzuHDh+vRRx8t8ZiePXtqwoQJVVrXzXDmeQduRAACLMRms2n9+vXOLsOpXnrpJW3fvt3eLkuwqA7mzZunuLg4Z5dxU8LDw5WVlSUPDw9nlwLwZaiAs129elU2m63QN7ajatSvX1/169d3dhnlVhNCw+XLl1W3bt1i99erV0/e3t63sCKgePyLC5RTz5499fzzz+v555/XXXfdpYYNG+rVV1/V9a/Vu3TpkiZNmqR77rlHd955p7p06aLExET78XFxcbrrrrv04YcfKigoSC4uLvruu++Un5+vSZMmyc/PTy4uLmrRooWWLl1qP+7f//63BgwYoPr168vLy0uRkZHKyclxqGvcuHGaNGmSPD095e3trWnTptn3BwQESJJ++ctfymaz2dsluX4b5r333lNAQIA8PDz0xBNP6Ny5c/Yx+fn5GjdunBo3bixXV1c9+OCD+vLLLx3m2bx5s1q2bCk3Nzf16tVL3377baHX2r17t7p37y43Nzf5+flp3LhxOn/+fKk1vvXWW2rXrp29vX79etlsNi1cuNDe169fP0VHRzus6fr/fvfdd7VhwwbZbDbZbDaHc/XNN9+oV69euuOOO9ShQwclJyeXWs/Zs2fl5uamTz75xKF/7dq1uvPOO5WXlydJevnll9WyZUvdcccdat68uaZMmaLLly8XO++NV6rOnz+vYcOGqX79+vLx8dHs2bNLre266OhohYaGFupv3769pk6dam8vW7ZMrVu3lqurq1q1aqW3337bvu/6bcIPPvhAPXv2lKurq1asWKHvvvtOAwcO1N13360777xTbdq00ebNmyUVfQssPj5ebdq0kYuLiwICAgqtIyAgQDNnztSIESPUoEEDNW3aVEuWLCnzWoFiVfTbWgGr6tGjh6lfv74ZP368OXz4sFmxYoW54447zJIlS4wxxjz55JMmPDzc7Ny50xw/ftz8+c9/Ni4uLubo0aPGGGOWLVtm6tata8LDw81nn31mDh8+bPLy8syQIUOMn5+fWbt2rTlx4oTZtm2bWb16tTHGmO+//940atTIREdHm0OHDpl9+/aZvn37ml69ejnU5e7ubqZNm2aOHj1q3n33XWOz2czWrVuNMcacPHnSSDLLli0zWVlZ5uTJk6WuderUqaZ+/frmV7/6lfnqq6/Mzp07jbe3t3nllVfsY8aNG2d8fX3N5s2bzcGDB80zzzxj7r77bnPq1CljjDHp6enGxcXF4f3y8vIykszp06eNMcYcOHDA1K9f38ydO9ccPXrUfPbZZyY4ONgMHz681BoPHDhgbDab+eGHH4wxxkyYMME0atTIPP7448YYYy5fvmzq169vPv74Y/uaOnToYIwx5ty5c2bIkCHmF7/4hcnKyjJZWVkmPz/f/m3grVq1Mh9++KE5cuSIGTx4sPH39zeXL18utabHHnvMPP3004X6fv3rX9vbr7/+uvnss89MWlqa2bhxo/Hy8jKzZs1yeO+v12mMMc8884wZNGiQvf273/3ONGnSxGzdutUcOHDAPPzww/afy9J89dVXRpI5fvy4ve/rr782ksyRI0eMMcYsWbLE+Pj4mPj4ePPNN9+Y+Ph44+npaeLi4owx//eN6QEBAfYxmZmZ5qGHHjJ9+/Y1Bw4cMCdOnDCbNm0ySUlJxhhjduzY4XDe9+zZY2rVqmVmzJhhjhw5YpYtW2bc3NzMsmXL7HX5+/sbT09Ps3DhQnPs2DETExNjatWqZQ4dOlTqOoGSEICAcurRo4dp3bq1KSgosPe9/PLLpnXr1ub48ePGZrOZzMxMh2N69+5toqOjjTHXApAkk5qaat9/5MgRI8kkJCQU+ZpTpkwxERERDn0ZGRkOv7B69OhhHnzwQYcxnTt3Ni+//LK9LcmsW7euzGudOnWqueOOO0xubq69b+LEiaZLly7GGGPy8vJM3bp1zfvvv2/ff+nSJePr62v+9Kc/GWOMiY6OLvL9+ukvwsjISPPcc885vPauXbtMrVq1zMWLF0ussaCgwDRq1Mj84x//MMYY07FjRxMTE2MaN25sjDFm9+7dpk6dOubcuXP2NZUULIz5v1/uf/vb3+x9Bw8eNJLK9It37dq1pn79+ub8+fPGGGPOnj1rXF1dzUcffVTsMX/6059MSEiIvV1SnefOnTP16tWzB2RjjDl16pRxc3MrUwAyxpj27dubGTNm2NvR0dGmc+fO9rafn59ZuXKlwzGvv/66CQsLM8b833sUGxvrMKZdu3Zm2rRpRb7mjQHoySefNH379nUYM3HiRBMUFGRv+/v7O4TJgoIC07hxY7No0aIyrRMoDrfAgAoIDQ2VzWazt8PCwnTs2DHt2bNHxhi1bNnS/qxJ/fr1lZSUpBMnTtjH16tXT+3bt7e3U1NTVbt2bfXo0aPI19u7d6927NjhMGerVq0kyWHen84pST4+Pjp58uRNrTUgIEANGjQocs4TJ07o8uXL6tq1q31/3bp19cADD+jQoUOSpEOHDhX5ft24vri4OIf19evXTwUFBUpLSyuxPpvNpu7duysxMVFnzpzRwYMHNXr0aF29elWHDh1SYmKi7r///go99/PT99PHx0eSyvR+PvTQQ6pTp442btwo6dptngYNGigiIsI+5h//+IcefPBBeXt7q379+poyZYrS09PLVNeJEyd06dIlh/fR09NTgYGBZTpekp566im9//77kiRjjFatWqWnnnpKkvTDDz8oIyNDI0eOdDgnb7zxhsPPmyR16tTJoT1u3Di98cYb6tq1q6ZOnaoDBw4UW8OhQ4ccfnYkqWvXrjp27JiuXr1q7/vpebDZbPL29r7pn2uAh6CBSla7dm3t3btXtWvXduj/6S9gNzc3h0Dg5uZW4pwFBQUaOHCgZs2aVWjf9V/Mkgo9gGqz2VRQUFCu+m9U0pzm/3/u6adrud5/ve/6mJIUFBTot7/9rcaNG1doX9OmTUs9vmfPnlqyZIl27dqlDh066K677lL37t2VlJSkxMRE9ezZs9Q5ivLTtV9fT1nez3r16mnw4MFauXKlnnjiCa1cuVJDhw5VnTrX/sn9/PPP9cQTT2j69Onq16+fPDw8tHr16jI/x1OW97Q0Tz75pP7whz9o3759unjxojIyMvTEE09I+r81/vWvf1WXLl0cjrvx5/rOO+90aI8aNUr9+vXTRx99pK1btyomJkazZ8/WCy+8UOQ6ivrZuVFV/FwDXAECKuDzzz8v1G7RooWCg4N19epVnTx5Uvfdd5/DVtKnX9q1a6eCggIlJSUVuf/+++/XwYMHFRAQUGjeG38BlaRu3boO/8/6Zt13332qV6+ePv30U3vf5cuXtWfPHrVu3VqSFBQUVOT79VPX13fj2q7PX5qePXvq4MGD+sc//mEPOz169NC2bdu0e/fuYq+sSdfCSmW+J9c99dRT+uSTT3Tw4EHt2LHDfnVFkj777DP5+/tr8uTJ6tSpk1q0aKHvvvuuzHPfd999qlu3rsP7ePr0aR09erTMczRp0kTdu3fX+++/r/fff199+vSRl5eXJMnLy0v33HOPvvnmm0Lno1mzZqXO7efnp9GjR2vt2rX6/e9/r7/+9a9FjgsKCnL42ZGuPQzfsmXLQkELqGwEIKACMjIyFBUVpSNHjmjVqlV66623NH78eLVs2VJPPfWUhg0bprVr1yotLU1ffvmlZs2aZf8kTFECAgL0zDPPaMSIEVq/fr3S0tKUmJioDz74QJI0duxY/e9//9Ovf/1r/etf/9I333yjrVu3asSIEeX65R0QEKDt27crOztbp0+fvun34c4779Tvfvc7TZw4UZ988on+/e9/69lnn9WFCxc0cuRISdLo0aN14sQJ+/u1cuXKQn/P5uWXX1ZycrLGjh2r1NRUHTt2TBs3bizyqkFR2rZtq4YNG+r999+3B6CePXtq/fr1unjxoh588MFijw0ICNCBAwd05MgR5eTklPhJrPLo0aOHvLy89NRTTykgIMDhU1f33Xef0tPTtXr1ap04cULz58/XunXryjx3/fr1NXLkSE2cOFHbt2/X119/reHDh5f7Tyk89dRTWr16tf7+97/r6aefdtg3bdo0xcTEaN68eTp69Ki++uorLVu2THPmzClxzgkTJmjLli1KS0vTvn379M9//tMehm/0+9//Xtu3b9frr7+uo0eP6t1339WCBQv00ksvlWsdQEUQgIAKGDZsmC5evKgHHnhAY8eO1QsvvKDnnntO0rWPDg8bNky///3vFRgYqEceeURffPGF/Pz8Spxz0aJFGjx4sMaMGaNWrVrp2WeftX8M3NfXV5999pmuXr2qfv36qW3btho/frw8PDzK9Utv9uzZSkhIkJ+fn4KDgyv+BvzEH//4Rz322GOKjIzU/fffr+PHj2vLli26++67JV27hRUfH69NmzapQ4cOWrx4sWbOnOkwR/v27ZWUlKRjx46pW7duCg4O1pQpUxxu75XEZrPZr/J069bNPqeHh4eCg4Pl7u5e7LHPPvusAgMD1alTJ/3sZz/TZ599VpG3ociafv3rX2v//v0OV38kadCgQXrxxRf1/PPPq2PHjtq9e7emTJlSrvn//Oc/q3v37nrkkUfUp08fPfjggwoJCSnXHI8//rhOnTqlCxcuFPpjkKNGjdLf/vY3xcXFqV27durRo4fi4uJKvQJ09epVjR07Vq1bt9YvfvELBQYGOnx8/qfuv/9+ffDBB1q9erXatm2r1157TTNmzNDw4cPLtQ6gImymMm4mAxbSs2dPdezYUbGxsc4uBQBQQVwBAgAAlkMAAiysTZs2Dh9z/ul2/SPSzrZr165ia3TWV1r079+/2HpuvL3nDNXxPQOqG26BARb23XffFfvQr5eXl8Pf/3GWixcvKjMzs9j999133y2s5prMzExdvHixyH2enp7y9PS8xRU5qo7vGVDdEIAAAIDlcAsMAABYDgEIAABYDgEIAABYDgEIAABYDgEIAABYDgEIAABYDgEIAABYDgEIAABYzv8H4b1KKEwoze8AAAAASUVORK5CYII=", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["sns.histplot(df, x=\"percent_node_with_valid_version\")"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.9"}}, "nbformat": 4, "nbformat_minor": 5}