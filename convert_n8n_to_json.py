#!/usr/bin/env python3
"""
Script to convert n8n_nodes_list.txt to JSON format.
Each line contains a node name and its versions in the format:
- node_name : version1,version2,version3
"""

import json
import re
import sys
from pathlib import Path


def parse_n8n_nodes_file(file_path):
    """
    Parse the n8n nodes list file and convert to structured data.
    
    Args:
        file_path (str): Path to the n8n_nodes_list.txt file
        
    Returns:
        dict: Structured data with nodes and their versions
    """
    nodes_data = {
        "nodes": [],
        "total_nodes": 0,
        "metadata": {
            "source_file": str(file_path),
            "description": "n8n nodes list with versions"
        }
    }
    
    try:
        with open(file_path, 'r', encoding='utf-8') as file:
            for line_num, line in enumerate(file, 1):
                line = line.strip()
                
                # Skip empty lines
                if not line:
                    continue
                
                # Parse line format: - node_name : versions
                match = re.match(r'^-\s*(.+?)\s*:\s*(.+)$', line)
                if match:
                    node_name = match.group(1).strip()
                    versions_str = match.group(2).strip()
                    
                    # Split versions by comma and clean them
                    versions = [v.strip() for v in versions_str.split(',') if v.strip()]
                    
                    node_data = {
                        "name": node_name,
                        "versions": versions,
                        "version_count": len(versions),
                        "line_number": line_num
                    }
                    
                    nodes_data["nodes"].append(node_data)
                else:
                    print(f"Warning: Could not parse line {line_num}: {line}", file=sys.stderr)
    
    except FileNotFoundError:
        print(f"Error: File '{file_path}' not found.", file=sys.stderr)
        return None
    except Exception as e:
        print(f"Error reading file: {e}", file=sys.stderr)
        return None
    
    nodes_data["total_nodes"] = len(nodes_data["nodes"])
    return nodes_data


def save_json(data, output_path):
    """
    Save data to JSON file with pretty formatting.
    
    Args:
        data (dict): Data to save
        output_path (str): Output file path
    """
    try:
        with open(output_path, 'w', encoding='utf-8') as file:
            json.dump(data, file, indent=2, ensure_ascii=False)
        print(f"Successfully converted to JSON: {output_path}")
    except Exception as e:
        print(f"Error saving JSON file: {e}", file=sys.stderr)


def main():
    """Main function to handle command line arguments and execute conversion."""
    
    # Default input file path
    input_file = "n8n_nodes_list.txt"
    
    # Check if custom input file is provided
    if len(sys.argv) > 1:
        input_file = sys.argv[1]
    
    # Generate output file name
    input_path = Path(input_file)
    output_file = input_path.with_suffix('.json')
    
    print(f"Converting '{input_file}' to '{output_file}'...")
    
    # Parse the file
    nodes_data = parse_n8n_nodes_file(input_file)
    
    if nodes_data is None:
        sys.exit(1)
    
    # Save to JSON
    save_json(nodes_data, output_file)
    
    # Print summary
    print(f"\nConversion Summary:")
    print(f"- Total nodes processed: {nodes_data['total_nodes']}")
    print(f"- Output file: {output_file}")
    
    # Show first few nodes as example
    if nodes_data['nodes']:
        print(f"\nFirst 3 nodes:")
        for i, node in enumerate(nodes_data['nodes'][:3]):
            print(f"  {i+1}. {node['name']} (versions: {', '.join(node['versions'])})")


if __name__ == "__main__":
    main()
