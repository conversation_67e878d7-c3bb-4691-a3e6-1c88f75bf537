{"nodes": [{"name": "@n8n/n8n-nodes-langchain.AI Agent", "versions": ["2"], "version_count": 1, "line_number": 1}, {"name": "@n8n/n8n-nodes-langchain.Summarization Chain", "versions": ["1", "2", "2.1"], "version_count": 3, "line_number": 2}, {"name": "@n8n/n8n-nodes-langchain.agent", "versions": ["1", "1.1", "1.2", "1.3", "1.4", "1.5", "1.6", "1.7", "1.8", "1.9", "2"], "version_count": 11, "line_number": 3}, {"name": "@n8n/n8n-nodes-langchain.allowFileUploads", "versions": ["1", "1.1"], "version_count": 2, "line_number": 4}, {"name": "@n8n/n8n-nodes-langchain.chainLlm", "versions": ["1", "1.1", "1.2", "1.3", "1.4", "1.5", "1.6", "1.7"], "version_count": 8, "line_number": 5}, {"name": "@n8n/n8n-nodes-langchain.chainRetrievalQa", "versions": ["1", "1.1", "1.2", "1.3", "1.4", "1.5", "1.6"], "version_count": 7, "line_number": 6}, {"name": "@n8n/n8n-nodes-langchain.chainSummarization", "versions": ["1", "2", "2.1"], "version_count": 3, "line_number": 7}, {"name": "@n8n/n8n-nodes-langchain.code", "versions": ["1"], "version_count": 1, "line_number": 8}, {"name": "@n8n/n8n-nodes-langchain.documentBinaryInputLoader", "versions": ["1"], "version_count": 1, "line_number": 9}, {"name": "@n8n/n8n-nodes-langchain.documentDefaultDataLoader", "versions": ["1", "1.1"], "version_count": 2, "line_number": 10}, {"name": "@n8n/n8n-nodes-langchain.documentGithubLoader", "versions": ["1", "1.1"], "version_count": 2, "line_number": 11}, {"name": "@n8n/n8n-nodes-langchain.documentJsonInputLoader", "versions": ["1"], "version_count": 1, "line_number": 12}, {"name": "@n8n/n8n-nodes-langchain.embeddingDimensions", "versions": ["1"], "version_count": 1, "line_number": 13}, {"name": "@n8n/n8n-nodes-langchain.embeddingsAwsBedrock", "versions": ["1"], "version_count": 1, "line_number": 14}, {"name": "@n8n/n8n-nodes-langchain.embeddingsAzureOpenAi", "versions": ["1"], "version_count": 1, "line_number": 15}, {"name": "@n8n/n8n-nodes-langchain.embeddingsCohere", "versions": ["1"], "version_count": 1, "line_number": 16}, {"name": "@n8n/n8n-nodes-langchain.embeddingsGoogleGemini", "versions": ["1"], "version_count": 1, "line_number": 17}, {"name": "@n8n/n8n-nodes-langchain.embeddingsGoogleVertex", "versions": ["1"], "version_count": 1, "line_number": 18}, {"name": "@n8n/n8n-nodes-langchain.embeddingsHuggingFaceInference", "versions": ["1"], "version_count": 1, "line_number": 19}, {"name": "@n8n/n8n-nodes-langchain.embeddingsMistralCloud", "versions": ["1"], "version_count": 1, "line_number": 20}, {"name": "@n8n/n8n-nodes-langchain.embeddingsOllama", "versions": ["1"], "version_count": 1, "line_number": 21}, {"name": "@n8n/n8n-nodes-langchain.informationExtractor", "versions": ["1", "1.1", "1.2"], "version_count": 3, "line_number": 22}, {"name": "@n8n/n8n-nodes-langchain.lmChatAwsBedrock", "versions": ["1"], "version_count": 1, "line_number": 23}, {"name": "@n8n/n8n-nodes-langchain.lmChatAzureOpenAi", "versions": ["1"], "version_count": 1, "line_number": 24}, {"name": "@n8n/n8n-nodes-langchain.lmChatDeepSeek", "versions": ["1"], "version_count": 1, "line_number": 25}, {"name": "@n8n/n8n-nodes-langchain.lmChatGoogleGemini", "versions": ["1"], "version_count": 1, "line_number": 26}, {"name": "@n8n/n8n-nodes-langchain.lmChatGoogleVertex", "versions": ["1"], "version_count": 1, "line_number": 27}, {"name": "@n8n/n8n-nodes-langchain.lmChatGroq", "versions": ["1"], "version_count": 1, "line_number": 28}, {"name": "@n8n/n8n-nodes-langchain.lmChatMistralCloud", "versions": ["1"], "version_count": 1, "line_number": 29}, {"name": "@n8n/n8n-nodes-langchain.lmChatOllama", "versions": ["1"], "version_count": 1, "line_number": 30}, {"name": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "versions": ["1", "1.1", "1.2"], "version_count": 3, "line_number": 31}, {"name": "@n8n/n8n-nodes-langchain.lmChatOpenRouter", "versions": ["1"], "version_count": 1, "line_number": 32}, {"name": "@n8n/n8n-nodes-langchain.lmChatXAiGrok", "versions": ["1"], "version_count": 1, "line_number": 33}, {"name": "@n8n/n8n-nodes-langchain.lmCohere", "versions": ["1"], "version_count": 1, "line_number": 34}, {"name": "@n8n/n8n-nodes-langchain.lmOllama", "versions": ["1"], "version_count": 1, "line_number": 35}, {"name": "@n8n/n8n-nodes-langchain.lmOpenAi", "versions": ["1"], "version_count": 1, "line_number": 36}, {"name": "@n8n/n8n-nodes-langchain.lmOpenHuggingFaceInference", "versions": ["1"], "version_count": 1, "line_number": 37}, {"name": "@n8n/n8n-nodes-langchain.manualChatTrigger", "versions": ["1", "1.1"], "version_count": 2, "line_number": 38}, {"name": "@n8n/n8n-nodes-langchain.mcpClientTool", "versions": ["1"], "version_count": 1, "line_number": 39}, {"name": "@n8n/n8n-nodes-langchain.mcpTrigger", "versions": ["1", "1.1", "2"], "version_count": 3, "line_number": 40}, {"name": "@n8n/n8n-nodes-langchain.memoryBufferWindow", "versions": ["1", "1.1", "1.2", "1.3"], "version_count": 4, "line_number": 41}, {"name": "@n8n/n8n-nodes-langchain.memoryChatRetriever", "versions": ["1"], "version_count": 1, "line_number": 42}, {"name": "@n8n/n8n-nodes-langchain.memoryManager", "versions": ["1", "1.1"], "version_count": 2, "line_number": 43}, {"name": "@n8n/n8n-nodes-langchain.memoryMongoDbChat", "versions": ["1"], "version_count": 1, "line_number": 44}, {"name": "@n8n/n8n-nodes-langchain.memoryMotorhead", "versions": ["1", "1.1", "1.2", "1.3"], "version_count": 4, "line_number": 45}, {"name": "@n8n/n8n-nodes-langchain.memoryPostgresChat", "versions": ["1", "1.1", "1.2", "1.3"], "version_count": 4, "line_number": 46}, {"name": "@n8n/n8n-nodes-langchain.memoryRedisChat", "versions": ["1", "1.1", "1.2", "1.3", "1.4", "1.5"], "version_count": 6, "line_number": 47}, {"name": "@n8n/n8n-nodes-langchain.memoryXata", "versions": ["1", "1.1", "1.2", "1.3", "1.4"], "version_count": 5, "line_number": 48}, {"name": "@n8n/n8n-nodes-langchain.memoryZep", "versions": ["1", "1.1", "1.2", "1.3"], "version_count": 4, "line_number": 49}, {"name": "@n8n/n8n-nodes-langchain.model", "versions": ["1", "1.1", "1.2", "1.3"], "version_count": 4, "line_number": 50}, {"name": "@n8n/n8n-nodes-langchain.mongoCollection", "versions": ["1"], "version_count": 1, "line_number": 51}, {"name": "@n8n/n8n-nodes-langchain.notice", "versions": ["1"], "version_count": 1, "line_number": 52}, {"name": "@n8n/n8n-nodes-langchain.openAiAssistant", "versions": ["1", "1.1"], "version_count": 2, "line_number": 53}, {"name": "@n8n/n8n-nodes-langchain.options", "versions": ["1"], "version_count": 1, "line_number": 54}, {"name": "@n8n/n8n-nodes-langchain.outputParserAutofixing", "versions": ["1"], "version_count": 1, "line_number": 55}, {"name": "@n8n/n8n-nodes-langchain.outputParserItemList", "versions": ["1"], "version_count": 1, "line_number": 56}, {"name": "@n8n/n8n-nodes-langchain.outputParserStructured", "versions": ["1", "1.1", "1.2", "1.3"], "version_count": 4, "line_number": 57}, {"name": "@n8n/n8n-nodes-langchain.pineconeNamespace", "versions": ["1"], "version_count": 1, "line_number": 58}, {"name": "@n8n/n8n-nodes-langchain.queryName", "versions": ["1"], "version_count": 1, "line_number": 59}, {"name": "@n8n/n8n-nodes-langchain.rerankerCohere", "versions": ["1"], "version_count": 1, "line_number": 60}, {"name": "@n8n/n8n-nodes-langchain.retrieverContextualCompression", "versions": ["1"], "version_count": 1, "line_number": 61}, {"name": "@n8n/n8n-nodes-langchain.retrieverMultiQuery", "versions": ["1"], "version_count": 1, "line_number": 62}, {"name": "@n8n/n8n-nodes-langchain.retrieverVectorStore", "versions": ["1"], "version_count": 1, "line_number": 63}, {"name": "@n8n/n8n-nodes-langchain.retrieverWorkflow", "versions": ["1", "1.1"], "version_count": 2, "line_number": 64}, {"name": "@n8n/n8n-nodes-langchain.sentimentAnalysis", "versions": ["1", "1.1"], "version_count": 2, "line_number": 65}, {"name": "@n8n/n8n-nodes-langchain.tableName", "versions": ["1"], "version_count": 1, "line_number": 66}, {"name": "@n8n/n8n-nodes-langchain.textClassifier", "versions": ["1", "1.1"], "version_count": 2, "line_number": 67}, {"name": "@n8n/n8n-nodes-langchain.textSplitterCharacterTextSplitter", "versions": ["1"], "version_count": 1, "line_number": 68}, {"name": "@n8n/n8n-nodes-langchain.textSplitterRecursiveCharacterTextSplitter", "versions": ["1"], "version_count": 1, "line_number": 69}, {"name": "@n8n/n8n-nodes-langchain.textSplitterTokenSplitter", "versions": ["1"], "version_count": 1, "line_number": 70}, {"name": "@n8n/n8n-nodes-langchain.toolCalculator", "versions": ["1"], "version_count": 1, "line_number": 71}, {"name": "@n8n/n8n-nodes-langchain.toolCode", "versions": ["1", "1.1", "1.2", "1.3"], "version_count": 4, "line_number": 72}, {"name": "@n8n/n8n-nodes-langchain.toolExecutor", "versions": ["1"], "version_count": 1, "line_number": 73}, {"name": "@n8n/n8n-nodes-langchain.toolHttpRequest", "versions": ["1", "1.1"], "version_count": 2, "line_number": 74}, {"name": "@n8n/n8n-nodes-langchain.toolSearXng", "versions": ["1"], "version_count": 1, "line_number": 75}, {"name": "@n8n/n8n-nodes-langchain.toolSerpApi", "versions": ["1"], "version_count": 1, "line_number": 76}, {"name": "@n8n/n8n-nodes-langchain.toolThink", "versions": ["1"], "version_count": 1, "line_number": 77}, {"name": "@n8n/n8n-nodes-langchain.toolVectorStore", "versions": ["1", "1.1"], "version_count": 2, "line_number": 78}, {"name": "@n8n/n8n-nodes-langchain.toolWikipedia", "versions": ["1"], "version_count": 1, "line_number": 79}, {"name": "@n8n/n8n-nodes-langchain.toolWolframAlpha", "versions": ["1"], "version_count": 1, "line_number": 80}, {"name": "@n8n/n8n-nodes-langchain.toolWorkflow", "versions": ["1", "1.1", "1.2", "1.3", "2", "2.1", "2.2"], "version_count": 7, "line_number": 81}, {"name": "@n8n/n8n-nodes-langchain.vectorStoreInMemoryInsert", "versions": ["1"], "version_count": 1, "line_number": 82}, {"name": "@n8n/n8n-nodes-langchain.vectorStoreInMemoryLoad", "versions": ["1"], "version_count": 1, "line_number": 83}, {"name": "@n8n/n8n-nodes-langchain.vectorStorePineconeInsert", "versions": ["1"], "version_count": 1, "line_number": 84}, {"name": "@n8n/n8n-nodes-langchain.vectorStorePineconeLoad", "versions": ["1"], "version_count": 1, "line_number": 85}, {"name": "@n8n/n8n-nodes-langchain.vectorStoreSupabaseInsert", "versions": ["1"], "version_count": 1, "line_number": 86}, {"name": "@n8n/n8n-nodes-langchain.vectorStoreSupabaseLoad", "versions": ["1"], "version_count": 1, "line_number": 87}, {"name": "@n8n/n8n-nodes-langchain.vectorStoreZepInsert", "versions": ["1"], "version_count": 1, "line_number": 88}, {"name": "@n8n/n8n-nodes-langchain.vectorStoreZepLoad", "versions": ["1"], "version_count": 1, "line_number": 89}, {"name": "n8n-nodes-base.Activity", "versions": ["1", "1.1"], "version_count": 2, "line_number": 90}, {"name": "n8n-nodes-base.Blur", "versions": ["1"], "version_count": 1, "line_number": 91}, {"name": "n8n-nodes-base.Brandfetch", "versions": ["1"], "version_count": 1, "line_number": 92}, {"name": "n8n-nodes-base.Comment", "versions": ["1"], "version_count": 1, "line_number": 93}, {"name": "n8n-nodes-base.Date & Time", "versions": ["2"], "version_count": 1, "line_number": 94}, {"name": "n8n-nodes-base.<PERSON><PERSON><PERSON>", "versions": ["1"], "version_count": 1, "line_number": 95}, {"name": "n8n-nodes-base.Filter", "versions": ["1", "2", "2.1", "2.2"], "version_count": 4, "line_number": 96}, {"name": "n8n-nodes-base.HTTP Request", "versions": ["1", "2", "3", "4", "4.1", "4.2"], "version_count": 6, "line_number": 97}, {"name": "n8n-nodes-base.HubSpot", "versions": ["1", "2", "2.1"], "version_count": 3, "line_number": 98}, {"name": "n8n-nodes-base.If", "versions": ["1", "2", "2.1", "2.2"], "version_count": 4, "line_number": 99}, {"name": "n8n-nodes-base.Item Lists", "versions": ["1", "2", "2.1", "2.2"], "version_count": 4, "line_number": 100}, {"name": "n8n-nodes-base.<PERSON>", "versions": ["1"], "version_count": 1, "line_number": 101}, {"name": "n8n-nodes-base.Merge", "versions": ["1", "2", "2.1"], "version_count": 3, "line_number": 102}, {"name": "n8n-nodes-base.SeaTable Trigger", "versions": ["1", "2"], "version_count": 2, "line_number": 103}, {"name": "n8n-nodes-base.Slack", "versions": ["1", "2", "2.1", "2.2", "2.3"], "version_count": 5, "line_number": 104}, {"name": "n8n-nodes-base.Spreadsheet File", "versions": ["1", "2"], "version_count": 2, "line_number": 105}, {"name": "n8n-nodes-base.Switch", "versions": ["1", "2", "3", "3.1", "3.2"], "version_count": 5, "line_number": 106}, {"name": "n8n-nodes-base.Twitter", "versions": ["1"], "version_count": 1, "line_number": 107}, {"name": "n8n-nodes-base.Webflow", "versions": ["1"], "version_count": 1, "line_number": 108}, {"name": "n8n-nodes-base.WorkflowTrigger", "versions": ["1"], "version_count": 1, "line_number": 109}, {"name": "n8n-nodes-base.X", "versions": ["2"], "version_count": 1, "line_number": 110}, {"name": "n8n-nodes-base.actionNetwork", "versions": ["1"], "version_count": 1, "line_number": 111}, {"name": "n8n-nodes-base.activeCampaign", "versions": ["1"], "version_count": 1, "line_number": 112}, {"name": "n8n-nodes-base.activeCampaignTrigger", "versions": ["1"], "version_count": 1, "line_number": 113}, {"name": "n8n-nodes-base.acuitySchedulingTrigger", "versions": ["1"], "version_count": 1, "line_number": 114}, {"name": "n8n-nodes-base.adalo", "versions": ["1"], "version_count": 1, "line_number": 115}, {"name": "n8n-nodes-base.affinity", "versions": ["1"], "version_count": 1, "line_number": 116}, {"name": "n8n-nodes-base.affinityTrigger", "versions": ["1"], "version_count": 1, "line_number": 117}, {"name": "n8n-nodes-base.aggregate", "versions": ["1"], "version_count": 1, "line_number": 118}, {"name": "n8n-nodes-base.agileCrm", "versions": ["1"], "version_count": 1, "line_number": 119}, {"name": "n8n-nodes-base.aiTransform", "versions": ["1"], "version_count": 1, "line_number": 120}, {"name": "n8n-nodes-base.airtable", "versions": ["1", "2", "2.1"], "version_count": 3, "line_number": 121}, {"name": "n8n-nodes-base.airtableTrigger", "versions": ["1"], "version_count": 1, "line_number": 122}, {"name": "n8n-nodes-base.airtop", "versions": ["1"], "version_count": 1, "line_number": 123}, {"name": "n8n-nodes-base.amount", "versions": ["1", "1.1"], "version_count": 2, "line_number": 124}, {"name": "n8n-nodes-base.amqp", "versions": ["1"], "version_count": 1, "line_number": 125}, {"name": "n8n-nodes-base.amqpTrigger", "versions": ["1"], "version_count": 1, "line_number": 126}, {"name": "n8n-nodes-base.apiTemplateIo", "versions": ["1"], "version_count": 1, "line_number": 127}, {"name": "n8n-nodes-base.asana", "versions": ["1"], "version_count": 1, "line_number": 128}, {"name": "n8n-nodes-base.asana<PERSON><PERSON><PERSON>", "versions": ["1"], "version_count": 1, "line_number": 129}, {"name": "n8n-nodes-base.automizy", "versions": ["1"], "version_count": 1, "line_number": 130}, {"name": "n8n-nodes-base.autopilot", "versions": ["1"], "version_count": 1, "line_number": 131}, {"name": "n8n-nodes-base.autopilotTrigger", "versions": ["1"], "version_count": 1, "line_number": 132}, {"name": "n8n-nodes-base.awsCertificateManager", "versions": ["1"], "version_count": 1, "line_number": 133}, {"name": "n8n-nodes-base.awsCognito", "versions": ["1"], "version_count": 1, "line_number": 134}, {"name": "n8n-nodes-base.awsComprehend", "versions": ["1"], "version_count": 1, "line_number": 135}, {"name": "n8n-nodes-base.awsDynamoDb", "versions": ["1"], "version_count": 1, "line_number": 136}, {"name": "n8n-nodes-base.awsElb", "versions": ["1"], "version_count": 1, "line_number": 137}, {"name": "n8n-nodes-base.awsIam", "versions": ["1"], "version_count": 1, "line_number": 138}, {"name": "n8n-nodes-base.awsLambda", "versions": ["1"], "version_count": 1, "line_number": 139}, {"name": "n8n-nodes-base.awsRekognition", "versions": ["1"], "version_count": 1, "line_number": 140}, {"name": "n8n-nodes-base.awsS3", "versions": ["1", "2"], "version_count": 2, "line_number": 141}, {"name": "n8n-nodes-base.awsSes", "versions": ["1"], "version_count": 1, "line_number": 142}, {"name": "n8n-nodes-base.awsSns", "versions": ["1"], "version_count": 1, "line_number": 143}, {"name": "n8n-nodes-base.awsSnsTrigger", "versions": ["1"], "version_count": 1, "line_number": 144}, {"name": "n8n-nodes-base.awsSqs", "versions": ["1"], "version_count": 1, "line_number": 145}, {"name": "n8n-nodes-base.awsTextract", "versions": ["1"], "version_count": 1, "line_number": 146}, {"name": "n8n-nodes-base.awsTranscribe", "versions": ["1"], "version_count": 1, "line_number": 147}, {"name": "n8n-nodes-base.awsiam", "versions": ["1"], "version_count": 1, "line_number": 148}, {"name": "n8n-nodes-base.azureCosmosDb", "versions": ["1"], "version_count": 1, "line_number": 149}, {"name": "n8n-nodes-base.azureStorage", "versions": ["1"], "version_count": 1, "line_number": 150}, {"name": "n8n-nodes-base.bambooHr", "versions": ["1"], "version_count": 1, "line_number": 151}, {"name": "n8n-nodes-base.bannerbear", "versions": ["1"], "version_count": 1, "line_number": 152}, {"name": "n8n-nodes-base.baserow", "versions": ["1"], "version_count": 1, "line_number": 153}, {"name": "n8n-nodes-base.beeminder", "versions": ["1"], "version_count": 1, "line_number": 154}, {"name": "n8n-nodes-base.bitbucketTrigger", "versions": ["1"], "version_count": 1, "line_number": 155}, {"name": "n8n-nodes-base.bitly", "versions": ["1"], "version_count": 1, "line_number": 156}, {"name": "n8n-nodes-base.bitwarden", "versions": ["1"], "version_count": 1, "line_number": 157}, {"name": "n8n-nodes-base.box", "versions": ["1"], "version_count": 1, "line_number": 158}, {"name": "n8n-nodes-base.boxTrigger", "versions": ["1"], "version_count": 1, "line_number": 159}, {"name": "n8n-nodes-base.brevo", "versions": ["1"], "version_count": 1, "line_number": 160}, {"name": "n8n-nodes-base.brevoTrigger", "versions": ["1"], "version_count": 1, "line_number": 161}, {"name": "n8n-nodes-base.bubble", "versions": ["1"], "version_count": 1, "line_number": 162}, {"name": "n8n-nodes-base.calTrigger", "versions": ["1", "2"], "version_count": 2, "line_number": 163}, {"name": "n8n-nodes-base.calendlyTrigger", "versions": ["1"], "version_count": 1, "line_number": 164}, {"name": "n8n-nodes-base.chargebee", "versions": ["1"], "version_count": 1, "line_number": 165}, {"name": "n8n-nodes-base.chargebeeTrigger", "versions": ["1"], "version_count": 1, "line_number": 166}, {"name": "n8n-nodes-base.circleCi", "versions": ["1"], "version_count": 1, "line_number": 167}, {"name": "n8n-nodes-base.ciscoWebex", "versions": ["1"], "version_count": 1, "line_number": 168}, {"name": "n8n-nodes-base.ciscoWebexTrigger", "versions": ["1"], "version_count": 1, "line_number": 169}, {"name": "n8n-nodes-base.citrixAdc", "versions": ["1"], "version_count": 1, "line_number": 170}, {"name": "n8n-nodes-base.clearbit", "versions": ["1"], "version_count": 1, "line_number": 171}, {"name": "n8n-nodes-base.clickUp", "versions": ["1"], "version_count": 1, "line_number": 172}, {"name": "n8n-nodes-base.clickUpTrigger", "versions": ["1"], "version_count": 1, "line_number": 173}, {"name": "n8n-nodes-base.clockify", "versions": ["1"], "version_count": 1, "line_number": 174}, {"name": "n8n-nodes-base.clockifyTrigger", "versions": ["1"], "version_count": 1, "line_number": 175}, {"name": "n8n-nodes-base.cloudflare", "versions": ["1"], "version_count": 1, "line_number": 176}, {"name": "n8n-nodes-base.cockpit", "versions": ["1"], "version_count": 1, "line_number": 177}, {"name": "n8n-nodes-base.coda", "versions": ["1", "1.1"], "version_count": 2, "line_number": 178}, {"name": "n8n-nodes-base.code", "versions": ["1", "2"], "version_count": 2, "line_number": 179}, {"name": "n8n-nodes-base.coinGecko", "versions": ["1"], "version_count": 1, "line_number": 180}, {"name": "n8n-nodes-base.compareDatasets", "versions": ["1", "2", "2.1", "2.2", "2.3"], "version_count": 5, "line_number": 181}, {"name": "n8n-nodes-base.compression", "versions": ["1", "1.1"], "version_count": 2, "line_number": 182}, {"name": "n8n-nodes-base.contentful", "versions": ["1"], "version_count": 1, "line_number": 183}, {"name": "n8n-nodes-base.convertKit", "versions": ["1"], "version_count": 1, "line_number": 184}, {"name": "n8n-nodes-base.convertKitTrigger", "versions": ["1"], "version_count": 1, "line_number": 185}, {"name": "n8n-nodes-base.convertToFile", "versions": ["1", "1.1"], "version_count": 2, "line_number": 186}, {"name": "n8n-nodes-base.copper", "versions": ["1"], "version_count": 1, "line_number": 187}, {"name": "n8n-nodes-base.copperTrigger", "versions": ["1"], "version_count": 1, "line_number": 188}, {"name": "n8n-nodes-base.cortex", "versions": ["1"], "version_count": 1, "line_number": 189}, {"name": "n8n-nodes-base.crateDb", "versions": ["1"], "version_count": 1, "line_number": 190}, {"name": "n8n-nodes-base.credentials", "versions": ["1"], "version_count": 1, "line_number": 191}, {"name": "n8n-nodes-base.cron", "versions": ["1"], "version_count": 1, "line_number": 192}, {"name": "n8n-nodes-base.crowdDev", "versions": ["1"], "version_count": 1, "line_number": 193}, {"name": "n8n-nodes-base.crowdDevTrigger", "versions": ["1"], "version_count": 1, "line_number": 194}, {"name": "n8n-nodes-base.crypto", "versions": ["1"], "version_count": 1, "line_number": 195}, {"name": "n8n-nodes-base.customerIo", "versions": ["1"], "version_count": 1, "line_number": 196}, {"name": "n8n-nodes-base.customerIoTrigger", "versions": ["1"], "version_count": 1, "line_number": 197}, {"name": "n8n-nodes-base.dateTime", "versions": ["1", "2"], "version_count": 2, "line_number": 198}, {"name": "n8n-nodes-base.debugHelper", "versions": ["1"], "version_count": 1, "line_number": 199}, {"name": "n8n-nodes-base.debugh<PERSON><PERSON>", "versions": ["1"], "version_count": 1, "line_number": 200}, {"name": "n8n-nodes-base.deepL", "versions": ["1"], "version_count": 1, "line_number": 201}, {"name": "n8n-nodes-base.demio", "versions": ["1"], "version_count": 1, "line_number": 202}, {"name": "n8n-nodes-base.dhl", "versions": ["1"], "version_count": 1, "line_number": 203}, {"name": "n8n-nodes-base.discord", "versions": ["1", "2"], "version_count": 2, "line_number": 204}, {"name": "n8n-nodes-base.discourse", "versions": ["1"], "version_count": 1, "line_number": 205}, {"name": "n8n-nodes-base.disqus", "versions": ["1"], "version_count": 1, "line_number": 206}, {"name": "n8n-nodes-base.documentId", "versions": ["1"], "version_count": 1, "line_number": 207}, {"name": "n8n-nodes-base.drift", "versions": ["1"], "version_count": 1, "line_number": 208}, {"name": "n8n-nodes-base.dropbox", "versions": ["1"], "version_count": 1, "line_number": 209}, {"name": "n8n-nodes-base.dropcontact", "versions": ["1"], "version_count": 1, "line_number": 210}, {"name": "n8n-nodes-base.e2eTest", "versions": ["1"], "version_count": 1, "line_number": 211}, {"name": "n8n-nodes-base.editImage", "versions": ["1"], "version_count": 1, "line_number": 212}, {"name": "n8n-nodes-base.egoi", "versions": ["1"], "version_count": 1, "line_number": 213}, {"name": "n8n-nodes-base.elasticSecurity", "versions": ["1"], "version_count": 1, "line_number": 214}, {"name": "n8n-nodes-base.elasticsearch", "versions": ["1"], "version_count": 1, "line_number": 215}, {"name": "n8n-nodes-base.emailReadImap", "versions": ["1", "2"], "version_count": 2, "line_number": 216}, {"name": "n8n-nodes-base.emailSend", "versions": ["1", "2", "2.1"], "version_count": 3, "line_number": 217}, {"name": "n8n-nodes-base.emelia", "versions": ["1"], "version_count": 1, "line_number": 218}, {"name": "n8n-nodes-base.em<PERSON><PERSON>", "versions": ["1"], "version_count": 1, "line_number": 219}, {"name": "n8n-nodes-base.erpNext", "versions": ["1"], "version_count": 1, "line_number": 220}, {"name": "n8n-nodes-base.errorTrigger", "versions": ["1"], "version_count": 1, "line_number": 221}, {"name": "n8n-nodes-base.eventbriteTrigger", "versions": ["1"], "version_count": 1, "line_number": 222}, {"name": "n8n-nodes-base.executeCommand", "versions": ["1"], "version_count": 1, "line_number": 223}, {"name": "n8n-nodes-base.executeWorkflow", "versions": ["1", "1.1", "1.2"], "version_count": 3, "line_number": 224}, {"name": "n8n-nodes-base.executeWorkflowTrigger", "versions": ["1", "1.1"], "version_count": 2, "line_number": 225}, {"name": "n8n-nodes-base.executionData", "versions": ["1"], "version_count": 1, "line_number": 226}, {"name": "n8n-nodes-base.extractFromFile", "versions": ["1"], "version_count": 1, "line_number": 227}, {"name": "n8n-nodes-base.extractionValues", "versions": ["1", "1.1", "1.2"], "version_count": 3, "line_number": 228}, {"name": "n8n-nodes-base.facebookGraphApi", "versions": ["1"], "version_count": 1, "line_number": 229}, {"name": "n8n-nodes-base.facebookLeadAdsTrigger", "versions": ["1"], "version_count": 1, "line_number": 230}, {"name": "n8n-nodes-base.facebookTrigger", "versions": ["1"], "version_count": 1, "line_number": 231}, {"name": "n8n-nodes-base.figmaTrigger", "versions": ["1"], "version_count": 1, "line_number": 232}, {"name": "n8n-nodes-base.filemaker", "versions": ["1"], "version_count": 1, "line_number": 233}, {"name": "n8n-nodes-base.filter", "versions": ["1", "2", "2.1", "2.2"], "version_count": 4, "line_number": 234}, {"name": "n8n-nodes-base.flow", "versions": ["1"], "version_count": 1, "line_number": 235}, {"name": "n8n-nodes-base.flowTrigger", "versions": ["1"], "version_count": 1, "line_number": 236}, {"name": "n8n-nodes-base.form", "versions": ["1"], "version_count": 1, "line_number": 237}, {"name": "n8n-nodes-base.formIoTrigger", "versions": ["1"], "version_count": 1, "line_number": 238}, {"name": "n8n-nodes-base.formTrigger", "versions": ["1", "2", "2.1", "2.2"], "version_count": 4, "line_number": 239}, {"name": "n8n-nodes-base.formstackTrigger", "versions": ["1"], "version_count": 1, "line_number": 240}, {"name": "n8n-nodes-base.freshdesk", "versions": ["1"], "version_count": 1, "line_number": 241}, {"name": "n8n-nodes-base.freshservice", "versions": ["1"], "version_count": 1, "line_number": 242}, {"name": "n8n-nodes-base.freshworksCrm", "versions": ["1"], "version_count": 1, "line_number": 243}, {"name": "n8n-nodes-base.ftp", "versions": ["1"], "version_count": 1, "line_number": 244}, {"name": "n8n-nodes-base.function", "versions": ["1"], "version_count": 1, "line_number": 245}, {"name": "n8n-nodes-base.functionItem", "versions": ["1"], "version_count": 1, "line_number": 246}, {"name": "n8n-nodes-base.gSuiteAdmin", "versions": ["1"], "version_count": 1, "line_number": 247}, {"name": "n8n-nodes-base.getResponse", "versions": ["1"], "version_count": 1, "line_number": 248}, {"name": "n8n-nodes-base.getResponseTrigger", "versions": ["1"], "version_count": 1, "line_number": 249}, {"name": "n8n-nodes-base.ghost", "versions": ["1"], "version_count": 1, "line_number": 250}, {"name": "n8n-nodes-base.git", "versions": ["1"], "version_count": 1, "line_number": 251}, {"name": "n8n-nodes-base.github", "versions": ["1", "1.1"], "version_count": 2, "line_number": 252}, {"name": "n8n-nodes-base.githubTrigger", "versions": ["1"], "version_count": 1, "line_number": 253}, {"name": "n8n-nodes-base.gitlab", "versions": ["1"], "version_count": 1, "line_number": 254}, {"name": "n8n-nodes-base.gitlabTrigger", "versions": ["1"], "version_count": 1, "line_number": 255}, {"name": "n8n-nodes-base.gmail", "versions": ["1", "2", "2.1"], "version_count": 3, "line_number": 256}, {"name": "n8n-nodes-base.gmailTrigger", "versions": ["1", "1.1", "1.2"], "version_count": 3, "line_number": 257}, {"name": "n8n-nodes-base.goToWebinar", "versions": ["1"], "version_count": 1, "line_number": 258}, {"name": "n8n-nodes-base.gong", "versions": ["1"], "version_count": 1, "line_number": 259}, {"name": "n8n-nodes-base.googleAds", "versions": ["1"], "version_count": 1, "line_number": 260}, {"name": "n8n-nodes-base.googleAnalytics", "versions": ["1", "2"], "version_count": 2, "line_number": 261}, {"name": "n8n-nodes-base.googleBigQuery", "versions": ["1", "2", "2.1"], "version_count": 3, "line_number": 262}, {"name": "n8n-nodes-base.googleBooks", "versions": ["1", "2"], "version_count": 2, "line_number": 263}, {"name": "n8n-nodes-base.googleBusinessProfile", "versions": ["1"], "version_count": 1, "line_number": 264}, {"name": "n8n-nodes-base.googleBusinessProfileTrigger", "versions": ["1"], "version_count": 1, "line_number": 265}, {"name": "n8n-nodes-base.googleCalendar", "versions": ["1", "1.1", "1.2", "1.3"], "version_count": 4, "line_number": 266}, {"name": "n8n-nodes-base.googleCalendarTrigger", "versions": ["1"], "version_count": 1, "line_number": 267}, {"name": "n8n-nodes-base.googleChat", "versions": ["1"], "version_count": 1, "line_number": 268}, {"name": "n8n-nodes-base.googleCloudNaturalLanguage", "versions": ["1"], "version_count": 1, "line_number": 269}, {"name": "n8n-nodes-base.googleCloudStorage", "versions": ["1"], "version_count": 1, "line_number": 270}, {"name": "n8n-nodes-base.googleContacts", "versions": ["1"], "version_count": 1, "line_number": 271}, {"name": "n8n-nodes-base.googleDocs", "versions": ["1", "2"], "version_count": 2, "line_number": 272}, {"name": "n8n-nodes-base.googleDrive", "versions": ["1", "2", "3"], "version_count": 3, "line_number": 273}, {"name": "n8n-nodes-base.googleDriveTrigger", "versions": ["1"], "version_count": 1, "line_number": 274}, {"name": "n8n-nodes-base.googleFirebaseCloudFirestore", "versions": ["1", "1.1"], "version_count": 2, "line_number": 275}, {"name": "n8n-nodes-base.googleFirebaseRealtimeDatabase", "versions": ["1"], "version_count": 1, "line_number": 276}, {"name": "n8n-nodes-base.googlePerspective", "versions": ["1"], "version_count": 1, "line_number": 277}, {"name": "n8n-nodes-base.googleSheets", "versions": ["1", "2", "3", "4", "4.1", "4.2", "4.3", "4.4", "4.5", "4.6"], "version_count": 10, "line_number": 278}, {"name": "n8n-nodes-base.googleSheetsTrigger", "versions": ["1"], "version_count": 1, "line_number": 279}, {"name": "n8n-nodes-base.googleSlides", "versions": ["1", "2"], "version_count": 2, "line_number": 280}, {"name": "n8n-nodes-base.googleTasks", "versions": ["1"], "version_count": 1, "line_number": 281}, {"name": "n8n-nodes-base.googleTranslate", "versions": ["1", "2"], "version_count": 2, "line_number": 282}, {"name": "n8n-nodes-base.gotify", "versions": ["1"], "version_count": 1, "line_number": 283}, {"name": "n8n-nodes-base.grafana", "versions": ["1"], "version_count": 1, "line_number": 284}, {"name": "n8n-nodes-base.graphql", "versions": ["1", "1.1"], "version_count": 2, "line_number": 285}, {"name": "n8n-nodes-base.grist", "versions": ["1"], "version_count": 1, "line_number": 286}, {"name": "n8n-nodes-base.gumroadTrigger", "versions": ["1"], "version_count": 1, "line_number": 287}, {"name": "n8n-nodes-base.hackerNews", "versions": ["1"], "version_count": 1, "line_number": 288}, {"name": "n8n-nodes-base.haloPSA", "versions": ["1"], "version_count": 1, "line_number": 289}, {"name": "n8n-nodes-base.harvest", "versions": ["1"], "version_count": 1, "line_number": 290}, {"name": "n8n-nodes-base.helpScout", "versions": ["1"], "version_count": 1, "line_number": 291}, {"name": "n8n-nodes-base.helpScoutTrigger", "versions": ["1"], "version_count": 1, "line_number": 292}, {"name": "n8n-nodes-base.highLevel", "versions": ["1", "2"], "version_count": 2, "line_number": 293}, {"name": "n8n-nodes-base.homeAssistant", "versions": ["1"], "version_count": 1, "line_number": 294}, {"name": "n8n-nodes-base.html", "versions": ["1"], "version_count": 1, "line_number": 295}, {"name": "n8n-nodes-base.htmlExtract", "versions": ["1"], "version_count": 1, "line_number": 296}, {"name": "n8n-nodes-base.httpRequest", "versions": ["1", "2", "3", "4", "4.1", "4.2"], "version_count": 6, "line_number": 297}, {"name": "n8n-nodes-base.hubspot", "versions": ["1", "2", "2.1"], "version_count": 3, "line_number": 298}, {"name": "n8n-nodes-base.hubspotTrigger", "versions": ["1"], "version_count": 1, "line_number": 299}, {"name": "n8n-nodes-base.humanticAi", "versions": ["1"], "version_count": 1, "line_number": 300}, {"name": "n8n-nodes-base.hunter", "versions": ["1"], "version_count": 1, "line_number": 301}, {"name": "n8n-nodes-base.iCal", "versions": ["1"], "version_count": 1, "line_number": 302}, {"name": "n8n-nodes-base.if", "versions": ["1", "2", "2.1", "2.2"], "version_count": 4, "line_number": 303}, {"name": "n8n-nodes-base.intercom", "versions": ["1"], "version_count": 1, "line_number": 304}, {"name": "n8n-nodes-base.interval", "versions": ["1"], "version_count": 1, "line_number": 305}, {"name": "n8n-nodes-base.invoiceNinja", "versions": ["1", "2"], "version_count": 2, "line_number": 306}, {"name": "n8n-nodes-base.invoiceNinjaTrigger", "versions": ["1", "2"], "version_count": 2, "line_number": 307}, {"name": "n8n-nodes-base.itemLists", "versions": ["1", "2", "2.1", "2.2", "3", "3.1"], "version_count": 6, "line_number": 308}, {"name": "n8n-nodes-base.iterable", "versions": ["1"], "version_count": 1, "line_number": 309}, {"name": "n8n-nodes-base.jenkins", "versions": ["1"], "version_count": 1, "line_number": 310}, {"name": "n8n-nodes-base.jina<PERSON>i", "versions": ["1"], "version_count": 1, "line_number": 311}, {"name": "n8n-nodes-base.jira", "versions": ["1"], "version_count": 1, "line_number": 312}, {"name": "n8n-nodes-base.<PERSON>ra<PERSON><PERSON><PERSON>", "versions": ["1", "1.1"], "version_count": 2, "line_number": 313}, {"name": "n8n-nodes-base.jotFormTrigger", "versions": ["1"], "version_count": 1, "line_number": 314}, {"name": "n8n-nodes-base.jwt", "versions": ["1"], "version_count": 1, "line_number": 315}, {"name": "n8n-nodes-base.kafka", "versions": ["1"], "version_count": 1, "line_number": 316}, {"name": "n8n-nodes-base.kafkaTrigger", "versions": ["1", "1.1"], "version_count": 2, "line_number": 317}, {"name": "n8n-nodes-base.keap", "versions": ["1"], "version_count": 1, "line_number": 318}, {"name": "n8n-nodes-base.keapTrigger", "versions": ["1"], "version_count": 1, "line_number": 319}, {"name": "n8n-nodes-base.kitemaker", "versions": ["1"], "version_count": 1, "line_number": 320}, {"name": "n8n-nodes-base.koBoToolbox", "versions": ["1"], "version_count": 1, "line_number": 321}, {"name": "n8n-nodes-base.koBoToolboxTrigger", "versions": ["1"], "version_count": 1, "line_number": 322}, {"name": "n8n-nodes-base.ldap", "versions": ["1"], "version_count": 1, "line_number": 323}, {"name": "n8n-nodes-base.lemlist", "versions": ["1", "2"], "version_count": 2, "line_number": 324}, {"name": "n8n-nodes-base.lemlistTrigger", "versions": ["1"], "version_count": 1, "line_number": 325}, {"name": "n8n-nodes-base.limit", "versions": ["1"], "version_count": 1, "line_number": 326}, {"name": "n8n-nodes-base.limitWaitTime", "versions": ["1"], "version_count": 1, "line_number": 327}, {"name": "n8n-nodes-base.line", "versions": ["1"], "version_count": 1, "line_number": 328}, {"name": "n8n-nodes-base.linear", "versions": ["1"], "version_count": 1, "line_number": 329}, {"name": "n8n-nodes-base.linearTrigger", "versions": ["1"], "version_count": 1, "line_number": 330}, {"name": "n8n-nodes-base.lingvaNex", "versions": ["1"], "version_count": 1, "line_number": 331}, {"name": "n8n-nodes-base.linkedIn", "versions": ["1"], "version_count": 1, "line_number": 332}, {"name": "n8n-nodes-base.localFileTrigger", "versions": ["1"], "version_count": 1, "line_number": 333}, {"name": "n8n-nodes-base.loneScale", "versions": ["1"], "version_count": 1, "line_number": 334}, {"name": "n8n-nodes-base.loneScaleTrigger", "versions": ["1"], "version_count": 1, "line_number": 335}, {"name": "n8n-nodes-base.lonescale", "versions": ["1"], "version_count": 1, "line_number": 336}, {"name": "n8n-nodes-base.lonescaleTrigger", "versions": ["1"], "version_count": 1, "line_number": 337}, {"name": "n8n-nodes-base.magento2", "versions": ["1"], "version_count": 1, "line_number": 338}, {"name": "n8n-nodes-base.mailcheck", "versions": ["1"], "version_count": 1, "line_number": 339}, {"name": "n8n-nodes-base.mailchimp", "versions": ["1"], "version_count": 1, "line_number": 340}, {"name": "n8n-nodes-base.mailchimpTrigger", "versions": ["1"], "version_count": 1, "line_number": 341}, {"name": "n8n-nodes-base.mailerLite", "versions": ["1", "2"], "version_count": 2, "line_number": 342}, {"name": "n8n-nodes-base.mailerLiteTrigger", "versions": ["1", "2"], "version_count": 2, "line_number": 343}, {"name": "n8n-nodes-base.mailgun", "versions": ["1"], "version_count": 1, "line_number": 344}, {"name": "n8n-nodes-base.mailjet", "versions": ["1"], "version_count": 1, "line_number": 345}, {"name": "n8n-nodes-base.mailjetTrigger", "versions": ["1"], "version_count": 1, "line_number": 346}, {"name": "n8n-nodes-base.mandrill", "versions": ["1"], "version_count": 1, "line_number": 347}, {"name": "n8n-nodes-base.manualTrigger", "versions": ["1"], "version_count": 1, "line_number": 348}, {"name": "n8n-nodes-base.markdown", "versions": ["1"], "version_count": 1, "line_number": 349}, {"name": "n8n-nodes-base.marketstack", "versions": ["1"], "version_count": 1, "line_number": 350}, {"name": "n8n-nodes-base.matrix", "versions": ["1"], "version_count": 1, "line_number": 351}, {"name": "n8n-nodes-base.mattermost", "versions": ["1"], "version_count": 1, "line_number": 352}, {"name": "n8n-nodes-base.mautic", "versions": ["1"], "version_count": 1, "line_number": 353}, {"name": "n8n-nodes-base.mauticTrigger", "versions": ["1"], "version_count": 1, "line_number": 354}, {"name": "n8n-nodes-base.medium", "versions": ["1"], "version_count": 1, "line_number": 355}, {"name": "n8n-nodes-base.merge", "versions": ["1", "2", "2.1", "3", "3.1", "3.2"], "version_count": 6, "line_number": 356}, {"name": "n8n-nodes-base.messageBird", "versions": ["1"], "version_count": 1, "line_number": 357}, {"name": "n8n-nodes-base.metabase", "versions": ["1"], "version_count": 1, "line_number": 358}, {"name": "n8n-nodes-base.microsoftDynamicsCrm", "versions": ["1"], "version_count": 1, "line_number": 359}, {"name": "n8n-nodes-base.microsoftEntra", "versions": ["1"], "version_count": 1, "line_number": 360}, {"name": "n8n-nodes-base.microsoftExcel", "versions": ["1", "2", "2.1"], "version_count": 3, "line_number": 361}, {"name": "n8n-nodes-base.microsoftGraphSecurity", "versions": ["1"], "version_count": 1, "line_number": 362}, {"name": "n8n-nodes-base.microsoftOneDrive", "versions": ["1"], "version_count": 1, "line_number": 363}, {"name": "n8n-nodes-base.microsoftOneDriveTrigger", "versions": ["1"], "version_count": 1, "line_number": 364}, {"name": "n8n-nodes-base.microsoftOutlook", "versions": ["1", "2"], "version_count": 2, "line_number": 365}, {"name": "n8n-nodes-base.microsoftOutlookTrigger", "versions": ["1"], "version_count": 1, "line_number": 366}, {"name": "n8n-nodes-base.microsoftSharePoint", "versions": ["1"], "version_count": 1, "line_number": 367}, {"name": "n8n-nodes-base.microsoftSql", "versions": ["1", "1.1"], "version_count": 2, "line_number": 368}, {"name": "n8n-nodes-base.microsoftTeams", "versions": ["1", "1.1", "2"], "version_count": 3, "line_number": 369}, {"name": "n8n-nodes-base.microsoftTeamsTrigger", "versions": ["1"], "version_count": 1, "line_number": 370}, {"name": "n8n-nodes-base.microsoftToDo", "versions": ["1"], "version_count": 1, "line_number": 371}, {"name": "n8n-nodes-base.mindee", "versions": ["1", "2", "3"], "version_count": 3, "line_number": 372}, {"name": "n8n-nodes-base.misp", "versions": ["1"], "version_count": 1, "line_number": 373}, {"name": "n8n-nodes-base.mocean", "versions": ["1"], "version_count": 1, "line_number": 374}, {"name": "n8n-nodes-base.mondayCom", "versions": ["1"], "version_count": 1, "line_number": 375}, {"name": "n8n-nodes-base.mongoDb", "versions": ["1", "1.1", "1.2"], "version_count": 3, "line_number": 376}, {"name": "n8n-nodes-base.monicaCrm", "versions": ["1"], "version_count": 1, "line_number": 377}, {"name": "n8n-nodes-base.moveBinaryData", "versions": ["1", "1.1"], "version_count": 2, "line_number": 378}, {"name": "n8n-nodes-base.mqtt", "versions": ["1"], "version_count": 1, "line_number": 379}, {"name": "n8n-nodes-base.mqttTrigger", "versions": ["1"], "version_count": 1, "line_number": 380}, {"name": "n8n-nodes-base.msg91", "versions": ["1"], "version_count": 1, "line_number": 381}, {"name": "n8n-nodes-base.mySql", "versions": ["1", "2", "2.1", "2.2", "2.3", "2.4"], "version_count": 6, "line_number": 382}, {"name": "n8n-nodes-base.n8n", "versions": ["1"], "version_count": 1, "line_number": 383}, {"name": "n8n-nodes-base.n8nTrainingCustomerMessenger", "versions": ["1"], "version_count": 1, "line_number": 384}, {"name": "n8n-nodes-base.n8nTrigger", "versions": ["1"], "version_count": 1, "line_number": 385}, {"name": "n8n-nodes-base.nasa", "versions": ["1"], "version_count": 1, "line_number": 386}, {"name": "n8n-nodes-base.netlify", "versions": ["1"], "version_count": 1, "line_number": 387}, {"name": "n8n-nodes-base.netlifyTrigger", "versions": ["1"], "version_count": 1, "line_number": 388}, {"name": "n8n-nodes-base.netscalerAdc", "versions": ["1"], "version_count": 1, "line_number": 389}, {"name": "n8n-nodes-base.nextCloud", "versions": ["1"], "version_count": 1, "line_number": 390}, {"name": "n8n-nodes-base.noOp", "versions": ["1"], "version_count": 1, "line_number": 391}, {"name": "n8n-nodes-base.nocoDb", "versions": ["1", "2", "3"], "version_count": 3, "line_number": 392}, {"name": "n8n-nodes-base.notion", "versions": ["1", "2", "2.1", "2.2"], "version_count": 4, "line_number": 393}, {"name": "n8n-nodes-base.notionTrigger", "versions": ["1"], "version_count": 1, "line_number": 394}, {"name": "n8n-nodes-base.npm", "versions": ["1"], "version_count": 1, "line_number": 395}, {"name": "n8n-nodes-base.odoo", "versions": ["1"], "version_count": 1, "line_number": 396}, {"name": "n8n-nodes-base.okta", "versions": ["1"], "version_count": 1, "line_number": 397}, {"name": "n8n-nodes-base.oneSimpleApi", "versions": ["1"], "version_count": 1, "line_number": 398}, {"name": "n8n-nodes-base.onfleet", "versions": ["1"], "version_count": 1, "line_number": 399}, {"name": "n8n-nodes-base.onfleetTrigger", "versions": ["1"], "version_count": 1, "line_number": 400}, {"name": "n8n-nodes-base.openAi", "versions": ["1", "1.1"], "version_count": 2, "line_number": 401}, {"name": "n8n-nodes-base.openThesaurus", "versions": ["1"], "version_count": 1, "line_number": 402}, {"name": "n8n-nodes-base.openWeatherMap", "versions": ["1"], "version_count": 1, "line_number": 403}, {"name": "n8n-nodes-base.orbit", "versions": ["1"], "version_count": 1, "line_number": 404}, {"name": "n8n-nodes-base.oura", "versions": ["1"], "version_count": 1, "line_number": 405}, {"name": "n8n-nodes-base.paddle", "versions": ["1"], "version_count": 1, "line_number": 406}, {"name": "n8n-nodes-base.pagerDuty", "versions": ["1"], "version_count": 1, "line_number": 407}, {"name": "n8n-nodes-base.payPal", "versions": ["1"], "version_count": 1, "line_number": 408}, {"name": "n8n-nodes-base.payPalTrigger", "versions": ["1"], "version_count": 1, "line_number": 409}, {"name": "n8n-nodes-base.peekalink", "versions": ["1"], "version_count": 1, "line_number": 410}, {"name": "n8n-nodes-base.perplexity", "versions": ["1"], "version_count": 1, "line_number": 411}, {"name": "n8n-nodes-base.phantombuster", "versions": ["1"], "version_count": 1, "line_number": 412}, {"name": "n8n-nodes-base.philipsHue", "versions": ["1"], "version_count": 1, "line_number": 413}, {"name": "n8n-nodes-base.pipedrive", "versions": ["1"], "version_count": 1, "line_number": 414}, {"name": "n8n-nodes-base.pipedriveTrigger", "versions": ["1"], "version_count": 1, "line_number": 415}, {"name": "n8n-nodes-base.plivo", "versions": ["1"], "version_count": 1, "line_number": 416}, {"name": "n8n-nodes-base.postBin", "versions": ["1"], "version_count": 1, "line_number": 417}, {"name": "n8n-nodes-base.postHog", "versions": ["1"], "version_count": 1, "line_number": 418}, {"name": "n8n-nodes-base.postbin", "versions": ["1"], "version_count": 1, "line_number": 419}, {"name": "n8n-nodes-base.postgres", "versions": ["1", "2", "2.1", "2.2", "2.3", "2.4", "2.5", "2.6"], "version_count": 8, "line_number": 420}, {"name": "n8n-nodes-base.postgresTrigger", "versions": ["1"], "version_count": 1, "line_number": 421}, {"name": "n8n-nodes-base.postmarkTrigger", "versions": ["1"], "version_count": 1, "line_number": 422}, {"name": "n8n-nodes-base.profitWell", "versions": ["1"], "version_count": 1, "line_number": 423}, {"name": "n8n-nodes-base.pushbullet", "versions": ["1"], "version_count": 1, "line_number": 424}, {"name": "n8n-nodes-base.pushcut", "versions": ["1"], "version_count": 1, "line_number": 425}, {"name": "n8n-nodes-base.pushcutTrigger", "versions": ["1"], "version_count": 1, "line_number": 426}, {"name": "n8n-nodes-base.pushover", "versions": ["1"], "version_count": 1, "line_number": 427}, {"name": "n8n-nodes-base.questDb", "versions": ["1"], "version_count": 1, "line_number": 428}, {"name": "n8n-nodes-base.quick<PERSON><PERSON>", "versions": ["1"], "version_count": 1, "line_number": 429}, {"name": "n8n-nodes-base.quickbase", "versions": ["1"], "version_count": 1, "line_number": 430}, {"name": "n8n-nodes-base.quickbooks", "versions": ["1"], "version_count": 1, "line_number": 431}, {"name": "n8n-nodes-base.rabbitmq", "versions": ["1", "1.1"], "version_count": 2, "line_number": 432}, {"name": "n8n-nodes-base.rabbitmqTrigger", "versions": ["1"], "version_count": 1, "line_number": 433}, {"name": "n8n-nodes-base.raindrop", "versions": ["1"], "version_count": 1, "line_number": 434}, {"name": "n8n-nodes-base.readBinaryFile", "versions": ["1"], "version_count": 1, "line_number": 435}, {"name": "n8n-nodes-base.readBinaryFiles", "versions": ["1"], "version_count": 1, "line_number": 436}, {"name": "n8n-nodes-base.readPDF", "versions": ["1"], "version_count": 1, "line_number": 437}, {"name": "n8n-nodes-base.readWriteFile", "versions": ["1"], "version_count": 1, "line_number": 438}, {"name": "n8n-nodes-base.reddit", "versions": ["1"], "version_count": 1, "line_number": 439}, {"name": "n8n-nodes-base.redis", "versions": ["1"], "version_count": 1, "line_number": 440}, {"name": "n8n-nodes-base.redisTrigger", "versions": ["1"], "version_count": 1, "line_number": 441}, {"name": "n8n-nodes-base.removeDuplicates", "versions": ["1", "1.1", "2"], "version_count": 3, "line_number": 442}, {"name": "n8n-nodes-base.rename<PERSON><PERSON>s", "versions": ["1"], "version_count": 1, "line_number": 443}, {"name": "n8n-nodes-base.resource", "versions": ["1", "2"], "version_count": 2, "line_number": 444}, {"name": "n8n-nodes-base.respondToWebhook", "versions": ["1"], "version_count": 1, "line_number": 445}, {"name": "n8n-nodes-base.respondWith", "versions": ["1", "1.1", "1.2", "1.3", "1.4"], "version_count": 5, "line_number": 446}, {"name": "n8n-nodes-base.rocketchat", "versions": ["1"], "version_count": 1, "line_number": 447}, {"name": "n8n-nodes-base.rssFeedRead", "versions": ["1", "1.1", "1.2"], "version_count": 3, "line_number": 448}, {"name": "n8n-nodes-base.rssFeedReadTrigger", "versions": ["1"], "version_count": 1, "line_number": 449}, {"name": "n8n-nodes-base.rundeck", "versions": ["1"], "version_count": 1, "line_number": 450}, {"name": "n8n-nodes-base.s3", "versions": ["1"], "version_count": 1, "line_number": 451}, {"name": "n8n-nodes-base.salesforce", "versions": ["1"], "version_count": 1, "line_number": 452}, {"name": "n8n-nodes-base.salesforceTrigger", "versions": ["1"], "version_count": 1, "line_number": 453}, {"name": "n8n-nodes-base.salesmate", "versions": ["1"], "version_count": 1, "line_number": 454}, {"name": "n8n-nodes-base.scheduleTrigger", "versions": ["1", "1.1", "1.2"], "version_count": 3, "line_number": 455}, {"name": "n8n-nodes-base.seaTable", "versions": ["1", "2"], "version_count": 2, "line_number": 456}, {"name": "n8n-nodes-base.seaTableTrigger", "versions": ["1", "2"], "version_count": 2, "line_number": 457}, {"name": "n8n-nodes-base.securityScorecard", "versions": ["1"], "version_count": 1, "line_number": 458}, {"name": "n8n-nodes-base.segment", "versions": ["1"], "version_count": 1, "line_number": 459}, {"name": "n8n-nodes-base.sendGrid", "versions": ["1"], "version_count": 1, "line_number": 460}, {"name": "n8n-nodes-base.sendInBlue", "versions": ["1"], "version_count": 1, "line_number": 461}, {"name": "n8n-nodes-base.sendInBlueApi", "versions": ["1"], "version_count": 1, "line_number": 462}, {"name": "n8n-nodes-base.sendy", "versions": ["1"], "version_count": 1, "line_number": 463}, {"name": "n8n-nodes-base.sentryIo", "versions": ["1"], "version_count": 1, "line_number": 464}, {"name": "n8n-nodes-base.serviceNow", "versions": ["1"], "version_count": 1, "line_number": 465}, {"name": "n8n-nodes-base.set", "versions": ["1", "2", "3", "3.1", "3.2", "3.3", "3.4"], "version_count": 7, "line_number": 466}, {"name": "n8n-nodes-base.shopify", "versions": ["1"], "version_count": 1, "line_number": 467}, {"name": "n8n-nodes-base.shopifyTrigger", "versions": ["1"], "version_count": 1, "line_number": 468}, {"name": "n8n-nodes-base.signl4", "versions": ["1"], "version_count": 1, "line_number": 469}, {"name": "n8n-nodes-base.simulate", "versions": ["1"], "version_count": 1, "line_number": 470}, {"name": "n8n-nodes-base.simulateTrigger", "versions": ["1"], "version_count": 1, "line_number": 471}, {"name": "n8n-nodes-base.slack", "versions": ["1", "2", "2.1", "2.2", "2.3"], "version_count": 5, "line_number": 472}, {"name": "n8n-nodes-base.slackTrigger", "versions": ["1"], "version_count": 1, "line_number": 473}, {"name": "n8n-nodes-base.sms77", "versions": ["1"], "version_count": 1, "line_number": 474}, {"name": "n8n-nodes-base.snowflake", "versions": ["1"], "version_count": 1, "line_number": 475}, {"name": "n8n-nodes-base.sort", "versions": ["1"], "version_count": 1, "line_number": 476}, {"name": "n8n-nodes-base.splitInBatches", "versions": ["1", "2", "3"], "version_count": 3, "line_number": 477}, {"name": "n8n-nodes-base.splitOut", "versions": ["1"], "version_count": 1, "line_number": 478}, {"name": "n8n-nodes-base.splunk", "versions": ["1", "2"], "version_count": 2, "line_number": 479}, {"name": "n8n-nodes-base.spontit", "versions": ["1"], "version_count": 1, "line_number": 480}, {"name": "n8n-nodes-base.spotify", "versions": ["1"], "version_count": 1, "line_number": 481}, {"name": "n8n-nodes-base.spreadsheetFile", "versions": ["1", "2"], "version_count": 2, "line_number": 482}, {"name": "n8n-nodes-base.sseTrigger", "versions": ["1"], "version_count": 1, "line_number": 483}, {"name": "n8n-nodes-base.ssh", "versions": ["1"], "version_count": 1, "line_number": 484}, {"name": "n8n-nodes-base.stackby", "versions": ["1"], "version_count": 1, "line_number": 485}, {"name": "n8n-nodes-base.start", "versions": ["1"], "version_count": 1, "line_number": 486}, {"name": "n8n-nodes-base.stickyNote", "versions": ["1"], "version_count": 1, "line_number": 487}, {"name": "n8n-nodes-base.stopAndError", "versions": ["1"], "version_count": 1, "line_number": 488}, {"name": "n8n-nodes-base.storyblok", "versions": ["1"], "version_count": 1, "line_number": 489}, {"name": "n8n-nodes-base.strapi", "versions": ["1"], "version_count": 1, "line_number": 490}, {"name": "n8n-nodes-base.strava", "versions": ["1", "1.1"], "version_count": 2, "line_number": 491}, {"name": "n8n-nodes-base.stravaTrigger", "versions": ["1"], "version_count": 1, "line_number": 492}, {"name": "n8n-nodes-base.stripe", "versions": ["1"], "version_count": 1, "line_number": 493}, {"name": "n8n-nodes-base.stripeTrigger", "versions": ["1"], "version_count": 1, "line_number": 494}, {"name": "n8n-nodes-base.summarize", "versions": ["1", "1.1"], "version_count": 2, "line_number": 495}, {"name": "n8n-nodes-base.supabase", "versions": ["1"], "version_count": 1, "line_number": 496}, {"name": "n8n-nodes-base.surveyMonkeyTrigger", "versions": ["1"], "version_count": 1, "line_number": 497}, {"name": "n8n-nodes-base.switch", "versions": ["1", "2", "3", "3.1", "3.2"], "version_count": 5, "line_number": 498}, {"name": "n8n-nodes-base.syncroMsp", "versions": ["1"], "version_count": 1, "line_number": 499}, {"name": "n8n-nodes-base.taiga", "versions": ["1"], "version_count": 1, "line_number": 500}, {"name": "n8n-nodes-base.taigaTrigger", "versions": ["1"], "version_count": 1, "line_number": 501}, {"name": "n8n-nodes-base.tapfiliate", "versions": ["1"], "version_count": 1, "line_number": 502}, {"name": "n8n-nodes-base.telegram", "versions": ["1", "1.1", "1.2"], "version_count": 3, "line_number": 503}, {"name": "n8n-nodes-base.telegramTrigger", "versions": ["1", "1.1", "1.2"], "version_count": 3, "line_number": 504}, {"name": "n8n-nodes-base.theHive", "versions": ["1"], "version_count": 1, "line_number": 505}, {"name": "n8n-nodes-base.theHiveProject", "versions": ["1"], "version_count": 1, "line_number": 506}, {"name": "n8n-nodes-base.theHiveProjectTrigger", "versions": ["1"], "version_count": 1, "line_number": 507}, {"name": "n8n-nodes-base.theHiveTrigger", "versions": ["1", "2"], "version_count": 2, "line_number": 508}, {"name": "n8n-nodes-base.timescaleDb", "versions": ["1"], "version_count": 1, "line_number": 509}, {"name": "n8n-nodes-base.todoist", "versions": ["1", "2", "2.1"], "version_count": 3, "line_number": 510}, {"name": "n8n-nodes-base.togglTrigger", "versions": ["1"], "version_count": 1, "line_number": 511}, {"name": "n8n-nodes-base.toolExecutor", "versions": ["1"], "version_count": 1, "line_number": 512}, {"name": "n8n-nodes-base.totp", "versions": ["1"], "version_count": 1, "line_number": 513}, {"name": "n8n-nodes-base.travisCi", "versions": ["1"], "version_count": 1, "line_number": 514}, {"name": "n8n-nodes-base.trello", "versions": ["1"], "version_count": 1, "line_number": 515}, {"name": "n8n-nodes-base.trelloTrigger", "versions": ["1"], "version_count": 1, "line_number": 516}, {"name": "n8n-nodes-base.twake", "versions": ["1"], "version_count": 1, "line_number": 517}, {"name": "n8n-nodes-base.twilio", "versions": ["1"], "version_count": 1, "line_number": 518}, {"name": "n8n-nodes-base.twilioTrigger", "versions": ["1"], "version_count": 1, "line_number": 519}, {"name": "n8n-nodes-base.twist", "versions": ["1"], "version_count": 1, "line_number": 520}, {"name": "n8n-nodes-base.twitter", "versions": ["1", "2"], "version_count": 2, "line_number": 521}, {"name": "n8n-nodes-base.typeformTrigger", "versions": ["1", "1.1"], "version_count": 2, "line_number": 522}, {"name": "n8n-nodes-base.unleashedSoftware", "versions": ["1"], "version_count": 1, "line_number": 523}, {"name": "n8n-nodes-base.uplead", "versions": ["1"], "version_count": 1, "line_number": 524}, {"name": "n8n-nodes-base.uproc", "versions": ["1"], "version_count": 1, "line_number": 525}, {"name": "n8n-nodes-base.uptimeRobot", "versions": ["1"], "version_count": 1, "line_number": 526}, {"name": "n8n-nodes-base.urlScanIo", "versions": ["1"], "version_count": 1, "line_number": 527}, {"name": "n8n-nodes-base.useWorkflowTimezone", "versions": ["2", "2.1", "2.2"], "version_count": 3, "line_number": 528}, {"name": "n8n-nodes-base.venafiTlsProtectCloud", "versions": ["1"], "version_count": 1, "line_number": 529}, {"name": "n8n-nodes-base.venafiTlsProtectCloudTrigger", "versions": ["1"], "version_count": 1, "line_number": 530}, {"name": "n8n-nodes-base.venafiTlsProtectDatacenter", "versions": ["1"], "version_count": 1, "line_number": 531}, {"name": "n8n-nodes-base.venafiTlsProtectDatacenterTrigger", "versions": ["1"], "version_count": 1, "line_number": 532}, {"name": "n8n-nodes-base.vero", "versions": ["1"], "version_count": 1, "line_number": 533}, {"name": "n8n-nodes-base.von<PERSON>", "versions": ["1"], "version_count": 1, "line_number": 534}, {"name": "n8n-nodes-base.wait", "versions": ["1"], "version_count": 1, "line_number": 535}, {"name": "n8n-nodes-base.webflow", "versions": ["1", "2"], "version_count": 2, "line_number": 536}, {"name": "n8n-nodes-base.webflowTrigger", "versions": ["1", "2"], "version_count": 2, "line_number": 537}, {"name": "n8n-nodes-base.webhook", "versions": ["1", "1.1", "2"], "version_count": 3, "line_number": 538}, {"name": "n8n-nodes-base.wekan", "versions": ["1"], "version_count": 1, "line_number": 539}, {"name": "n8n-nodes-base.whatsApp", "versions": ["1"], "version_count": 1, "line_number": 540}, {"name": "n8n-nodes-base.whatsAppTrigger", "versions": ["1"], "version_count": 1, "line_number": 541}, {"name": "n8n-nodes-base.wise", "versions": ["1"], "version_count": 1, "line_number": 542}, {"name": "n8n-nodes-base.wiseTrigger", "versions": ["1"], "version_count": 1, "line_number": 543}, {"name": "n8n-nodes-base.wooCommerce", "versions": ["1"], "version_count": 1, "line_number": 544}, {"name": "n8n-nodes-base.wooCommerceTrigger", "versions": ["1"], "version_count": 1, "line_number": 545}, {"name": "n8n-nodes-base.wordpress", "versions": ["1"], "version_count": 1, "line_number": 546}, {"name": "n8n-nodes-base.workableTrigger", "versions": ["1"], "version_count": 1, "line_number": 547}, {"name": "n8n-nodes-base.workflowTrigger", "versions": ["1"], "version_count": 1, "line_number": 548}, {"name": "n8n-nodes-base.writeBinaryFile", "versions": ["1"], "version_count": 1, "line_number": 549}, {"name": "n8n-nodes-base.wufooTrigger", "versions": ["1"], "version_count": 1, "line_number": 550}, {"name": "n8n-nodes-base.xero", "versions": ["1"], "version_count": 1, "line_number": 551}, {"name": "n8n-nodes-base.xml", "versions": ["1"], "version_count": 1, "line_number": 552}, {"name": "n8n-nodes-base.youTube", "versions": ["1"], "version_count": 1, "line_number": 553}, {"name": "n8n-nodes-base.yourls", "versions": ["1"], "version_count": 1, "line_number": 554}, {"name": "n8n-nodes-base.zammad", "versions": ["1"], "version_count": 1, "line_number": 555}, {"name": "n8n-nodes-base.zendesk", "versions": ["1"], "version_count": 1, "line_number": 556}, {"name": "n8n-nodes-base.zendeskTrigger", "versions": ["1"], "version_count": 1, "line_number": 557}, {"name": "n8n-nodes-base.zohoCrm", "versions": ["1"], "version_count": 1, "line_number": 558}, {"name": "n8n-nodes-base.zoom", "versions": ["1"], "version_count": 1, "line_number": 559}, {"name": "n8n-nodes-base.zulip", "versions": ["1"], "version_count": 1, "line_number": 560}], "total_nodes": 560, "metadata": {"source_file": "n8n_nodes_list.txt", "description": "n8n nodes list with versions"}}