[{"prompt": "Create a manually triggered workflow that first sets a `woocommerce url` variable. Then, make an authenticated HTTP POST request using WordPress API credentials to the `wp-json/rank-math-api/v1/update-meta` endpoint on that URL, updating Rank Math SEO meta for `post_id` 246 with a specific title, description, and canonical URL, configured to retry on failure.", "index": 12.0, "llm_score": 10.0, "json_parsed": 1.0, "top_level_keys_present": 4.0, "workflow_name_valid": 1.0, "num_nodes": 3.0, "num_nodes_with_valid_type": 3.0, "num_nodes_with_valid_version": 3.0, "num_nodes_with_valid_structure": 3.0, "num_nodes_with_parameters": 3.0, "num_connections": 2.0, "num_connections_with_valid_structure": 2.0, "num_connections_with_valid_target_node": 2.0, "active_field_boolean": 1.0, "percent_node_with_valid_type": 100.0, "percent_node_with_valid_version": 100.0, "percent_node_with_valid_structure": 100.0, "percent_connections_with_valid_structure": 100.0, "percent_connections_with_valid_target_node": 100.0, "percent_node_with_parameters": 100.0, "difficulty": "medium"}, {"prompt": "Create a workflow that manually triggers and sets a hardcoded phone number to \"+34605281220\". Then, use the `uproc` node with the `miquel-uproc` credential and `getPhoneParsed` tool to parse and validate this number, followed by an If node that checks if the phone is valid based on the parsing result.", "index": 13.0, "llm_score": 8.75, "json_parsed": 1.0, "top_level_keys_present": 4.0, "workflow_name_valid": 1.0, "num_nodes": 4.0, "num_nodes_with_valid_type": 4.0, "num_nodes_with_valid_version": 3.75, "num_nodes_with_valid_structure": 4.0, "num_nodes_with_parameters": 4.0, "num_connections": 3.0, "num_connections_with_valid_structure": 3.0, "num_connections_with_valid_target_node": 2.5, "active_field_boolean": 1.0, "percent_node_with_valid_type": 100.0, "percent_node_with_valid_version": 93.75, "percent_node_with_valid_structure": 100.0, "percent_connections_with_valid_structure": 100.0, "percent_connections_with_valid_target_node": 83.**********, "percent_node_with_parameters": 100.0, "difficulty": "medium"}, {"prompt": "Create a workflow that triggers on Google Sheets updates, checks for Telegram links, retrieves reactions, and updates the Google Sheet. The workflow uses HTTP requests, code, conditionals, and Google Sheets nodes to automate the process of extracting and storing Telegram message reaction data.\n", "index": 26.0, "llm_score": 5.75, "json_parsed": 1.0, "top_level_keys_present": 4.0, "workflow_name_valid": 1.0, "num_nodes": 5.75, "num_nodes_with_valid_type": 5.75, "num_nodes_with_valid_version": 5.75, "num_nodes_with_valid_structure": 5.75, "num_nodes_with_parameters": 5.75, "num_connections": 4.75, "num_connections_with_valid_structure": 4.75, "num_connections_with_valid_target_node": 4.75, "active_field_boolean": 1.0, "percent_node_with_valid_type": 100.0, "percent_node_with_valid_version": 100.0, "percent_node_with_valid_structure": 100.0, "percent_connections_with_valid_structure": 100.0, "percent_connections_with_valid_target_node": 100.0, "percent_node_with_parameters": 100.0, "difficulty": "hard"}, {"prompt": "Create a workflow that, when manually triggered, reads the file `/files/tmp/tst1.mp4` from disk, then sends it as `multipart/form-data` to `https://api.elevenlabs.io/v1/speech-to-text` (POST method) using the 'Eleven Labs' credential, specifying `model_id` as `scribe_v1`.", "index": 25.0, "llm_score": 9.25, "json_parsed": 1.0, "top_level_keys_present": 4.0, "workflow_name_valid": 1.0, "num_nodes": 2.75, "num_nodes_with_valid_type": 2.0, "num_nodes_with_valid_version": 2.0, "num_nodes_with_valid_structure": 2.75, "num_nodes_with_parameters": 2.75, "num_connections": 1.75, "num_connections_with_valid_structure": 1.75, "num_connections_with_valid_target_node": 1.75, "active_field_boolean": 1.0, "percent_node_with_valid_type": 70.8333333333, "percent_node_with_valid_version": 70.8333333333, "percent_node_with_valid_structure": 100.0, "percent_connections_with_valid_structure": 100.0, "percent_connections_with_valid_target_node": 100.0, "percent_node_with_parameters": 100.0, "difficulty": "medium"}, {"prompt": "Create an inactive n8n workflow named \"Twilio Test\" that starts with a Twilio Trigger node. This trigger should activate when an inbound message is received and use existing Twilio API credentials.", "index": 2.0, "llm_score": 9.5, "json_parsed": 1.0, "top_level_keys_present": 4.0, "workflow_name_valid": 1.0, "num_nodes": 1.0, "num_nodes_with_valid_type": 0.75, "num_nodes_with_valid_version": 0.75, "num_nodes_with_valid_structure": 1.0, "num_nodes_with_parameters": 1.0, "num_connections": 0.0, "num_connections_with_valid_structure": 0.0, "num_connections_with_valid_target_node": 0.0, "active_field_boolean": 1.0, "percent_node_with_valid_type": 75.0, "percent_node_with_valid_version": 75.0, "percent_node_with_valid_structure": 100.0, "percent_connections_with_valid_structure": null, "percent_connections_with_valid_target_node": null, "percent_node_with_parameters": 100.0, "difficulty": "easy"}, {"prompt": "Create an n8n workflow JSON with nodes for a manual trigger, token splitter, Xata memory, structured output parser, Wikipedia tool, Hugging Face embeddings, and a Supabase vector store. Include the necessary credentials and connection details for these nodes.\n", "index": 0.0, "llm_score": 9.75, "json_parsed": 1.0, "top_level_keys_present": 4.0, "workflow_name_valid": 1.0, "num_nodes": 7.0, "num_nodes_with_valid_type": 2.5, "num_nodes_with_valid_version": 2.5, "num_nodes_with_valid_structure": 7.0, "num_nodes_with_parameters": 7.0, "num_connections": 6.0, "num_connections_with_valid_structure": 6.0, "num_connections_with_valid_target_node": 6.0, "active_field_boolean": 1.0, "percent_node_with_valid_type": 35.7142857143, "percent_node_with_valid_version": 35.7142857143, "percent_node_with_valid_structure": 100.0, "percent_connections_with_valid_structure": 100.0, "percent_connections_with_valid_target_node": 100.0, "percent_node_with_parameters": 100.0, "difficulty": "easy"}, {"prompt": "Create an n8n workflow for a chat-based weather assistant. It uses AI agents to parse user queries, handling city, time, and intent (current, historical, forecast), fetches weather data from an external API, and generates natural language responses, including clarification questions for ambiguous inputs.", "index": 45.0, "llm_score": 7.125, "json_parsed": 1.0, "top_level_keys_present": 4.0, "workflow_name_valid": 1.0, "num_nodes": 7.0, "num_nodes_with_valid_type": 4.75, "num_nodes_with_valid_version": 4.75, "num_nodes_with_valid_structure": 7.0, "num_nodes_with_parameters": 7.0, "num_connections": 6.75, "num_connections_with_valid_structure": 6.75, "num_connections_with_valid_target_node": 6.75, "active_field_boolean": 1.0, "percent_node_with_valid_type": 71.5476190476, "percent_node_with_valid_version": 71.5476190476, "percent_node_with_valid_structure": 100.0, "percent_connections_with_valid_structure": 100.0, "percent_connections_with_valid_target_node": 100.0, "percent_node_with_parameters": 100.0, "difficulty": "hard"}, {"prompt": "Create an n8n workflow named \"GetOnboardingFileChainofThought\" that triggers via a webhook. This workflow should fetch the content of the Google Doc at `https://docs.google.com/document/d/1K0Y-OxyIV0auMN0CKe8tWUb4uSimFFIWZQCWaEvKQo4/view` and respond to the initial webhook with that content as plain text, using a Google Docs account credential. Apply the tags: Day4, PromptEngineer, Files, Onboarding.", "index": 18.0, "llm_score": 8.5, "json_parsed": 1.0, "top_level_keys_present": 4.0, "workflow_name_valid": 1.0, "num_nodes": 2.**********, "num_nodes_with_valid_type": 2.0, "num_nodes_with_valid_version": 2.0, "num_nodes_with_valid_structure": 2.**********, "num_nodes_with_parameters": 2.**********, "num_connections": 1.**********, "num_connections_with_valid_structure": 1.**********, "num_connections_with_valid_target_node": 1.**********, "active_field_boolean": 1.0, "percent_node_with_valid_type": 88.**********, "percent_node_with_valid_version": 88.**********, "percent_node_with_valid_structure": 100.0, "percent_connections_with_valid_structure": 100.0, "percent_connections_with_valid_target_node": 100.0, "percent_node_with_parameters": 100.0, "difficulty": "medium"}, {"prompt": "Create an n8n workflow named \"Receive updates when an event occurs in Asana\". It should start with an Asana Trigger node configured to monitor the \"Tweets\" resource in the \"Engineering\" workspace, using your \"asana\" API credentials.", "index": 3.0, "llm_score": 10.0, "json_parsed": 1.0, "top_level_keys_present": 4.0, "workflow_name_valid": 1.0, "num_nodes": 1.0, "num_nodes_with_valid_type": 0.75, "num_nodes_with_valid_version": 0.75, "num_nodes_with_valid_structure": 1.0, "num_nodes_with_parameters": 1.0, "num_connections": 0.0, "num_connections_with_valid_structure": 0.0, "num_connections_with_valid_target_node": 0.0, "active_field_boolean": 1.0, "percent_node_with_valid_type": 75.0, "percent_node_with_valid_version": 75.0, "percent_node_with_valid_structure": 100.0, "percent_connections_with_valid_structure": null, "percent_connections_with_valid_target_node": null, "percent_node_with_parameters": 100.0, "difficulty": "easy"}, {"prompt": "Create an n8n workflow named 'Multiple trigger node rerun' that can be triggered manually or on a minute-by-minute schedule. Both triggers should initiate an HTTP request to `https://internal.users.n8n.cloud/webhook/random-data-api` to fetch random users, then use a code node to process the data by reversing the 'firstname' field.", "index": 8.0, "llm_score": 10.0, "json_parsed": 1.0, "top_level_keys_present": 4.0, "workflow_name_valid": 1.0, "num_nodes": 4.25, "num_nodes_with_valid_type": 4.0, "num_nodes_with_valid_version": 4.0, "num_nodes_with_valid_structure": 4.25, "num_nodes_with_parameters": 4.25, "num_connections": 3.25, "num_connections_with_valid_structure": 3.25, "num_connections_with_valid_target_node": 3.25, "active_field_boolean": 1.0, "percent_node_with_valid_type": 93.75, "percent_node_with_valid_version": 93.75, "percent_node_with_valid_structure": 100.0, "percent_connections_with_valid_structure": 100.0, "percent_connections_with_valid_target_node": 100.0, "percent_node_with_parameters": 100.0, "difficulty": "medium"}, {"prompt": "Create an n8n workflow named 'My workflow 8' that starts with an n8n Form Trigger (path: `d1cba915-ca18-4425-bcfb-133205fc815a`, title 'test', single field 'test'). This trigger should output two example items (e.g., `{'name': 'First item', 'code': 1}`, `{'name': 'Second item', 'code': 2}`) and connect to a Switch node.", "index": 9.0, "llm_score": 6.75, "json_parsed": 1.0, "top_level_keys_present": 4.0, "workflow_name_valid": 1.0, "num_nodes": 2.25, "num_nodes_with_valid_type": 2.25, "num_nodes_with_valid_version": 2.25, "num_nodes_with_valid_structure": 2.25, "num_nodes_with_parameters": 2.25, "num_connections": 1.25, "num_connections_with_valid_structure": 1.25, "num_connections_with_valid_target_node": 1.25, "active_field_boolean": 1.0, "percent_node_with_valid_type": 100.0, "percent_node_with_valid_version": 100.0, "percent_node_with_valid_structure": 100.0, "percent_connections_with_valid_structure": 100.0, "percent_connections_with_valid_target_node": 100.0, "percent_node_with_parameters": 100.0, "difficulty": "medium"}, {"prompt": "Create an n8n workflow that manually triggers adding an event to Google Calendar. The event should run from June 25, 2020, 7 AM UTC to June 27, 2020, 7 AM UTC, on the '<EMAIL>' calendar.", "index": 11.0, "llm_score": 10.0, "json_parsed": 1.0, "top_level_keys_present": 4.0, "workflow_name_valid": 1.0, "num_nodes": 2.0, "num_nodes_with_valid_type": 2.0, "num_nodes_with_valid_version": 2.0, "num_nodes_with_valid_structure": 2.0, "num_nodes_with_parameters": 2.0, "num_connections": 1.0, "num_connections_with_valid_structure": 1.0, "num_connections_with_valid_target_node": 1.0, "active_field_boolean": 1.0, "percent_node_with_valid_type": 100.0, "percent_node_with_valid_version": 100.0, "percent_node_with_valid_structure": 100.0, "percent_connections_with_valid_structure": 100.0, "percent_connections_with_valid_target_node": 100.0, "percent_node_with_parameters": 100.0, "difficulty": "medium"}, {"prompt": "Create an n8n workflow that starts with a Schedule Trigger and branches into two parallel sequences of 'Set' nodes. Ensure the 'Schedule Trigger', 'Edit Fields7', and 'Edit Fields2' nodes all contain the exact pinned data: `[{\"name\": \"First item\", \"code\": 1}, {\"name\": \"Second item\", \"code\": 2}]`.", "index": 23.0, "llm_score": 8.25, "json_parsed": 1.0, "top_level_keys_present": 4.0, "workflow_name_valid": 1.0, "num_nodes": 4.0, "num_nodes_with_valid_type": 3.25, "num_nodes_with_valid_version": 3.25, "num_nodes_with_valid_structure": 4.0, "num_nodes_with_parameters": 4.0, "num_connections": 3.0, "num_connections_with_valid_structure": 3.0, "num_connections_with_valid_target_node": 3.0, "active_field_boolean": 1.0, "percent_node_with_valid_type": 84.5238095238, "percent_node_with_valid_version": 84.5238095238, "percent_node_with_valid_structure": 100.0, "percent_connections_with_valid_structure": 100.0, "percent_connections_with_valid_target_node": 100.0, "percent_node_with_parameters": 100.0, "difficulty": "medium"}, {"prompt": "Create an n8n workflow that starts with a Webhook, expecting a `crc_token` query parameter. This token should then be used in a Crypto node to perform a SHA256 HMAC with \"API KEY SECRET\" and base64 encoding, and finally, a Set node should create a `response_token` by prepending \"sha256=\" to the HMAC output, with the webhook responding only with this new token.", "index": 10.0, "llm_score": 7.25, "json_parsed": 1.0, "top_level_keys_present": 4.0, "workflow_name_valid": 1.0, "num_nodes": 3.0, "num_nodes_with_valid_type": 2.0, "num_nodes_with_valid_version": 2.0, "num_nodes_with_valid_structure": 3.0, "num_nodes_with_parameters": 3.0, "num_connections": 2.0, "num_connections_with_valid_structure": 2.0, "num_connections_with_valid_target_node": 2.0, "active_field_boolean": 1.0, "percent_node_with_valid_type": 66.**********, "percent_node_with_valid_version": 66.**********, "percent_node_with_valid_structure": 100.0, "percent_connections_with_valid_structure": 100.0, "percent_connections_with_valid_target_node": 100.0, "percent_node_with_parameters": 100.0, "difficulty": "medium"}, {"prompt": "Create an n8n workflow that starts with a Webhook, then branches into two parallel paths. Each path should generate 5 empty items using a Code node, and then proceed to an Edit Fields (Set) node.", "index": 22.0, "llm_score": 10.0, "json_parsed": 1.0, "top_level_keys_present": 4.0, "workflow_name_valid": 1.0, "num_nodes": 5.25, "num_nodes_with_valid_type": 5.0, "num_nodes_with_valid_version": 5.0, "num_nodes_with_valid_structure": 5.25, "num_nodes_with_parameters": 5.25, "num_connections": 4.25, "num_connections_with_valid_structure": 4.25, "num_connections_with_valid_target_node": 4.25, "active_field_boolean": 1.0, "percent_node_with_valid_type": 95.8333333333, "percent_node_with_valid_version": 95.8333333333, "percent_node_with_valid_structure": 100.0, "percent_connections_with_valid_structure": 100.0, "percent_connections_with_valid_target_node": 100.0, "percent_node_with_parameters": 100.0, "difficulty": "medium"}, {"prompt": "Create an n8n workflow that starts with a manual trigger node. This node should then connect to and initiate a 'trigger' operation on a TravisCI node.", "index": 17.0, "llm_score": 10.0, "json_parsed": 1.0, "top_level_keys_present": 4.0, "workflow_name_valid": 1.0, "num_nodes": 2.0, "num_nodes_with_valid_type": 1.0, "num_nodes_with_valid_version": 1.0, "num_nodes_with_valid_structure": 2.0, "num_nodes_with_parameters": 2.0, "num_connections": 1.0, "num_connections_with_valid_structure": 1.0, "num_connections_with_valid_target_node": 1.0, "active_field_boolean": 1.0, "percent_node_with_valid_type": 50.0, "percent_node_with_valid_version": 50.0, "percent_node_with_valid_structure": 100.0, "percent_connections_with_valid_structure": 100.0, "percent_connections_with_valid_target_node": 100.0, "percent_node_with_parameters": 100.0, "difficulty": "medium"}, {"prompt": "Create an n8n workflow that triggers on a web form submission titled \"Newsletter de UDIA\", collecting a user's name, email, and privacy consent. Upon submission, it should append or update the collected name and email to \"Hoja 1\" in the specified Google Sheet, matching existing entries by email.", "index": 24.0, "llm_score": 6.5, "json_parsed": 1.0, "top_level_keys_present": 4.0, "workflow_name_valid": 1.0, "num_nodes": 5.75, "num_nodes_with_valid_type": 5.75, "num_nodes_with_valid_version": 5.75, "num_nodes_with_valid_structure": 5.75, "num_nodes_with_parameters": 5.75, "num_connections": 4.75, "num_connections_with_valid_structure": 4.75, "num_connections_with_valid_target_node": 4.75, "active_field_boolean": 1.0, "percent_node_with_valid_type": 100.0, "percent_node_with_valid_version": 100.0, "percent_node_with_valid_structure": 100.0, "percent_connections_with_valid_structure": 100.0, "percent_connections_with_valid_target_node": 100.0, "percent_node_with_parameters": 100.0, "difficulty": "medium"}, {"prompt": "Create an n8n workflow that triggers when a file is created in the Google Drive folder with ID `1HwOAKkkgveLji8vVpW9Xrg1EsBskwMNb`. The workflow should then send an email to `<EMAIL>` (from `<EMAIL>`) with the subject 'File Update' and the body 'A file in your Google Drive file folder has been created: {{$json[\"name\"]}}'.", "index": 16.0, "llm_score": 8.75, "json_parsed": 1.0, "top_level_keys_present": 4.0, "workflow_name_valid": 1.0, "num_nodes": 2.0, "num_nodes_with_valid_type": 1.5, "num_nodes_with_valid_version": 1.5, "num_nodes_with_valid_structure": 2.0, "num_nodes_with_parameters": 2.0, "num_connections": 1.0, "num_connections_with_valid_structure": 1.0, "num_connections_with_valid_target_node": 1.0, "active_field_boolean": 1.0, "percent_node_with_valid_type": 75.0, "percent_node_with_valid_version": 75.0, "percent_node_with_valid_structure": 100.0, "percent_connections_with_valid_structure": 100.0, "percent_connections_with_valid_target_node": 100.0, "percent_node_with_parameters": 100.0, "difficulty": "medium"}, {"prompt": "Create an n8n workflow that triggers when a new order is created in WooCommerce. Then, send a message to Mattermost using the format `{{$node[\"WooCommerce Trigger\"].json[\"billing\"][\"first_name\"]}} bought {{$node[\"WooCommerce Trigger\"].json[\"line_items\"][0][\"name\"]}}!` to channel ID `pj1p95ebei8g3ro5p84kxxuuio`.", "index": 15.0, "llm_score": 9.25, "json_parsed": 1.0, "top_level_keys_present": 4.0, "workflow_name_valid": 1.0, "num_nodes": 2.0, "num_nodes_with_valid_type": 0.5, "num_nodes_with_valid_version": 0.5, "num_nodes_with_valid_structure": 2.0, "num_nodes_with_parameters": 2.0, "num_connections": 1.0, "num_connections_with_valid_structure": 1.0, "num_connections_with_valid_target_node": 1.0, "active_field_boolean": 1.0, "percent_node_with_valid_type": 25.0, "percent_node_with_valid_version": 25.0, "percent_node_with_valid_structure": 100.0, "percent_connections_with_valid_structure": 100.0, "percent_connections_with_valid_target_node": 100.0, "percent_node_with_parameters": 100.0, "difficulty": "medium"}, {"prompt": "Create an n8n workflow triggered by a Gumroad sale that adds the customer to MailerLite, assigns them to a specific group, and appends their data to a Google Sheets CRM.\n", "index": 6.0, "llm_score": 9.0, "json_parsed": 1.0, "top_level_keys_present": 4.0, "workflow_name_valid": 1.0, "num_nodes": 3.0, "num_nodes_with_valid_type": 2.25, "num_nodes_with_valid_version": 2.25, "num_nodes_with_valid_structure": 3.0, "num_nodes_with_parameters": 3.0, "num_connections": 2.0, "num_connections_with_valid_structure": 2.0, "num_connections_with_valid_target_node": 2.0, "active_field_boolean": 1.0, "percent_node_with_valid_type": 75.0, "percent_node_with_valid_version": 75.0, "percent_node_with_valid_structure": 100.0, "percent_connections_with_valid_structure": 100.0, "percent_connections_with_valid_target_node": 100.0, "percent_node_with_parameters": 100.0, "difficulty": "medium"}, {"prompt": "Generate a Flowise workflow JSON that uses an AI Agent to answer questions about stock earnings and financial metrics, utilizing tools like a financial metrics API, Flowise assistant API for earnings reports/technical analysis, and an option strategy API. The workflow should use an Anthropic Chat Model, a window buffer memory, and a Set node to return the final response.\n", "index": 30.0, "llm_score": 5.75, "json_parsed": 1.0, "top_level_keys_present": 4.0, "workflow_name_valid": 1.0, "num_nodes": 5.5, "num_nodes_with_valid_type": 2.75, "num_nodes_with_valid_version": 2.75, "num_nodes_with_valid_structure": 5.5, "num_nodes_with_parameters": 5.5, "num_connections": 3.0, "num_connections_with_valid_structure": 3.0, "num_connections_with_valid_target_node": 3.0, "active_field_boolean": 1.0, "percent_node_with_valid_type": 50.0, "percent_node_with_valid_version": 50.0, "percent_node_with_valid_structure": 100.0, "percent_connections_with_valid_structure": 100.0, "percent_connections_with_valid_target_node": 100.0, "percent_node_with_parameters": 100.0, "difficulty": "hard"}, {"prompt": "Generate a JSON object for a disabled configuration, named 'ComputerUsetest', with the ID 'bCDw3w8IJky94a1z' and version ID 'bc3cb97e-83b5-4dbe-a42a-0265d10b1db1'. Ensure it includes `createdAt` and `updatedAt` timestamps, `triggerCount: 0`, `settings: {executionOrder: 'v1'}`, and all other fields like `nodes`, `connections`, `staticData`, `meta`, `pinData`, and `tags` are initialized to their respective default empty or null states.", "index": 7.0, "llm_score": 1.0, "json_parsed": 1.0, "top_level_keys_present": 4.0, "workflow_name_valid": 1.0, "num_nodes": 1.0, "num_nodes_with_valid_type": 1.0, "num_nodes_with_valid_version": 1.0, "num_nodes_with_valid_structure": 1.0, "num_nodes_with_parameters": 1.0, "num_connections": 1.0, "num_connections_with_valid_structure": 1.0, "num_connections_with_valid_target_node": 1.0, "active_field_boolean": 1.0, "percent_node_with_valid_type": 100.0, "percent_node_with_valid_version": 100.0, "percent_node_with_valid_structure": 100.0, "percent_connections_with_valid_structure": 100.0, "percent_connections_with_valid_target_node": 100.0, "percent_node_with_parameters": 100.0, "difficulty": "easy"}, {"prompt": "Generate a JSON object for a new voice agent named \"AGENTE DE VOZ - VENDEDOR SOLUCIÓN\". Ensure it includes the exact ID \"CcRwXOu8gMRB1LGY\", version ID \"6b6a82c9-d454-4c59-a13f-5e489510f9e9\", and future timestamps \"2025-04-04T20:11:53.532Z\" for both `createdAt` and `updatedAt`.", "index": 19.0, "llm_score": 0.0, "json_parsed": 1.0, "top_level_keys_present": 4.0, "workflow_name_valid": 1.0, "num_nodes": 2.0, "num_nodes_with_valid_type": 1.0, "num_nodes_with_valid_version": 1.0, "num_nodes_with_valid_structure": 2.0, "num_nodes_with_parameters": 2.0, "num_connections": 1.0, "num_connections_with_valid_structure": 1.0, "num_connections_with_valid_target_node": 1.0, "active_field_boolean": 1.0, "percent_node_with_valid_type": 50.0, "percent_node_with_valid_version": 50.0, "percent_node_with_valid_structure": 100.0, "percent_connections_with_valid_structure": 100.0, "percent_connections_with_valid_target_node": 100.0, "percent_node_with_parameters": 100.0, "difficulty": "easy"}, {"prompt": "Generate a JSON object for a new, inactive workflow named \"My workflow 2\", including specific IDs and current timestamps. Ensure it has empty arrays for `nodes` and `tags`, empty objects for `connections` and `pinData`, and set `settings.executionOrder` to 'v1', `meta.templateCredsSetupCompleted` to `true`, `triggerCount` to `0`, and `staticData` to `null`.", "index": 6.0, "llm_score": 10.0, "json_parsed": 1.0, "top_level_keys_present": 4.0, "workflow_name_valid": 1.0, "num_nodes": 0.0, "num_nodes_with_valid_type": 0.0, "num_nodes_with_valid_version": 0.0, "num_nodes_with_valid_structure": 0.0, "num_nodes_with_parameters": 0.0, "num_connections": 0.0, "num_connections_with_valid_structure": 0.0, "num_connections_with_valid_target_node": 0.0, "active_field_boolean": 1.0, "percent_node_with_valid_type": null, "percent_node_with_valid_version": null, "percent_node_with_valid_structure": null, "percent_connections_with_valid_structure": null, "percent_connections_with_valid_target_node": null, "percent_node_with_parameters": null, "difficulty": "easy"}, {"prompt": "Generate a JSON object for a workflow configuration. Set `active` to `false`, `triggerCount` to `0`, and the `name` to 'CRAWL dataa QUAN LEN'. Include `id: \"f4lL0OCun8opuxQ\"`, `versionId: \"8dee12d6-4b1b-476e-8951-b6bcd12190d0\"`, and `createdAt` and `updatedAt` as the exact timestamp '2025-03-28T03:54:16.496Z'. Finally, set `settings` to `{'executionOrder': 'v1'}`, with all other array/object fields empty and `meta`/`staticData` as `null`.", "index": 11.0, "llm_score": 2.0, "json_parsed": 1.0, "top_level_keys_present": 4.0, "workflow_name_valid": 1.0, "num_nodes": 2.0, "num_nodes_with_valid_type": 2.0, "num_nodes_with_valid_version": 2.0, "num_nodes_with_valid_structure": 2.0, "num_nodes_with_parameters": 2.0, "num_connections": 1.0, "num_connections_with_valid_structure": 1.0, "num_connections_with_valid_target_node": 1.0, "active_field_boolean": 1.0, "percent_node_with_valid_type": 100.0, "percent_node_with_valid_version": 100.0, "percent_node_with_valid_structure": 100.0, "percent_connections_with_valid_structure": 100.0, "percent_connections_with_valid_target_node": 100.0, "percent_node_with_parameters": 100.0, "difficulty": "easy"}, {"prompt": "Generate a JSON object for a workflow named \"PostOnboardingJSON new\" that is inactive. Include current timestamps, unique IDs (a random string `id` and a UUID `versionId`), `settings` with `executionOrder: 'v1'`, `triggerCount: 0`, and ensure `nodes`, `connections`, `pinData`, `tags` are empty, while `staticData` and `meta` are null.", "index": 14.0, "llm_score": 10.0, "json_parsed": 1.0, "top_level_keys_present": 4.0, "workflow_name_valid": 1.0, "num_nodes": 0.0, "num_nodes_with_valid_type": 0.0, "num_nodes_with_valid_version": 0.0, "num_nodes_with_valid_structure": 0.0, "num_nodes_with_parameters": 0.0, "num_connections": 0.0, "num_connections_with_valid_structure": 0.0, "num_connections_with_valid_target_node": 0.0, "active_field_boolean": 1.0, "percent_node_with_valid_type": null, "percent_node_with_valid_version": null, "percent_node_with_valid_structure": null, "percent_connections_with_valid_structure": null, "percent_connections_with_valid_target_node": null, "percent_node_with_parameters": null, "difficulty": "easy"}, {"prompt": "Generate a JSON object for an inactive \"Fin Calls Reports\" configuration, setting `createdAt` and `updatedAt` to `2025-03-06T09:37:12.268Z`, `id` to `u3Y8NPVdzogwNo5s`, and `versionId` to `12ebf7f5-1254-486f-b5f1-b25795bf0ef4`. Ensure `settings.executionOrder` is `v1`, `triggerCount` is `0`, and all other array/object fields are empty or null.", "index": 9.0, "llm_score": 1.**********, "json_parsed": 1.0, "top_level_keys_present": 4.0, "workflow_name_valid": 1.0, "num_nodes": 1.**********, "num_nodes_with_valid_type": 1.0, "num_nodes_with_valid_version": 1.0, "num_nodes_with_valid_structure": 1.**********, "num_nodes_with_parameters": 1.**********, "num_connections": 0.**********, "num_connections_with_valid_structure": 0.**********, "num_connections_with_valid_target_node": 0.**********, "active_field_boolean": 1.0, "percent_node_with_valid_type": 66.**********, "percent_node_with_valid_version": 66.**********, "percent_node_with_valid_structure": 100.0, "percent_connections_with_valid_structure": 100.0, "percent_connections_with_valid_target_node": 100.0, "percent_node_with_parameters": 100.0, "difficulty": "easy"}, {"prompt": "Generate a JSON object for an inactive entry named 'ComputerUsetest', with both `createdAt` and `updatedAt` set to '2025-03-12T14:14:51.068Z'. Ensure it has `id` 'bCDw3w8IJky94a1z', `versionId` 'bc3cb97e-83b5-4dbe-a42a-0265d10b1db1', `settings` containing `executionOrder: v1`, and all other fields (e.g., `nodes`, `connections`, `staticData`) matching the empty/null states provided.", "index": 8.0, "llm_score": 10.0, "json_parsed": 1.0, "top_level_keys_present": 3.0, "workflow_name_valid": 1.0, "num_nodes": 0.0, "num_nodes_with_valid_type": 0.0, "num_nodes_with_valid_version": 0.0, "num_nodes_with_valid_structure": 0.0, "num_nodes_with_parameters": 0.0, "num_connections": 0.0, "num_connections_with_valid_structure": 0.0, "num_connections_with_valid_target_node": 0.0, "active_field_boolean": 0.0, "percent_node_with_valid_type": null, "percent_node_with_valid_version": null, "percent_node_with_valid_structure": null, "percent_connections_with_valid_structure": null, "percent_connections_with_valid_target_node": null, "percent_node_with_parameters": null, "difficulty": "easy"}, {"prompt": "Generate a JSON object for an inactive item named 'PostOnboardingJSON new', including current timestamps for `createdAt` and `updatedAt`, a unique `id` and UUID for `versionId`, and `triggerCount` as 0. Ensure `nodes` and `tags` are empty arrays, `connections` and `pinData` are empty objects, `staticData` and `meta` are `null`, and `settings` specify `{'executionOrder': 'v1'}`.", "index": 15.0, "llm_score": 10.0, "json_parsed": 1.0, "top_level_keys_present": 3.5, "workflow_name_valid": 1.0, "num_nodes": 0.0, "num_nodes_with_valid_type": 0.0, "num_nodes_with_valid_version": 0.0, "num_nodes_with_valid_structure": 0.0, "num_nodes_with_parameters": 0.0, "num_connections": 0.0, "num_connections_with_valid_structure": 0.0, "num_connections_with_valid_target_node": 0.0, "active_field_boolean": 0.5, "percent_node_with_valid_type": null, "percent_node_with_valid_version": null, "percent_node_with_valid_structure": null, "percent_connections_with_valid_structure": null, "percent_connections_with_valid_target_node": null, "percent_node_with_parameters": null, "difficulty": "easy"}, {"prompt": "Generate a JSON object for an inactive workflow definition named \"Email with attachement workflow\". Set its creation and update timestamps to January 10, 2025, ensure `executionOrder: 'v1'` in its settings, and include random unique IDs for `id` and `versionId`.", "index": 18.0, "llm_score": 10.0, "json_parsed": 1.0, "top_level_keys_present": 4.0, "workflow_name_valid": 1.0, "num_nodes": 1.0, "num_nodes_with_valid_type": 1.0, "num_nodes_with_valid_version": 1.0, "num_nodes_with_valid_structure": 1.0, "num_nodes_with_parameters": 1.0, "num_connections": 0.5, "num_connections_with_valid_structure": 0.5, "num_connections_with_valid_target_node": 0.5, "active_field_boolean": 1.0, "percent_node_with_valid_type": 100.0, "percent_node_with_valid_version": 100.0, "percent_node_with_valid_structure": 100.0, "percent_connections_with_valid_structure": 100.0, "percent_connections_with_valid_target_node": 100.0, "percent_node_with_parameters": 100.0, "difficulty": "easy"}, {"prompt": "Generate a JSON object for an inactive workflow named '+11 AI-Powered RAG Workflow For Stock Earnings Report Analysis', with `settings.executionOrder` set to 'v1', empty `nodes` and `connections`, and a `triggerCount` of 0.", "index": 20.0, "llm_score": 10.0, "json_parsed": 1.0, "top_level_keys_present": 4.0, "workflow_name_valid": 1.0, "num_nodes": 0.0, "num_nodes_with_valid_type": 0.0, "num_nodes_with_valid_version": 0.0, "num_nodes_with_valid_structure": 0.0, "num_nodes_with_parameters": 0.0, "num_connections": 0.0, "num_connections_with_valid_structure": 0.0, "num_connections_with_valid_target_node": 0.0, "active_field_boolean": 1.0, "percent_node_with_valid_type": null, "percent_node_with_valid_version": null, "percent_node_with_valid_structure": null, "percent_connections_with_valid_structure": null, "percent_connections_with_valid_target_node": null, "percent_node_with_parameters": null, "difficulty": "easy"}, {"prompt": "Generate a JSON object for an inactive workflow named 'CRAWL dataa QUAN LEN', with `createdAt` and `updatedAt` timestamps of '2025-03-28T03:54:16.496Z', `triggerCount` 0, and `settings.executionOrder` 'v1'. Ensure `meta` and `staticData` are null, `connections`, `nodes`, `pinData`, and `tags` are empty, and provide placeholder `id` and `versionId` strings.", "index": 12.0, "llm_score": 10.0, "json_parsed": 1.0, "top_level_keys_present": 4.0, "workflow_name_valid": 1.0, "num_nodes": 0.0, "num_nodes_with_valid_type": 0.0, "num_nodes_with_valid_version": 0.0, "num_nodes_with_valid_structure": 0.0, "num_nodes_with_parameters": 0.0, "num_connections": 0.0, "num_connections_with_valid_structure": 0.0, "num_connections_with_valid_target_node": 0.0, "active_field_boolean": 1.0, "percent_node_with_valid_type": null, "percent_node_with_valid_version": null, "percent_node_with_valid_structure": null, "percent_connections_with_valid_structure": null, "percent_connections_with_valid_target_node": null, "percent_node_with_parameters": null, "difficulty": "easy"}, {"prompt": "Generate a JSON object representing a n8n workflow named \"email extractor in airtable,\" including its ID, creation/update timestamps, status, and basic structure (empty nodes/connections). The workflow is currently inactive and uses execution order \"v1\".\n", "index": 17.0, "llm_score": 10.0, "json_parsed": 1.0, "top_level_keys_present": 4.0, "workflow_name_valid": 1.0, "num_nodes": 0.0, "num_nodes_with_valid_type": 0.0, "num_nodes_with_valid_version": 0.0, "num_nodes_with_valid_structure": 0.0, "num_nodes_with_parameters": 0.0, "num_connections": 0.0, "num_connections_with_valid_structure": 0.0, "num_connections_with_valid_target_node": 0.0, "active_field_boolean": 1.0, "percent_node_with_valid_type": null, "percent_node_with_valid_version": null, "percent_node_with_valid_structure": null, "percent_connections_with_valid_structure": null, "percent_connections_with_valid_target_node": null, "percent_node_with_parameters": null, "difficulty": "easy"}, {"prompt": "Generate a JSON object representing an n8n workflow for automating Zoom meeting creation, Stripe product and payment link generation, Google Sheets participant list management, and email notifications to both teachers and participants. The workflow should be triggered by a Form and a Stripe event, and include configuration options, conditional logic, and data formatting nodes.\n", "index": 29.0, "llm_score": 8.25, "json_parsed": 1.0, "top_level_keys_present": 4.0, "workflow_name_valid": 1.0, "num_nodes": 11.5, "num_nodes_with_valid_type": 10.75, "num_nodes_with_valid_version": 10.5, "num_nodes_with_valid_structure": 11.5, "num_nodes_with_parameters": 11.5, "num_connections": 10.0, "num_connections_with_valid_structure": 10.0, "num_connections_with_valid_target_node": 10.0, "active_field_boolean": 1.0, "percent_node_with_valid_type": 93.1818181818, "percent_node_with_valid_version": 90.9090909091, "percent_node_with_valid_structure": 100.0, "percent_connections_with_valid_structure": 100.0, "percent_connections_with_valid_target_node": 100.0, "percent_node_with_parameters": 100.0, "difficulty": "hard"}, {"prompt": "Generate a JSON object representing an n8n workflow for sending daily appointment reminders, including node definitions, connections, credentials, and settings. The workflow should handle different appointment types (Sessão Estratégica Comercial and Funil SS) and integrate with Botconversa and Clint.digital.\n", "index": 31.0, "llm_score": 9.0, "json_parsed": 1.0, "top_level_keys_present": 4.0, "workflow_name_valid": 1.0, "num_nodes": 5.75, "num_nodes_with_valid_type": 5.25, "num_nodes_with_valid_version": 5.25, "num_nodes_with_valid_structure": 5.75, "num_nodes_with_parameters": 5.75, "num_connections": 5.0, "num_connections_with_valid_structure": 5.0, "num_connections_with_valid_target_node": 5.0, "active_field_boolean": 1.0, "percent_node_with_valid_type": 92.8571428571, "percent_node_with_valid_version": 92.8571428571, "percent_node_with_valid_structure": 100.0, "percent_connections_with_valid_structure": 100.0, "percent_connections_with_valid_target_node": 100.0, "percent_node_with_parameters": 100.0, "difficulty": "hard"}, {"prompt": "Generate a JSON object representing an n8n workflow that sends WhatsApp messages at 12:00 and 17:00 on October 10, 2024, reading data from a Google Sheet, processing phone numbers, and using specific WhatsApp templates. The workflow includes error handling, data saving, and scheduling based on the current date.\n", "index": 39.0, "llm_score": 8.5, "json_parsed": 1.0, "top_level_keys_present": 4.0, "workflow_name_valid": 1.0, "num_nodes": 8.0, "num_nodes_with_valid_type": 5.25, "num_nodes_with_valid_version": 5.25, "num_nodes_with_valid_structure": 8.0, "num_nodes_with_parameters": 8.0, "num_connections": 6.25, "num_connections_with_valid_structure": 6.25, "num_connections_with_valid_target_node": 6.25, "active_field_boolean": 1.0, "percent_node_with_valid_type": 63.8392857143, "percent_node_with_valid_version": 63.8392857143, "percent_node_with_valid_structure": 100.0, "percent_connections_with_valid_structure": 100.0, "percent_connections_with_valid_target_node": 100.0, "percent_node_with_parameters": 100.0, "difficulty": "hard"}, {"prompt": "Generate a JSON object with `name: 'PostOnboardingJSON new'`, `active: false`, `triggerCount: 0`, and `settings.executionOrder: 'v1'`. Ensure all array and object fields are empty, `staticData` and `meta` are `null`, and include unique values for `id`, `versionId`, `createdAt`, and `updatedAt`.", "index": 13.0, "llm_score": 8.0, "json_parsed": 1.0, "top_level_keys_present": 4.0, "workflow_name_valid": 1.0, "num_nodes": 2.0, "num_nodes_with_valid_type": 1.0, "num_nodes_with_valid_version": 1.0, "num_nodes_with_valid_structure": 2.0, "num_nodes_with_parameters": 2.0, "num_connections": 1.0, "num_connections_with_valid_structure": 1.0, "num_connections_with_valid_target_node": 1.0, "active_field_boolean": 1.0, "percent_node_with_valid_type": 50.0, "percent_node_with_valid_version": 50.0, "percent_node_with_valid_structure": 100.0, "percent_connections_with_valid_structure": 100.0, "percent_connections_with_valid_target_node": 100.0, "percent_node_with_parameters": 100.0, "difficulty": "easy"}, {"prompt": "Generate a n8n workflow JSON that uses a schedule trigger to check a Redis key for \"idle\" status, then executes another workflow and updates the Redis key to \"running\" and then \"idle\". The workflow should also have troubleshooting nodes to reset the Redis flag in case of errors.\n", "index": 33.0, "llm_score": 9.0, "json_parsed": 1.0, "top_level_keys_present": 4.0, "workflow_name_valid": 1.0, "num_nodes": 7.75, "num_nodes_with_valid_type": 6.0, "num_nodes_with_valid_version": 4.25, "num_nodes_with_valid_structure": 7.75, "num_nodes_with_parameters": 7.75, "num_connections": 6.25, "num_connections_with_valid_structure": 6.25, "num_connections_with_valid_target_node": 6.25, "active_field_boolean": 1.0, "percent_node_with_valid_type": 75.4464285714, "percent_node_with_valid_version": 53.5714285714, "percent_node_with_valid_structure": 100.0, "percent_connections_with_valid_structure": 100.0, "percent_connections_with_valid_target_node": 100.0, "percent_node_with_parameters": 100.0, "difficulty": "hard"}, {"prompt": "Generate an active n8n workflow named \"AppTest n8n Onboarding Scaling Automation Solutions\". It should start with a Webhook node configured at `onboarding/n8n/tests/ScalingAutomationSolutions` that connects to a \"Respond to Webhook\" node. The \"Respond to Webhook\" node must return an HTML document that renders a \"Scaling Automation Solutions\" quiz, which upon submission or task completion, posts data to `https://auto.crm-s.com/webhook/Onboarding/Update` and `https://auto.crm-s.com/webhook/Onboarding/UpdateTaskTable` respectively. The workflow should also be tagged with \"n8n\", \"Tests\", \"Day5\", and \"Onboarding\".", "index": 1.0, "llm_score": 9.25, "json_parsed": 1.0, "top_level_keys_present": 4.0, "workflow_name_valid": 1.0, "num_nodes": 2.0, "num_nodes_with_valid_type": 2.0, "num_nodes_with_valid_version": 2.0, "num_nodes_with_valid_structure": 2.0, "num_nodes_with_parameters": 2.0, "num_connections": 1.0, "num_connections_with_valid_structure": 1.0, "num_connections_with_valid_target_node": 1.0, "active_field_boolean": 1.0, "percent_node_with_valid_type": 100.0, "percent_node_with_valid_version": 100.0, "percent_node_with_valid_structure": 100.0, "percent_connections_with_valid_structure": 100.0, "percent_connections_with_valid_target_node": 100.0, "percent_node_with_parameters": 100.0, "difficulty": "easy"}, {"prompt": "Generate an active n8n workflow named \"GetOnboardingFileIteration Methods\" that triggers via a webhook on the path \"Oboarding/PromptEngineer/IterationMethods\". The workflow should retrieve content from the Google Doc at \"https://docs.google.com/document/d/1sJVUFcSfL5POnNNc9IQOk17MS9vgJ1DdYvPi2su1KHI/edit?usp=drive_link\" and then respond to the webhook with that content, tagged as \"Day 2\", \"PromptEngineer\", \"Files\", and \"Onboarding\".", "index": 19.0, "llm_score": 8.5, "json_parsed": 1.0, "top_level_keys_present": 4.0, "workflow_name_valid": 1.0, "num_nodes": 3.5, "num_nodes_with_valid_type": 2.75, "num_nodes_with_valid_version": 2.75, "num_nodes_with_valid_structure": 3.5, "num_nodes_with_parameters": 3.5, "num_connections": 2.5, "num_connections_with_valid_structure": 2.5, "num_connections_with_valid_target_node": 2.5, "active_field_boolean": 1.0, "percent_node_with_valid_type": 79.1666666667, "percent_node_with_valid_version": 79.1666666667, "percent_node_with_valid_structure": 100.0, "percent_connections_with_valid_structure": 100.0, "percent_connections_with_valid_target_node": 100.0, "percent_node_with_parameters": 100.0, "difficulty": "medium"}, {"prompt": "Generate an inactive workflow JSON object named '[UDIA] Workflow de prueba'. It should be configured for the 'Europe/Madrid' timezone, include an unconfigured Schedule Trigger, and have two tags: '👁️‍🗨️Desarrollo' and '📍UDIA'.", "index": 16.0, "llm_score": 10.0, "json_parsed": 1.0, "top_level_keys_present": 4.0, "workflow_name_valid": 1.0, "num_nodes": 1.0, "num_nodes_with_valid_type": 0.75, "num_nodes_with_valid_version": 0.75, "num_nodes_with_valid_structure": 1.0, "num_nodes_with_parameters": 1.0, "num_connections": 0.0, "num_connections_with_valid_structure": 0.0, "num_connections_with_valid_target_node": 0.0, "active_field_boolean": 1.0, "percent_node_with_valid_type": 75.0, "percent_node_with_valid_version": 75.0, "percent_node_with_valid_structure": 100.0, "percent_connections_with_valid_structure": null, "percent_connections_with_valid_target_node": null, "percent_node_with_parameters": 100.0, "difficulty": "easy"}, {"prompt": "Generate an n8n workflow JSON configuration that retrieves tickets from the Linear API for a specific team, handles pagination, applies custom data, and writes the data to a Google Sheet. The workflow should include nodes for scheduling, Linear API interaction, conditional logic, data transformation, and Google Sheets integration.\n", "index": 28.0, "llm_score": 5.375, "json_parsed": 1.0, "top_level_keys_present": 4.0, "workflow_name_valid": 1.0, "num_nodes": 5.**********, "num_nodes_with_valid_type": 5.**********, "num_nodes_with_valid_version": 5.**********, "num_nodes_with_valid_structure": 5.**********, "num_nodes_with_parameters": 5.**********, "num_connections": 4.**********, "num_connections_with_valid_structure": 4.**********, "num_connections_with_valid_target_node": 4.**********, "active_field_boolean": 1.0, "percent_node_with_valid_type": 100.0, "percent_node_with_valid_version": 100.0, "percent_node_with_valid_structure": 100.0, "percent_connections_with_valid_structure": 100.0, "percent_connections_with_valid_target_node": 100.0, "percent_node_with_parameters": 100.0, "difficulty": "hard"}, {"prompt": "Generate an n8n workflow JSON for YouTube channel analysis, including parsing URLs, fetching channel/video data, calculating metrics, and using LLMs for generating channel analysis reports, content ideas, and copywriting suggestions. The workflow should include nodes for webhook trigger, data parsing, API calls to YouTube and OpenRouter, and data transformation for report compilation.\n", "index": 27.0, "llm_score": 8.375, "json_parsed": 1.0, "top_level_keys_present": 4.0, "workflow_name_valid": 1.0, "num_nodes": 8.75, "num_nodes_with_valid_type": 8.25, "num_nodes_with_valid_version": 8.25, "num_nodes_with_valid_structure": 8.75, "num_nodes_with_parameters": 8.75, "num_connections": 8.5, "num_connections_with_valid_structure": 8.5, "num_connections_with_valid_target_node": 8.5, "active_field_boolean": 1.0, "percent_node_with_valid_type": 94.7222222222, "percent_node_with_valid_version": 94.7222222222, "percent_node_with_valid_structure": 100.0, "percent_connections_with_valid_structure": 100.0, "percent_connections_with_valid_target_node": 100.0, "percent_node_with_parameters": 100.0, "difficulty": "hard"}, {"prompt": "Generate an n8n workflow JSON for fetching content from a specific Google Docs document (\"https://docs.google.com/document/d/1SWrpxi5jfVL79ftSA1u3PZL_wuzcfYFsA_5g02Kuet0/view\") upon receiving a webhook and responding with the document's text content. Include webhook, Google Docs (using the \"u4aaDpAzm6YKZV9W\" credential), and Respond to Webhook nodes.\n", "index": 5.0, "llm_score": 7.75, "json_parsed": 1.0, "top_level_keys_present": 4.0, "workflow_name_valid": 1.0, "num_nodes": 3.0, "num_nodes_with_valid_type": 2.75, "num_nodes_with_valid_version": 2.75, "num_nodes_with_valid_structure": 3.0, "num_nodes_with_parameters": 3.0, "num_connections": 2.0, "num_connections_with_valid_structure": 2.0, "num_connections_with_valid_target_node": 2.0, "active_field_boolean": 1.0, "percent_node_with_valid_type": 91.**********, "percent_node_with_valid_version": 91.**********, "percent_node_with_valid_structure": 100.0, "percent_connections_with_valid_structure": 100.0, "percent_connections_with_valid_target_node": 100.0, "percent_node_with_parameters": 100.0, "difficulty": "medium"}, {"prompt": "Generate an n8n workflow JSON representing a node that filters data based on whether a profile name contains \"an\". The workflow starts with a manual trigger, edits fields, uses an IF node to check for \"an\" in the profile name, and outputs to \"True\" and \"False\" nodes.\n", "index": 7.0, "llm_score": 9.5, "json_parsed": 1.0, "top_level_keys_present": 4.0, "workflow_name_valid": 1.0, "num_nodes": 5.0, "num_nodes_with_valid_type": 4.5, "num_nodes_with_valid_version": 4.5, "num_nodes_with_valid_structure": 5.0, "num_nodes_with_parameters": 5.0, "num_connections": 4.0, "num_connections_with_valid_structure": 4.0, "num_connections_with_valid_target_node": 4.0, "active_field_boolean": 1.0, "percent_node_with_valid_type": 90.0, "percent_node_with_valid_version": 90.0, "percent_node_with_valid_structure": 100.0, "percent_connections_with_valid_structure": 100.0, "percent_connections_with_valid_target_node": 100.0, "percent_node_with_parameters": 100.0, "difficulty": "medium"}, {"prompt": "Generate an n8n workflow for a \"Contact Agent\" that uses OpenAI, Airtable (for contact search and upsert), and a Langchain Agent to manage contact information based on a query. The workflow should include error handling and a trigger for execution by another workflow.\n", "index": 36.0, "llm_score": 6.875, "json_parsed": 1.0, "top_level_keys_present": 4.0, "workflow_name_valid": 1.0, "num_nodes": 5.75, "num_nodes_with_valid_type": 4.5, "num_nodes_with_valid_version": 4.5, "num_nodes_with_valid_structure": 5.75, "num_nodes_with_parameters": 5.75, "num_connections": 6.0, "num_connections_with_valid_structure": 6.0, "num_connections_with_valid_target_node": 6.0, "active_field_boolean": 1.0, "percent_node_with_valid_type": 77.6785714286, "percent_node_with_valid_version": 77.6785714286, "percent_node_with_valid_structure": 100.0, "percent_connections_with_valid_structure": 100.0, "percent_connections_with_valid_target_node": 100.0, "percent_node_with_parameters": 100.0, "difficulty": "hard"}, {"prompt": "Generate an n8n workflow named \"Agent:Tools:OpenAI\" that showcases AI agent functionalities, including calling various tools (custom workflow tools, code, and HTTP requests), processing structured output, and managing conversational memory with OpenAI chat models.", "index": 43.0, "llm_score": 6.5, "json_parsed": 1.0, "top_level_keys_present": 4.0, "workflow_name_valid": 1.0, "num_nodes": 6.5, "num_nodes_with_valid_type": 5.5, "num_nodes_with_valid_version": 5.5, "num_nodes_with_valid_structure": 6.5, "num_nodes_with_parameters": 6.5, "num_connections": 6.0, "num_connections_with_valid_structure": 6.0, "num_connections_with_valid_target_node": 6.0, "active_field_boolean": 1.0, "percent_node_with_valid_type": 85.3571428571, "percent_node_with_valid_version": 85.3571428571, "percent_node_with_valid_structure": 100.0, "percent_connections_with_valid_structure": 100.0, "percent_connections_with_valid_target_node": 100.0, "percent_node_with_parameters": 100.0, "difficulty": "hard"}, {"prompt": "Generate an n8n workflow named 'Tutorial n8n - nocodb' that manually triggers. It should first retrieve all NocoDB records from `idtable` in `pcdrdmidko5oz5y` where `estado` is `dormido`, and then update records in `gguidtable` in `idproyecto`, using the `Id` from the retrieved data and setting `texto` to 'es una prueba'.", "index": 14.0, "llm_score": 9.0, "json_parsed": 1.0, "top_level_keys_present": 4.0, "workflow_name_valid": 1.0, "num_nodes": 4.0, "num_nodes_with_valid_type": 2.5, "num_nodes_with_valid_version": 2.5, "num_nodes_with_valid_structure": 4.0, "num_nodes_with_parameters": 4.0, "num_connections": 3.0, "num_connections_with_valid_structure": 3.0, "num_connections_with_valid_target_node": 3.0, "active_field_boolean": 1.0, "percent_node_with_valid_type": 66.25, "percent_node_with_valid_version": 66.25, "percent_node_with_valid_structure": 100.0, "percent_connections_with_valid_structure": 100.0, "percent_connections_with_valid_target_node": 100.0, "percent_node_with_parameters": 100.0, "difficulty": "medium"}, {"prompt": "Generate an n8n workflow that automates AI news aggregation from Google Sheets, fetches website content, appends the data back to Google Sheets, and sends notifications via KakaoTalk. The workflow should use MCP triggers for AI News and KakaoTalk servers, and employ n8n Workflow tools for web content retrieval and KakaoTalk messaging.\n", "index": 32.0, "llm_score": 6.75, "json_parsed": 1.0, "top_level_keys_present": 4.0, "workflow_name_valid": 1.0, "num_nodes": 6.5, "num_nodes_with_valid_type": 3.0, "num_nodes_with_valid_version": 3.0, "num_nodes_with_valid_structure": 6.5, "num_nodes_with_parameters": 6.5, "num_connections": 5.25, "num_connections_with_valid_structure": 5.25, "num_connections_with_valid_target_node": 5.25, "active_field_boolean": 1.0, "percent_node_with_valid_type": 40.1785714286, "percent_node_with_valid_version": 40.1785714286, "percent_node_with_valid_structure": 100.0, "percent_connections_with_valid_structure": 100.0, "percent_connections_with_valid_target_node": 100.0, "percent_node_with_parameters": 100.0, "difficulty": "hard"}, {"prompt": "Generate an n8n workflow that automatically responds to appointment requests from Gmail, using Langchain and OpenAI to classify emails, check Google Calendar availability, and compose a reply with proposed meeting times. The workflow should also mark emails as read upon completion.\n", "index": 34.0, "llm_score": 6.5, "json_parsed": 1.0, "top_level_keys_present": 4.0, "workflow_name_valid": 1.0, "num_nodes": 7.0, "num_nodes_with_valid_type": 6.5, "num_nodes_with_valid_version": 6.5, "num_nodes_with_valid_structure": 7.0, "num_nodes_with_parameters": 7.0, "num_connections": 6.0, "num_connections_with_valid_structure": 6.0, "num_connections_with_valid_target_node": 6.0, "active_field_boolean": 1.0, "percent_node_with_valid_type": 92.8571428571, "percent_node_with_valid_version": 92.8571428571, "percent_node_with_valid_structure": 100.0, "percent_connections_with_valid_structure": 100.0, "percent_connections_with_valid_target_node": 100.0, "percent_node_with_parameters": 100.0, "difficulty": "hard"}, {"prompt": "Generate an n8n workflow that begins with a manual trigger. This trigger should then connect to a Set node which stores a multi-book XML library string into a field named 'body'.", "index": 4.0, "llm_score": 10.0, "json_parsed": 1.0, "top_level_keys_present": 4.0, "workflow_name_valid": 1.0, "num_nodes": 2.0, "num_nodes_with_valid_type": 1.75, "num_nodes_with_valid_version": 1.75, "num_nodes_with_valid_structure": 2.0, "num_nodes_with_parameters": 2.0, "num_connections": 1.0, "num_connections_with_valid_structure": 1.0, "num_connections_with_valid_target_node": 1.0, "active_field_boolean": 1.0, "percent_node_with_valid_type": 87.5, "percent_node_with_valid_version": 87.5, "percent_node_with_valid_structure": 100.0, "percent_connections_with_valid_structure": 100.0, "percent_connections_with_valid_target_node": 100.0, "percent_node_with_parameters": 100.0, "difficulty": "easy"}, {"prompt": "Generate an n8n workflow that manually triggers a Shopify node to retrieve all records, using 'shopify_creds'.", "index": 21.0, "llm_score": 8.0, "json_parsed": 1.0, "top_level_keys_present": 4.0, "workflow_name_valid": 1.0, "num_nodes": 2.0, "num_nodes_with_valid_type": 1.5, "num_nodes_with_valid_version": 1.5, "num_nodes_with_valid_structure": 2.0, "num_nodes_with_parameters": 2.0, "num_connections": 1.0, "num_connections_with_valid_structure": 1.0, "num_connections_with_valid_target_node": 1.0, "active_field_boolean": 1.0, "percent_node_with_valid_type": 75.0, "percent_node_with_valid_version": 75.0, "percent_node_with_valid_structure": 100.0, "percent_connections_with_valid_structure": 100.0, "percent_connections_with_valid_target_node": 100.0, "percent_node_with_parameters": 100.0, "difficulty": "medium"}, {"prompt": "Generate an n8n workflow that manually triggers to scrape Google Custom Search results for \"CEO real estate Chicago\". It should paginate requests by incrementing the `start` parameter by 10 for up to 100 results, extract the `link` from each `item` in the response, and then append these URLs to a Google Sheet.", "index": 42.0, "llm_score": 8.25, "json_parsed": 1.0, "top_level_keys_present": 4.0, "workflow_name_valid": 1.0, "num_nodes": 7.25, "num_nodes_with_valid_type": 6.75, "num_nodes_with_valid_version": 6.75, "num_nodes_with_valid_structure": 7.25, "num_nodes_with_parameters": 7.25, "num_connections": 6.25, "num_connections_with_valid_structure": 6.25, "num_connections_with_valid_target_node": 6.25, "active_field_boolean": 1.0, "percent_node_with_valid_type": 91.**********, "percent_node_with_valid_version": 91.**********, "percent_node_with_valid_structure": 100.0, "percent_connections_with_valid_structure": 100.0, "percent_connections_with_valid_target_node": 100.0, "percent_node_with_parameters": 100.0, "difficulty": "hard"}, {"prompt": "Generate an n8n workflow that provides daily stock trade recommendations, scheduled to run every weekday morning at 9:45 AM. It should use an Anthropic AI agent to analyze specific assets like Microsoft, Meta, Nvidia, Schwab Dividend ETF, Vanguard SP500, and Vanguard Total Market, leveraging specialized NASDAQ, NYSE, and NYSE Arca stock analysis tools, along with news sources, and then deliver the recommendations via email.", "index": 41.0, "llm_score": 8.75, "json_parsed": 1.0, "top_level_keys_present": 4.0, "workflow_name_valid": 1.0, "num_nodes": 3.5, "num_nodes_with_valid_type": 2.0, "num_nodes_with_valid_version": 2.0, "num_nodes_with_valid_structure": 3.5, "num_nodes_with_parameters": 3.5, "num_connections": 2.5, "num_connections_with_valid_structure": 2.5, "num_connections_with_valid_target_node": 2.5, "active_field_boolean": 1.0, "percent_node_with_valid_type": 56.**********, "percent_node_with_valid_version": 56.**********, "percent_node_with_valid_structure": 100.0, "percent_connections_with_valid_structure": 100.0, "percent_connections_with_valid_target_node": 100.0, "percent_node_with_parameters": 100.0, "difficulty": "hard"}, {"prompt": "Generate an n8n workflow that triggers manually and posts a tweet with the text \"This is a test workflow for the twitter node\" via the Twitter node using 'twitter-credentials'.", "index": 20.0, "llm_score": 8.75, "json_parsed": 1.0, "top_level_keys_present": 4.0, "workflow_name_valid": 1.0, "num_nodes": 2.0, "num_nodes_with_valid_type": 1.5, "num_nodes_with_valid_version": 1.5, "num_nodes_with_valid_structure": 2.0, "num_nodes_with_parameters": 2.0, "num_connections": 1.0, "num_connections_with_valid_structure": 1.0, "num_connections_with_valid_target_node": 1.0, "active_field_boolean": 1.0, "percent_node_with_valid_type": 75.0, "percent_node_with_valid_version": 75.0, "percent_node_with_valid_structure": 100.0, "percent_connections_with_valid_structure": 100.0, "percent_connections_with_valid_target_node": 100.0, "percent_node_with_parameters": 100.0, "difficulty": "medium"}, {"prompt": "Generate an n8n workflow that triggers on a Telegram message. If the message text contains \"lies meine email\", it should fetch the 10 most recent Gmail messages, aggregate their snippets for summarization by the DeepSeek API, and then send the generated summary back to the original Telegram chat.", "index": 44.0, "llm_score": 9.0, "json_parsed": 1.0, "top_level_keys_present": 4.0, "workflow_name_valid": 1.0, "num_nodes": 6.5, "num_nodes_with_valid_type": 5.75, "num_nodes_with_valid_version": 5.75, "num_nodes_with_valid_structure": 6.5, "num_nodes_with_parameters": 6.5, "num_connections": 5.75, "num_connections_with_valid_structure": 5.75, "num_connections_with_valid_target_node": 5.75, "active_field_boolean": 1.0, "percent_node_with_valid_type": 88.5416666667, "percent_node_with_valid_version": 88.5416666667, "percent_node_with_valid_structure": 100.0, "percent_connections_with_valid_structure": 100.0, "percent_connections_with_valid_target_node": 100.0, "percent_node_with_parameters": 100.0, "difficulty": "hard"}, {"prompt": "Generate an n8n workflow that triggers, then uses an OpenAI `gpt-4o-mini` node to parse an input query for city, state, latitude, longitude, and a 'Length' (Single for current weather, Multiple for a 5-day forecast). An If node should then branch execution: if 'Length' is 'Multiple', fetch a 5-day forecast; otherwise, fetch current weather using the OpenWeatherMap node (both in imperial format, using extracted coordinates), and return the final data.", "index": 46.0, "llm_score": 7.75, "json_parsed": 1.0, "top_level_keys_present": 4.0, "workflow_name_valid": 1.0, "num_nodes": 5.5, "num_nodes_with_valid_type": 3.25, "num_nodes_with_valid_version": 3.25, "num_nodes_with_valid_structure": 5.5, "num_nodes_with_parameters": 5.5, "num_connections": 4.75, "num_connections_with_valid_structure": 4.75, "num_connections_with_valid_target_node": 4.75, "active_field_boolean": 1.0, "percent_node_with_valid_type": 57.8571428571, "percent_node_with_valid_version": 57.8571428571, "percent_node_with_valid_structure": 100.0, "percent_connections_with_valid_structure": 100.0, "percent_connections_with_valid_target_node": 100.0, "percent_node_with_parameters": 100.0, "difficulty": "hard"}, {"prompt": "Generate an n8n workflow that uses a Cron trigger to check GitHub notifications every minute, filters for new notifications in the last minute, and sends a formatted Discord message if there are any. The workflow should include nodes for fetching data, calculating time, conditional logic, and messaging.\n", "index": 37.0, "llm_score": 9.375, "json_parsed": 1.0, "top_level_keys_present": 4.0, "workflow_name_valid": 1.0, "num_nodes": 5.5, "num_nodes_with_valid_type": 5.5, "num_nodes_with_valid_version": 5.5, "num_nodes_with_valid_structure": 5.5, "num_nodes_with_parameters": 5.5, "num_connections": 4.5, "num_connections_with_valid_structure": 4.5, "num_connections_with_valid_target_node": 4.5, "active_field_boolean": 1.0, "percent_node_with_valid_type": 100.0, "percent_node_with_valid_version": 100.0, "percent_node_with_valid_structure": 100.0, "percent_connections_with_valid_structure": 100.0, "percent_connections_with_valid_target_node": 100.0, "percent_node_with_parameters": 100.0, "difficulty": "hard"}, {"prompt": "Generate an n8n workflow that, upon receiving a webhook request, conditionally creates a new Xero contact (fetching client data from NocoDB if needed) or retrieves an existing one, then dynamically constructs Xero quote line items from various input data fields, and finally retrieves Xero invoice data to respond to the webhook.", "index": 40.0, "llm_score": 5.5, "json_parsed": 1.0, "top_level_keys_present": 4.0, "workflow_name_valid": 1.0, "num_nodes": 9.75, "num_nodes_with_valid_type": 9.0, "num_nodes_with_valid_version": 9.0, "num_nodes_with_valid_structure": 9.75, "num_nodes_with_parameters": 9.75, "num_connections": 8.25, "num_connections_with_valid_structure": 8.25, "num_connections_with_valid_target_node": 8.25, "active_field_boolean": 1.0, "percent_node_with_valid_type": 91.9444444444, "percent_node_with_valid_version": 91.9444444444, "percent_node_with_valid_structure": 100.0, "percent_connections_with_valid_structure": 100.0, "percent_connections_with_valid_target_node": 100.0, "percent_node_with_parameters": 100.0, "difficulty": "hard"}, {"prompt": "Write a JSON object representing an n8n workflow for an AI agent that scrapes webpages, processes the content, and uses a ReAct agent for interaction, including nodes for manual chat, OpenAI chat model, HTTP requests, HTML extraction, and Markdown conversion. The workflow uses query parameters for URL, method (full/simplified), and max page length, and includes error handling and content simplification options.\n", "index": 35.0, "llm_score": 6.25, "json_parsed": 1.0, "top_level_keys_present": 4.0, "workflow_name_valid": 1.0, "num_nodes": 6.75, "num_nodes_with_valid_type": 4.5, "num_nodes_with_valid_version": 4.5, "num_nodes_with_valid_structure": 6.75, "num_nodes_with_parameters": 6.75, "num_connections": 5.5, "num_connections_with_valid_structure": 5.5, "num_connections_with_valid_target_node": 5.5, "active_field_boolean": 1.0, "percent_node_with_valid_type": 65.4166666667, "percent_node_with_valid_version": 65.4166666667, "percent_node_with_valid_structure": 100.0, "percent_connections_with_valid_structure": 100.0, "percent_connections_with_valid_target_node": 100.0, "percent_node_with_parameters": 100.0, "difficulty": "hard"}]