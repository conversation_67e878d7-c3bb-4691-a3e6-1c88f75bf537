{"cells": [{"cell_type": "code", "execution_count": 94, "id": "cf9d20bd", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import seaborn as sns"]}, {"cell_type": "code", "execution_count": 95, "id": "2a29a094", "metadata": {}, "outputs": [], "source": ["df = pd.read_json(\"workflow_ref.json_medium_mean.json\")"]}, {"cell_type": "code", "execution_count": 96, "id": "55ed4ffb", "metadata": {}, "outputs": [{"data": {"application/vnd.microsoft.datawrangler.viewer.v0+json": {"columns": [{"name": "index", "rawType": "int64", "type": "integer"}, {"name": "prompt", "rawType": "object", "type": "string"}, {"name": "index", "rawType": "int64", "type": "integer"}, {"name": "llm_score", "rawType": "float64", "type": "float"}, {"name": "json_parsed", "rawType": "int64", "type": "integer"}, {"name": "top_level_keys_present", "rawType": "float64", "type": "float"}, {"name": "workflow_name_valid", "rawType": "int64", "type": "integer"}, {"name": "num_nodes", "rawType": "float64", "type": "float"}, {"name": "num_nodes_with_valid_type", "rawType": "float64", "type": "float"}, {"name": "num_nodes_with_valid_version", "rawType": "float64", "type": "float"}, {"name": "num_nodes_with_valid_structure", "rawType": "float64", "type": "float"}, {"name": "num_nodes_with_parameters", "rawType": "float64", "type": "float"}, {"name": "num_connections", "rawType": "float64", "type": "float"}, {"name": "num_connections_with_valid_structure", "rawType": "float64", "type": "float"}, {"name": "num_connections_with_valid_target_node", "rawType": "float64", "type": "float"}, {"name": "active_field_boolean", "rawType": "float64", "type": "float"}, {"name": "percent_node_with_valid_type", "rawType": "float64", "type": "float"}, {"name": "percent_node_with_valid_version", "rawType": "float64", "type": "float"}, {"name": "percent_node_with_valid_structure", "rawType": "float64", "type": "float"}, {"name": "percent_connections_with_valid_structure", "rawType": "float64", "type": "float"}, {"name": "percent_connections_with_valid_target_node", "rawType": "float64", "type": "float"}, {"name": "percent_node_with_parameters", "rawType": "float64", "type": "float"}, {"name": "difficulty", "rawType": "object", "type": "string"}], "ref": "fb28972e-1718-4476-842b-50d1309f788a", "rows": [["0", "Create a manually triggered workflow that first sets a `woocommerce url` variable. Then, make an authenticated HTTP POST request using WordPress API credentials to the `wp-json/rank-math-api/v1/update-meta` endpoint on that URL, updating Rank Math SEO meta for `post_id` 246 with a specific title, description, and canonical URL, configured to retry on failure.", "12", "10.0", "1", "4.0", "1", "3.0", "3.0", "3.0", "3.0", "3.0", "2.0", "2.0", "2.0", "1.0", "100.0", "100.0", "100.0", "100.0", "100.0", "100.0", "medium"], ["1", "Create a workflow that manually triggers and sets a hardcoded phone number to \"+34605281220\". Then, use the `uproc` node with the `miquel-uproc` credential and `getPhoneParsed` tool to parse and validate this number, followed by an If node that checks if the phone is valid based on the parsing result.", "13", "8.75", "1", "4.0", "1", "4.0", "4.0", "3.75", "4.0", "4.0", "3.0", "3.0", "2.5", "1.0", "100.0", "93.75", "100.0", "100.0", "83.3333333333", "100.0", "medium"], ["2", "Create a workflow that triggers on Google Sheets updates, checks for Telegram links, retrieves reactions, and updates the Google Sheet. The workflow uses HTTP requests, code, conditionals, and Google Sheets nodes to automate the process of extracting and storing Telegram message reaction data.\n", "26", "5.75", "1", "4.0", "1", "5.75", "5.75", "5.75", "5.75", "5.75", "4.75", "4.75", "4.75", "1.0", "100.0", "100.0", "100.0", "100.0", "100.0", "100.0", "hard"], ["3", "Create a workflow that, when manually triggered, reads the file `/files/tmp/tst1.mp4` from disk, then sends it as `multipart/form-data` to `https://api.elevenlabs.io/v1/speech-to-text` (POST method) using the 'Eleven Labs' credential, specifying `model_id` as `scribe_v1`.", "25", "9.25", "1", "4.0", "1", "2.75", "2.0", "2.0", "2.75", "2.75", "1.75", "1.75", "1.75", "1.0", "70.8333333333", "70.8333333333", "100.0", "100.0", "100.0", "100.0", "medium"], ["4", "Create an inactive n8n workflow named \"Twilio Test\" that starts with a Twilio Trigger node. This trigger should activate when an inbound message is received and use existing Twilio API credentials.", "2", "9.5", "1", "4.0", "1", "1.0", "0.75", "0.75", "1.0", "1.0", "0.0", "0.0", "0.0", "1.0", "75.0", "75.0", "100.0", null, null, "100.0", "easy"]], "shape": {"columns": 22, "rows": 5}}, "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>prompt</th>\n", "      <th>index</th>\n", "      <th>llm_score</th>\n", "      <th>json_parsed</th>\n", "      <th>top_level_keys_present</th>\n", "      <th>workflow_name_valid</th>\n", "      <th>num_nodes</th>\n", "      <th>num_nodes_with_valid_type</th>\n", "      <th>num_nodes_with_valid_version</th>\n", "      <th>num_nodes_with_valid_structure</th>\n", "      <th>...</th>\n", "      <th>num_connections_with_valid_structure</th>\n", "      <th>num_connections_with_valid_target_node</th>\n", "      <th>active_field_boolean</th>\n", "      <th>percent_node_with_valid_type</th>\n", "      <th>percent_node_with_valid_version</th>\n", "      <th>percent_node_with_valid_structure</th>\n", "      <th>percent_connections_with_valid_structure</th>\n", "      <th>percent_connections_with_valid_target_node</th>\n", "      <th>percent_node_with_parameters</th>\n", "      <th>difficulty</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>Create a manually triggered workflow that firs...</td>\n", "      <td>12</td>\n", "      <td>10.00</td>\n", "      <td>1</td>\n", "      <td>4.0</td>\n", "      <td>1</td>\n", "      <td>3.00</td>\n", "      <td>3.00</td>\n", "      <td>3.00</td>\n", "      <td>3.00</td>\n", "      <td>...</td>\n", "      <td>2.00</td>\n", "      <td>2.00</td>\n", "      <td>1.0</td>\n", "      <td>100.000000</td>\n", "      <td>100.000000</td>\n", "      <td>100.0</td>\n", "      <td>100.0</td>\n", "      <td>100.000000</td>\n", "      <td>100.0</td>\n", "      <td>medium</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>Create a workflow that manually triggers and s...</td>\n", "      <td>13</td>\n", "      <td>8.75</td>\n", "      <td>1</td>\n", "      <td>4.0</td>\n", "      <td>1</td>\n", "      <td>4.00</td>\n", "      <td>4.00</td>\n", "      <td>3.75</td>\n", "      <td>4.00</td>\n", "      <td>...</td>\n", "      <td>3.00</td>\n", "      <td>2.50</td>\n", "      <td>1.0</td>\n", "      <td>100.000000</td>\n", "      <td>93.750000</td>\n", "      <td>100.0</td>\n", "      <td>100.0</td>\n", "      <td>83.333333</td>\n", "      <td>100.0</td>\n", "      <td>medium</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>Create a workflow that triggers on Google Shee...</td>\n", "      <td>26</td>\n", "      <td>5.75</td>\n", "      <td>1</td>\n", "      <td>4.0</td>\n", "      <td>1</td>\n", "      <td>5.75</td>\n", "      <td>5.75</td>\n", "      <td>5.75</td>\n", "      <td>5.75</td>\n", "      <td>...</td>\n", "      <td>4.75</td>\n", "      <td>4.75</td>\n", "      <td>1.0</td>\n", "      <td>100.000000</td>\n", "      <td>100.000000</td>\n", "      <td>100.0</td>\n", "      <td>100.0</td>\n", "      <td>100.000000</td>\n", "      <td>100.0</td>\n", "      <td>hard</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>Create a workflow that, when manually triggere...</td>\n", "      <td>25</td>\n", "      <td>9.25</td>\n", "      <td>1</td>\n", "      <td>4.0</td>\n", "      <td>1</td>\n", "      <td>2.75</td>\n", "      <td>2.00</td>\n", "      <td>2.00</td>\n", "      <td>2.75</td>\n", "      <td>...</td>\n", "      <td>1.75</td>\n", "      <td>1.75</td>\n", "      <td>1.0</td>\n", "      <td>70.833333</td>\n", "      <td>70.833333</td>\n", "      <td>100.0</td>\n", "      <td>100.0</td>\n", "      <td>100.000000</td>\n", "      <td>100.0</td>\n", "      <td>medium</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>Create an inactive n8n workflow named \"Twilio ...</td>\n", "      <td>2</td>\n", "      <td>9.50</td>\n", "      <td>1</td>\n", "      <td>4.0</td>\n", "      <td>1</td>\n", "      <td>1.00</td>\n", "      <td>0.75</td>\n", "      <td>0.75</td>\n", "      <td>1.00</td>\n", "      <td>...</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>1.0</td>\n", "      <td>75.000000</td>\n", "      <td>75.000000</td>\n", "      <td>100.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>100.0</td>\n", "      <td>easy</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>5 rows × 22 columns</p>\n", "</div>"], "text/plain": ["                                              prompt  index  llm_score  \\\n", "0  Create a manually triggered workflow that firs...     12      10.00   \n", "1  Create a workflow that manually triggers and s...     13       8.75   \n", "2  Create a workflow that triggers on Google Shee...     26       5.75   \n", "3  Create a workflow that, when manually triggere...     25       9.25   \n", "4  Create an inactive n8n workflow named \"Twilio ...      2       9.50   \n", "\n", "   json_parsed  top_level_keys_present  workflow_name_valid  num_nodes  \\\n", "0            1                     4.0                    1       3.00   \n", "1            1                     4.0                    1       4.00   \n", "2            1                     4.0                    1       5.75   \n", "3            1                     4.0                    1       2.75   \n", "4            1                     4.0                    1       1.00   \n", "\n", "   num_nodes_with_valid_type  num_nodes_with_valid_version  \\\n", "0                       3.00                          3.00   \n", "1                       4.00                          3.75   \n", "2                       5.75                          5.75   \n", "3                       2.00                          2.00   \n", "4                       0.75                          0.75   \n", "\n", "   num_nodes_with_valid_structure  ...  num_connections_with_valid_structure  \\\n", "0                            3.00  ...                                  2.00   \n", "1                            4.00  ...                                  3.00   \n", "2                            5.75  ...                                  4.75   \n", "3                            2.75  ...                                  1.75   \n", "4                            1.00  ...                                  0.00   \n", "\n", "   num_connections_with_valid_target_node  active_field_boolean  \\\n", "0                                    2.00                   1.0   \n", "1                                    2.50                   1.0   \n", "2                                    4.75                   1.0   \n", "3                                    1.75                   1.0   \n", "4                                    0.00                   1.0   \n", "\n", "   percent_node_with_valid_type  percent_node_with_valid_version  \\\n", "0                    100.000000                       100.000000   \n", "1                    100.000000                        93.750000   \n", "2                    100.000000                       100.000000   \n", "3                     70.833333                        70.833333   \n", "4                     75.000000                        75.000000   \n", "\n", "   percent_node_with_valid_structure  \\\n", "0                              100.0   \n", "1                              100.0   \n", "2                              100.0   \n", "3                              100.0   \n", "4                              100.0   \n", "\n", "   percent_connections_with_valid_structure  \\\n", "0                                     100.0   \n", "1                                     100.0   \n", "2                                     100.0   \n", "3                                     100.0   \n", "4                                       NaN   \n", "\n", "   percent_connections_with_valid_target_node  percent_node_with_parameters  \\\n", "0                                  100.000000                         100.0   \n", "1                                   83.333333                         100.0   \n", "2                                  100.000000                         100.0   \n", "3                                  100.000000                         100.0   \n", "4                                         NaN                         100.0   \n", "\n", "   difficulty  \n", "0      medium  \n", "1      medium  \n", "2        hard  \n", "3      medium  \n", "4        easy  \n", "\n", "[5 rows x 22 columns]"]}, "execution_count": 96, "metadata": {}, "output_type": "execute_result"}], "source": ["df.head()"]}, {"cell_type": "markdown", "id": "e459bb7b", "metadata": {}, "source": ["## Overall"]}, {"cell_type": "code", "execution_count": 97, "id": "aeaeeef2", "metadata": {}, "outputs": [{"data": {"text/plain": ["<Axes: xlabel='llm_score', ylabel='Count'>"]}, "execution_count": 97, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["sns.histplot(df, x=\"llm_score\", bins=20)"]}, {"cell_type": "code", "execution_count": 98, "id": "8825d978", "metadata": {}, "outputs": [{"data": {"text/plain": ["np.float64(0.75)"]}, "execution_count": 98, "metadata": {}, "output_type": "execute_result"}], "source": ["(df[\"llm_score\"]>=7).sum()/len(df)"]}, {"cell_type": "code", "execution_count": 99, "id": "c1ecdd0c", "metadata": {}, "outputs": [{"data": {"text/plain": ["<Axes: xlabel='top_level_keys_present', ylabel='Count'>"]}, "execution_count": 99, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["sns.histplot(df, x=\"top_level_keys_present\")"]}, {"cell_type": "code", "execution_count": 100, "id": "a18db11e", "metadata": {}, "outputs": [{"data": {"text/plain": ["<Axes: xlabel='percent_node_with_valid_type', ylabel='Count'>"]}, "execution_count": 100, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["sns.histplot(df, x=\"percent_node_with_valid_type\")"]}, {"cell_type": "code", "execution_count": 101, "id": "3c7c398e", "metadata": {}, "outputs": [{"data": {"text/plain": ["<Axes: xlabel='percent_node_with_valid_version', ylabel='Count'>"]}, "execution_count": 101, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["sns.histplot(df, x=\"percent_node_with_valid_version\")"]}, {"cell_type": "code", "execution_count": 102, "id": "4c46bdcf", "metadata": {}, "outputs": [{"data": {"text/plain": ["<Axes: xlabel='percent_node_with_valid_structure', ylabel='Count'>"]}, "execution_count": 102, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["sns.histplot(df, x=\"percent_node_with_valid_structure\")"]}, {"cell_type": "code", "execution_count": 103, "id": "44883d67", "metadata": {}, "outputs": [{"data": {"text/plain": ["<Axes: xlabel='percent_node_with_parameters', ylabel='Count'>"]}, "execution_count": 103, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["sns.histplot(df, x=\"percent_node_with_parameters\")"]}, {"cell_type": "code", "execution_count": 104, "id": "048db9e6", "metadata": {}, "outputs": [{"data": {"text/plain": ["<Axes: xlabel='percent_connections_with_valid_structure', ylabel='Count'>"]}, "execution_count": 104, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["sns.histplot(df, x=\"percent_connections_with_valid_structure\")"]}, {"cell_type": "code", "execution_count": 105, "id": "24225a83", "metadata": {}, "outputs": [{"data": {"text/plain": ["<Axes: xlabel='percent_connections_with_valid_target_node', ylabel='Count'>"]}, "execution_count": 105, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["sns.histplot(df, x=\"percent_connections_with_valid_target_node\")"]}, {"cell_type": "code", "execution_count": 106, "id": "9bb20feb", "metadata": {}, "outputs": [], "source": ["df_prime = df"]}, {"cell_type": "markdown", "id": "a691c4b9", "metadata": {}, "source": ["## Easy workflows"]}, {"cell_type": "code", "execution_count": 107, "id": "db12a210", "metadata": {}, "outputs": [], "source": ["df = df_prime[df_prime[\"difficulty\"] == \"easy\"]"]}, {"cell_type": "code", "execution_count": 108, "id": "b99f16aa", "metadata": {}, "outputs": [{"data": {"text/plain": ["<Axes: xlabel='llm_score', ylabel='Count'>"]}, "execution_count": 108, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["sns.histplot(df, x=\"llm_score\", bins=20)"]}, {"cell_type": "code", "execution_count": 109, "id": "28199c28", "metadata": {}, "outputs": [{"data": {"text/plain": ["np.float64(0.7894736842105263)"]}, "execution_count": 109, "metadata": {}, "output_type": "execute_result"}], "source": ["(df[\"llm_score\"]>=7).sum()/len(df)"]}, {"cell_type": "code", "execution_count": 110, "id": "03e96ef0", "metadata": {}, "outputs": [{"data": {"text/plain": ["<Axes: xlabel='top_level_keys_present', ylabel='Count'>"]}, "execution_count": 110, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["sns.histplot(df, x=\"top_level_keys_present\")"]}, {"cell_type": "code", "execution_count": 111, "id": "0991d8aa", "metadata": {}, "outputs": [{"data": {"text/plain": ["<Axes: xlabel='percent_node_with_valid_type', ylabel='Count'>"]}, "execution_count": 111, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["sns.histplot(df, x=\"percent_node_with_valid_type\")"]}, {"cell_type": "code", "execution_count": 112, "id": "7c475b8e", "metadata": {}, "outputs": [{"data": {"text/plain": ["<Axes: xlabel='percent_node_with_valid_version', ylabel='Count'>"]}, "execution_count": 112, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["sns.histplot(df, x=\"percent_node_with_valid_version\")"]}, {"cell_type": "markdown", "id": "9291756b", "metadata": {}, "source": ["## Medium workflows"]}, {"cell_type": "code", "execution_count": 113, "id": "7c7d96c7", "metadata": {}, "outputs": [], "source": ["df = df_prime[df_prime[\"difficulty\"] == \"medium\"]"]}, {"cell_type": "code", "execution_count": 114, "id": "44472701", "metadata": {}, "outputs": [{"data": {"text/plain": ["<Axes: xlabel='llm_score', ylabel='Count'>"]}, "execution_count": 114, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["sns.histplot(df, x=\"llm_score\", bins=20)"]}, {"cell_type": "code", "execution_count": 115, "id": "09842340", "metadata": {}, "outputs": [{"data": {"text/plain": ["np.float64(0.9047619047619048)"]}, "execution_count": 115, "metadata": {}, "output_type": "execute_result"}], "source": ["(df[\"llm_score\"]>=7).sum()/len(df)"]}, {"cell_type": "code", "execution_count": 116, "id": "a4688dfe", "metadata": {}, "outputs": [{"data": {"text/plain": ["<Axes: xlabel='top_level_keys_present', ylabel='Count'>"]}, "execution_count": 116, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["sns.histplot(df, x=\"top_level_keys_present\")"]}, {"cell_type": "code", "execution_count": 117, "id": "8066e818", "metadata": {}, "outputs": [{"data": {"text/plain": ["<Axes: xlabel='percent_node_with_valid_type', ylabel='Count'>"]}, "execution_count": 117, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAjMAAAGxCAYAAACXwjeMAAAAOnRFWHRTb2Z0d2FyZQBNYXRwbG90bGliIHZlcnNpb24zLjEwLjAsIGh0dHBzOi8vbWF0cGxvdGxpYi5vcmcvlHJYcgAAAAlwSFlzAAAPYQAAD2EBqD+naQAALUBJREFUeJzt3XlclXXe//H3EfCIsrgli6JomrtI2qKZ4lQ6pm1Oi+ZCWU4mKubcZmSOS3eSzZ05LWMPmxmzzOy+c8kaU9EJNG1R1FRC3FAYxZsoFVQChe/vj36eW8IFETzne+b1fDyux6Prey18PheneHct53IYY4wAAAAsVcPdBQAAAFwNwgwAALAaYQYAAFiNMAMAAKxGmAEAAFYjzAAAAKsRZgAAgNUIMwAAwGq+7i6gupWWlurIkSMKDAyUw+FwdzkAAKACjDEqKChQeHi4atS49LkXrw8zR44cUUREhLvLAAAAlZCdna0mTZpcch2vDzOBgYGSfjkYQUFBbq4GAABURH5+viIiIlx/xy/F68PMuUtLQUFBhBkAACxTkVtEuAEYAABYjTADAACsRpgBAABWI8wAAACrEWYAAIDVCDMAAMBqhBkAAGA1wgwAALAaYQYAAFiNMAMAAKzm1jCzfv163XPPPQoPD5fD4dDy5cvLLDfGaNq0aQoPD5e/v79iYmKUlpbmnmIBAIBHcmuYOXXqlKKiovTmm29ecPkrr7yi2bNn680339TmzZsVGhqqu+66SwUFBde4UgAA4Knc+qLJfv36qV+/fhdcZozRnDlzNHnyZA0cOFCStGDBAoWEhGjRokV66qmnrmWpAADAQ3nsPTOZmZk6evSo+vTp4xpzOp3q1auXNm3a5MbKAACAJ3HrmZlLOXr0qCQpJCSkzHhISIgOHTp00e2KiopUVFTkms/Pz6+eAgEAXisrK0t5eXnuLsMKDRs2VNOmTd1ag8eGmXMcDkeZeWNMubHzJSYmavr06dVdFgDAS2VlZalNm7YqLDzt7lKs4O9fW7t3p7s10HhsmAkNDZX0yxmasLAw13hubm65szXnS0hI0IQJE1zz+fn5ioiIqL5CAQBeJS8vT4WFp3XLiKkKCot0dzkeLT/noL75+3Tl5eURZi6kefPmCg0NVVJSkqKjoyVJxcXFSklJ0axZsy66ndPplNPpvFZlAgC8VFBYpOo3be3uMlABbg0zJ0+e1L59+1zzmZmZ2r59u+rXr6+mTZtq/Pjxmjlzplq1aqVWrVpp5syZql27th599FE3Vg0AADyJW8PMli1b1Lt3b9f8uctDsbGxevfdd/Xss8+qsLBQo0eP1rFjx3TLLbdozZo1CgwMdFfJAADAw7g1zMTExMgYc9HlDodD06ZN07Rp065dUQAAwCoe+z0zAAAAFUGYAQAAViPMAAAAqxFmAACA1QgzAADAaoQZAABgNcIMAACwGmEGAABYjTADAACsRpgBAABWI8wAAACrEWYAAIDVCDMAAMBqhBkAAGA1wgwAALAaYQYAAFiNMAMAAKxGmAEAAFYjzAAAAKsRZgAAgNUIMwAAwGqEGQAAYDXCDAAAsBphBgAAWI0wAwAArEaYAQAAViPMAAAAqxFmAACA1QgzAADAaoQZAABgNcIMAACwGmEGAABYjTADAACsRpgBAABWI8wAAACrEWYAAIDVCDMAAMBqhBkAAGA1wgwAALAaYQYAAFiNMAMAAKxGmAEAAFYjzAAAAKsRZgAAgNUIMwAAwGqEGQAAYDXCDAAAsBphBgAAWI0wAwAArEaYAQAAViPMAAAAqxFmAACA1QgzAADAaoQZAABgNcIMAACwGmEGAABYjTADAACsRpgBAABWI8wAAACrEWYAAIDVPDrMnD17Vi+88IKaN28uf39/tWjRQjNmzFBpaam7SwMAAB7C190FXMqsWbP09ttva8GCBWrfvr22bNmixx9/XMHBwYqPj3d3eQAAwAN4dJj56quvdN9996l///6SpMjISH344YfasmWLmysDAACewqMvM/Xo0UPr1q3Tnj17JEnfffedvvzyS919990X3aaoqEj5+fllJgAA4L08+szMpEmTdOLECbVp00Y+Pj4qKSnRSy+9pMGDB190m8TERE2fPv0aVgkAANzJo8/MfPTRR1q4cKEWLVqkrVu3asGCBfqv//ovLViw4KLbJCQk6MSJE64pOzv7GlYMAACuNY8+MzNx4kQ999xzGjRokCSpY8eOOnTokBITExUbG3vBbZxOp5xO57UsEwAAuJFHn5k5ffq0atQoW6KPjw+PZgMAABePPjNzzz336KWXXlLTpk3Vvn17bdu2TbNnz9aIESPcXRoAAPAQHh1m3njjDU2ZMkWjR49Wbm6uwsPD9dRTT+mPf/yju0sDAAAewqPDTGBgoObMmaM5c+a4uxQAAOChPPqeGQAAgMshzAAAAKsRZgAAgNUIMwAAwGqEGQAAYDXCDAAAsBphBgAAWI0wAwAArEaYAQAAViPMAAAAqxFmAACA1QgzAADAaoQZAABgNcIMAACwGmEGAABYjTADAACsRpgBAABWI8wAAACrEWYAAIDVCDMAAMBqhBkAAGA1wgwAALAaYQYAAFiNMAMAAKxGmAEAAFYjzAAAAKsRZgAAgNUIMwAAwGqEGQAAYDXCDAAAsBphBgAAWI0wAwAArEaYAQAAViPMAAAAqxFmAACA1QgzAADAaoQZAABgNcIMAACwGmEGAABYjTADAACsRpgBAABWI8wAAACrEWYAAIDVCDMAAMBqhBkAAGA1wgwAALAaYQYAAFiNMAMAAKxGmAEAAFYjzAAAAKsRZgAAgNUIMwAAwGqEGQAAYDXCDAAAsBphBgAAWI0wAwAArEaYAQAAViPMAAAAqxFmAACA1Tw+zBw+fFhDhw5VgwYNVLt2bXXu3FmpqanuLgsAAHgIX3cXcCnHjh3Tbbfdpt69e+vzzz9Xo0aNtH//ftWtW9fdpQEAAA/h0WFm1qxZioiI0Pz5811jkZGR7isIAAB4HI8OMytWrFDfvn310EMPKSUlRY0bN9bo0aM1cuTIi25TVFSkoqIi13x+fv61KBUAPF5WVpby8vLcXYbHS09Pd3cJuEIeHWYOHDiguXPnasKECXr++ef17bffaty4cXI6nRo+fPgFt0lMTNT06dOvcaUA4NmysrLUpk1bFRaedncp1jhTVOzuElBBHh1mSktL1bVrV82cOVOSFB0drbS0NM2dO/eiYSYhIUETJkxwzefn5ysiIuKa1AsAniovL0+Fhad1y4ipCgqLdHc5Hi1n51fatWKezp496+5SUEEeHWbCwsLUrl27MmNt27bVkiVLLrqN0+mU0+ms7tIAwEpBYZGq37S1u8vwaPk5B91dAq6QRz+afdtttykjI6PM2J49e9SsWTM3VQQAADyNR4eZZ555Rl9//bVmzpypffv2adGiRZo3b57i4uLcXRoAAPAQHh1mbrrpJi1btkwffvihOnTooBdffFFz5szRkCFD3F0aAADwEB59z4wkDRgwQAMGDHB3GQAAwEN59JkZAACAy6lUmGnRooV+/PHHcuPHjx9XixYtrrooAACAiqpUmDl48KBKSkrKjRcVFenw4cNXXRQAAEBFXdE9MytWrHD98+rVqxUcHOyaLykp0bp163h3EgAAuKauKMzcf//9kiSHw6HY2Ngyy/z8/BQZGalXX321yooDAAC4nCsKM6WlpZKk5s2ba/PmzWrYsGG1FAUAAFBRlXo0OzMzs6rrAAAAqJRKf8/MunXrtG7dOuXm5rrO2Jzz97///aoLAwAAqIhKhZnp06drxowZ6tq1q8LCwuRwOKq6LgAAgAqpVJh5++239e6772rYsGFVXQ8AAMAVqdT3zBQXF6t79+5VXQsAAMAVq1SYefLJJ7Vo0aKqrgUAAOCKVeoy088//6x58+Zp7dq16tSpk/z8/Mosnz17dpUUBwAAcDmVCjM7duxQ586dJUm7du0qs4ybgQEAwLVUqTDzxRdfVHUdAAAAlVKpe2YAAAA8RaXOzPTu3fuSl5P++c9/VrogAACAK1GpMHPufplzzpw5o+3bt2vXrl3lXkAJAABQnSoVZl577bULjk+bNk0nT568qoIAAACuRJXeMzN06FDeywQAAK6pKg0zX331lWrVqlWVuwQAALikSl1mGjhwYJl5Y4xycnK0ZcsWTZkypUoKAwAAqIhKhZng4OAy8zVq1FDr1q01Y8YM9enTp0oKAwAAqIhKhZn58+dXdR0AAACVUqkwc05qaqrS09PlcDjUrl07RUdHV1VdAAAAFVKpMJObm6tBgwYpOTlZdevWlTFGJ06cUO/evbV48WJdd911VV0nAADABVXqaaaxY8cqPz9faWlp+umnn3Ts2DHt2rVL+fn5GjduXFXXCAAAcFGVOjOzatUqrV27Vm3btnWNtWvXTm+99RY3AAMAgGuqUmdmSktL5efnV27cz89PpaWlV10UAABARVUqzPzmN79RfHy8jhw54ho7fPiwnnnmGd1xxx1VVhwAAMDlVCrMvPnmmyooKFBkZKSuv/56tWzZUs2bN1dBQYHeeOONqq4RAADgoip1z0xERIS2bt2qpKQk7d69W8YYtWvXTnfeeWdV1wcAAHBJV3Rm5p///KfatWun/Px8SdJdd92lsWPHaty4cbrpppvUvn17bdiwoVoKBQAAuJArCjNz5szRyJEjFRQUVG5ZcHCwnnrqKc2ePbvKigMAALicKwoz3333nX77299edHmfPn2Umpp61UUBAABU1BWFmf/93/+94CPZ5/j6+uqHH3646qIAAAAq6orCTOPGjbVz586LLt+xY4fCwsKuuigAAICKuqIwc/fdd+uPf/yjfv7553LLCgsLNXXqVA0YMKDKigMAALicK3o0+4UXXtDSpUt1ww03aMyYMWrdurUcDofS09P11ltvqaSkRJMnT66uWgEAAMq5ojATEhKiTZs26emnn1ZCQoKMMZIkh8Ohvn376i9/+YtCQkKqpVAAAIALueIvzWvWrJlWrlypY8eOad++fTLGqFWrVqpXr1511AcAAHBJlfoGYEmqV6+ebrrppqqsBQAA4IpV6t1MAAAAnoIwAwAArEaYAQAAViPMAAAAqxFmAACA1QgzAADAaoQZAABgNcIMAACwGmEGAABYjTADAACsRpgBAABWI8wAAACrEWYAAIDVCDMAAMBqhBkAAGA1wgwAALAaYQYAAFjNqjCTmJgoh8Oh8ePHu7sUAADgIawJM5s3b9a8efPUqVMnd5cCAAA8iBVh5uTJkxoyZIjeeecd1atXz93lAAAAD2JFmImLi1P//v115513ursUAADgYXzdXcDlLF68WFu3btXmzZsrtH5RUZGKiopc8/n5+dVVGgAA8AAefWYmOztb8fHxWrhwoWrVqlWhbRITExUcHOyaIiIiqrlKAADgTh4dZlJTU5Wbm6suXbrI19dXvr6+SklJ0euvvy5fX1+VlJSU2yYhIUEnTpxwTdnZ2W6oHAAAXCsefZnpjjvu0M6dO8uMPf7442rTpo0mTZokHx+fcts4nU45nc5rVSIAAHAzjw4zgYGB6tChQ5mxOnXqqEGDBuXGAQDAvyePvswEAABwOR59ZuZCkpOT3V0CAADwIJyZAQAAViPMAAAAqxFmAACA1QgzAADAaoQZAABgNcIMAACwGmEGAABYjTADAACsRpgBAABWI8wAAACrEWYAAIDVCDMAAMBqhBkAAGA1wgwAALAaYQYAAFiNMAMAAKxGmAEAAFYjzAAAAKsRZgAAgNUIMwAAwGqEGQAAYDXCDAAAsBphBgAAWI0wAwAArEaYAQAAViPMAAAAqxFmAACA1QgzAADAar7uLgAArkZWVpby8vLcXYbHS09Pd3cJQLUhzACwVlZWltq0aavCwtPuLsUaZ4qK3V0CUOUIMwCslZeXp8LC07plxFQFhUW6uxyPlrPzK+1aMU9nz551dylAlSPMALBeUFik6jdt7e4yPFp+zkF3lwBUG24ABgAAViPMAAAAqxFmAACA1QgzAADAaoQZAABgNcIMAACwGmEGAABYjTADAACsRpgBAABWI8wAAACrEWYAAIDVCDMAAMBqhBkAAGA1wgwAALAaYQYAAFiNMAMAAKxGmAEAAFYjzAAAAKsRZgAAgNUIMwAAwGqEGQAAYDXCDAAAsBphBgAAWI0wAwAArEaYAQAAViPMAAAAqxFmAACA1QgzAADAah4dZhITE3XTTTcpMDBQjRo10v3336+MjAx3lwUAADyIR4eZlJQUxcXF6euvv1ZSUpLOnj2rPn366NSpU+4uDQAAeAhfdxdwKatWrSozP3/+fDVq1Eipqanq2bOnm6oCAACexKPDzK+dOHFCklS/fv2LrlNUVKSioiLXfH5+frXXBQAA3MejLzOdzxijCRMmqEePHurQocNF10tMTFRwcLBrioiIuIZVAgCAa82aMDNmzBjt2LFDH3744SXXS0hI0IkTJ1xTdnb2NaoQAAC4gxWXmcaOHasVK1Zo/fr1atKkySXXdTqdcjqd16gyAADgbh4dZowxGjt2rJYtW6bk5GQ1b97c3SUBAAAP49FhJi4uTosWLdInn3yiwMBAHT16VJIUHBwsf39/N1cHAAA8gUffMzN37lydOHFCMTExCgsLc00fffSRu0sDAAAewqPPzBhj3F0CAADwcB59ZgYAAOByCDMAAMBqhBkAAGA1wgwAALAaYQYAAFiNMAMAAKxGmAEAAFYjzAAAAKsRZgAAgNUIMwAAwGqEGQAAYDXCDAAAsBphBgAAWI0wAwAArEaYAQAAViPMAAAAqxFmAACA1QgzAADAaoQZAABgNcIMAACwGmEGAABYjTADAACsRpgBAABWI8wAAACrEWYAAIDVCDMAAMBqhBkAAGA1X3cXYLusrCzl5eW5uwwrNGzYUE2bNnV3GVbgc1Ux6enp7i4BgAcgzFyFrKwstWnTVoWFp91dihX8/Wtr9+50As1l8Lm6cmeKit1dAgA3Isxchby8PBUWntYtI6YqKCzS3eV4tPycg/rm79OVl5dHmLkMPlcVl7PzK+1aMU9nz551dykA3IgwUwWCwiJVv2lrd5cBL8Pn6vLycw66uwQAHoAbgAEAgNUIMwAAwGqEGQAAYDXCDAAAsBphBgAAWI0wAwAArEaYAQAAViPMAAAAqxFmAACA1QgzAADAaoQZAABgNcIMAACwGmEGAABYjTADAACsRpgBAABWI8wAAACrEWYAAIDVCDMAAMBqhBkAAGA1wgwAALAaYQYAAFiNMAMAAKxGmAEAAFYjzAAAAKsRZgAAgNUIMwAAwGqEGQAAYDXCDAAAsJoVYeYvf/mLmjdvrlq1aqlLly7asGGDu0sCAAAewuPDzEcffaTx48dr8uTJ2rZtm26//Xb169dPWVlZ7i4NAAB4AI8PM7Nnz9YTTzyhJ598Um3bttWcOXMUERGhuXPnurs0AADgATw6zBQXFys1NVV9+vQpM96nTx9t2rTJTVUBAABP4uvuAi4lLy9PJSUlCgkJKTMeEhKio0ePXnCboqIiFRUVueZPnDghScrPz6/y+k6ePClJ+ulQhs4WFVb5/r1J/tFfLgumpqa6jhsuLCMjQxKfq4rIzzkkSTpxeK/8fB1ursazcawqjmNVcef+237y5Mkq/zt7bn/GmMuvbDzY4cOHjSSzadOmMuP/+Z//aVq3bn3BbaZOnWokMTExMTExMXnBlJ2dfdm84NFnZho2bCgfH59yZ2Fyc3PLna05JyEhQRMmTHDNl5aW6qefflKDBg3kcHhHws7Pz1dERISys7MVFBTk7nKqHf16N/r1bvTr3aqzX2OMCgoKFB4eftl1PTrM1KxZU126dFFSUpIeeOAB13hSUpLuu+++C27jdDrldDrLjNWtW7c6y3SboKCgf4t/Wc6hX+9Gv96Nfr1bdfUbHBxcofU8OsxI0oQJEzRs2DB17dpV3bp107x585SVlaVRo0a5uzQAAOABPD7MPPLII/rxxx81Y8YM5eTkqEOHDlq5cqWaNWvm7tIAAIAH8PgwI0mjR4/W6NGj3V2Gx3A6nZo6dWq5y2nein69G/16N/r1bp7Sr8OYijzzBAAA4Jk8+kvzAAAALocwAwAArEaYAQAAViPMeKi5c+eqU6dOrmf3u3Xrps8//9y13BijadOmKTw8XP7+/oqJiVFaWpobK65aiYmJcjgcGj9+vGvMm3qeNm2aHA5HmSk0NNS13Jt6Pefw4cMaOnSoGjRooNq1a6tz585KTU11LfemniMjI8v9fh0Oh+Li4iR5V6+SdPbsWb3wwgtq3ry5/P391aJFC82YMUOlpaWudbyt54KCAo0fP17NmjWTv7+/unfvrs2bN7uW297v+vXrdc899yg8PFwOh0PLly8vs7wi/RUVFWns2LFq2LCh6tSpo3vvvVf/+te/qqfgyr9sANVpxYoV5h//+IfJyMgwGRkZ5vnnnzd+fn5m165dxhhjXn75ZRMYGGiWLFlidu7caR555BETFhZm8vPz3Vz51fv2229NZGSk6dSpk4mPj3eNe1PPU6dONe3btzc5OTmuKTc317Xcm3o1xpiffvrJNGvWzDz22GPmm2++MZmZmWbt2rVm3759rnW8qefc3Nwyv9ukpCQjyXzxxRfGGO/q1ZhfXjHToEED89lnn5nMzEzzP//zPyYgIMDMmTPHtY639fzwww+bdu3amZSUFLN3714zdepUExQUZP71r38ZY+zvd+XKlWby5MlmyZIlRpJZtmxZmeUV6W/UqFGmcePGJikpyWzdutX07t3bREVFmbNnz1Z5vYQZi9SrV8/89a9/NaWlpSY0NNS8/PLLrmU///yzCQ4ONm+//bYbK7x6BQUFplWrViYpKcn06tXLFWa8reepU6eaqKioCy7ztl6NMWbSpEmmR48eF13ujT2fLz4+3lx//fWmtLTUK3vt37+/GTFiRJmxgQMHmqFDhxpjvO/3e/r0aePj42M+++yzMuNRUVFm8uTJXtfvr8NMRfo7fvy48fPzM4sXL3atc/jwYVOjRg2zatWqKq+Ry0wWKCkp0eLFi3Xq1Cl169ZNmZmZOnr0qPr06eNax+l0qlevXtq0aZMbK716cXFx6t+/v+68884y497Y8969exUeHq7mzZtr0KBBOnDggCTv7HXFihXq2rWrHnroITVq1EjR0dF65513XMu9sedziouLtXDhQo0YMUIOh8Mre+3Ro4fWrVunPXv2SJK+++47ffnll7r77rsled/v9+zZsyopKVGtWrXKjPv7++vLL7/0un5/rSL9paam6syZM2XWCQ8PV4cOHarlGBBmPNjOnTsVEBAgp9OpUaNGadmyZWrXrp3rxZu/ftlmSEhIuZdy2mTx4sXaunWrEhMTyy3ztp5vueUWvffee1q9erXeeecdHT16VN27d9ePP/7odb1K0oEDBzR37ly1atVKq1ev1qhRozRu3Di99957krzv93u+5cuX6/jx43rsscckeWevkyZN0uDBg9WmTRv5+fkpOjpa48eP1+DBgyV5X8+BgYHq1q2bXnzxRR05ckQlJSVauHChvvnmG+Xk5Hhdv79Wkf6OHj2qmjVrql69ehddpypZ8Q3A/65at26t7du36/jx41qyZIliY2OVkpLiWv7rt4AbY6x9M3h2drbi4+O1Zs2acv+3cz5v6blfv36uf+7YsaO6deum66+/XgsWLNCtt94qyXt6lX55e33Xrl01c+ZMSVJ0dLTS0tI0d+5cDR8+3LWeN/V8zt/+9jf169ev3Jt/vanXjz76SAsXLtSiRYvUvn17bd++XePHj1d4eLhiY2Nd63lTz++//75GjBihxo0by8fHRzfeeKMeffRRbd261bWON/V7IZXpr7qOAWdmPFjNmjXVsmVLde3aVYmJiYqKitKf//xn11Mvv063ubm55ZKyLVJTU5Wbm6suXbrI19dXvr6+SklJ0euvvy5fX19XX97U8/nq1Kmjjh07au/evV75+w0LC1O7du3KjLVt21ZZWVmS5JU9S9KhQ4e0du1aPfnkk64xb+x14sSJeu655zRo0CB17NhRw4YN0zPPPOM6y+qNPV9//fVKSUnRyZMnlZ2drW+//VZnzpxR8+bNvbLf81Wkv9DQUBUXF+vYsWMXXacqEWYsYoxRUVGR61+WpKQk17Li4mKlpKSoe/fubqyw8u644w7t3LlT27dvd01du3bVkCFDtH37drVo0cLrej5fUVGR0tPTFRYW5pW/39tuu00ZGRllxvbs2eN6Yaw39ixJ8+fPV6NGjdS/f3/XmDf2evr0adWoUfbPiY+Pj+vRbG/s+Zw6deooLCxMx44d0+rVq3Xfffd5db9SxX6fXbp0kZ+fX5l1cnJytGvXruo5BlV+SzGqREJCglm/fr3JzMw0O3bsMM8//7ypUaOGWbNmjTHml8figoODzdKlS83OnTvN4MGDrXrsryLOf5rJGO/q+Q9/+INJTk42Bw4cMF9//bUZMGCACQwMNAcPHjTGeFevxvzyuL2vr6956aWXzN69e80HH3xgateubRYuXOhax9t6LikpMU2bNjWTJk0qt8zbeo2NjTWNGzd2PZq9dOlS07BhQ/Pss8+61vG2nletWmU+//xzc+DAAbNmzRoTFRVlbr75ZlNcXGyMsb/fgoICs23bNrNt2zYjycyePdts27bNHDp0yBhTsf5GjRplmjRpYtauXWu2bt1qfvOb3/Bo9r+bESNGmGbNmpmaNWua6667ztxxxx2uIGPML4/GTZ061YSGhhqn02l69uxpdu7c6caKq96vw4w39XzuOxn8/PxMeHi4GThwoElLS3Mt96Zez/n0009Nhw4djNPpNG3atDHz5s0rs9zbel69erWRZDIyMsot87Ze8/PzTXx8vGnatKmpVauWadGihZk8ebIpKipyreNtPX/00UemRYsWpmbNmiY0NNTExcWZ48ePu5bb3u8XX3xhJJWbYmNjjTEV66+wsNCMGTPG1K9f3/j7+5sBAwaYrKysaqmXt2YDAACrcc8MAACwGmEGAABYjTADAACsRpgBAABWI8wAAACrEWYAAIDVCDMAAMBqhBkAAGA1wgyAapecnCyHw6Hjx49f8589bdo0de7c+ZLrHDx4UA6HQ9u3b78mNV3Ir+t87LHHdP/9919ym5iYGI0fP75a6wJsQJgBvJjD4dDy5cvdXYZb/cd//IfWrVvnmq9ISPAEf/7zn/Xuu+9W2f4iIyM1Z86cKtsf4El83V0A8O+mpKREDoej3FuGUT0CAgIUEBDg7jKuWHBwsLtLAKzBf02By4iJidGYMWM0ZswY1a1bVw0aNNALL7ygc681Ky4u1rPPPqvGjRurTp06uuWWW5ScnOza/t1331XdunX12WefqV27dnI6nTp06JCKior07LPPKiIiQk6nU61atdLf/vY313bff/+97r77bgUEBCgkJETDhg1TXl5embrGjRunZ599VvXr11doaKimTZvmWh4ZGSlJeuCBB+RwOFzzl3LuUsf777+vyMhIBQcHa9CgQSooKHCtU1RUpHHjxqlRo0aqVauWevTooc2bN5fZz8qVK3XDDTfI399fvXv31sGDB8v9rE2bNqlnz57y9/dXRESExo0bp1OnTl22xjfeeEMdO3Z0zS9fvlwOh0NvvfWWa6xv375KSEgo09O5f16wYIE++eQTORwOORyOMr+rAwcOqHfv3qpdu7aioqL01VdfXbaeEydOyN/fX6tWrSozvnTpUtWpU0cnT56UJE2aNEk33HCDateurRYtWmjKlCk6c+bMRff76zNIp06d0vDhwxUQEKCwsDC9+uqrl63tnJiYGB06dEjPPPOMq+9Tp04pKChIH3/8cZl1P/30U9WpU0cFBQWuy2+LFy9W9+7dVatWLbVv377MMZMu/1kFql21vL4S8CK9evUyAQEBJj4+3uzevdssXLjQ1K5d2/XW50cffdR0797drF+/3uzbt8/86U9/Mk6n0+zZs8cYY8z8+fONn5+f6d69u9m4caPZvXu3OXnypHn44YdNRESEWbp0qdm/f79Zu3atWbx4sTHGmCNHjpiGDRuahIQEk56ebrZu3Wruuusu07t37zJ1BQUFmWnTppk9e/aYBQsWGIfD4Xq7em5urpFk5s+fb3Jyckxubu5le506daoJCAgwAwcONDt37jTr1683oaGh5vnnn3etM27cOBMeHm5Wrlxp0tLSTGxsrKlXr5758ccfjTHGZGVlGafTWeZ4hYSEGEnm2LFjxhhjduzYYQICAsxrr71m9uzZYzZu3Giio6PNY489dtkad+zYYRwOh/nhhx+MMcaMHz/eNGzY0Dz00EPGGGPOnDljAgICzOeff+7qKSoqyhhjTEFBgXn44YfNb3/7W5OTk2NycnJMUVGRyczMNJJMmzZtzGeffWYyMjLMgw8+aJo1a2bOnDlz2Zp+97vfmaFDh5YbGzx4sGv+xRdfNBs3bjSZmZlmxYoVJiQkxMyaNavMsT9XpzHGxMbGmvvuu881//TTT5smTZqYNWvWmB07dpgBAwa4PpeX8+OPP5omTZqYGTNmuPo2xpiRI0eau+++u8y6DzzwgBk+fLgxxriOS5MmTczHH39svv/+e/Pkk0+awMBAk5eXZ4yp2GcVqG6EGeAyevXqZdq2bWtKS0tdY5MmTTJt27Y1+/btMw6Hwxw+fLjMNnfccYdJSEgwxvwSZiSZ7du3u5ZnZGQYSSYpKemCP3PKlCmmT58+Zcays7ONJJORkeGqq0ePHmXWuemmm8ykSZNc85LMsmXLKtzr1KlTTe3atU1+fr5rbOLEieaWW24xxhhz8uRJ4+fnZz744APX8uLiYhMeHm5eeeUVY4wxCQkJFzxe54eZYcOGmd///vdlfvaGDRtMjRo1TGFh4SVrLC0tNQ0bNjQff/yxMcaYzp07m8TERNOoUSNjjDGbNm0yvr6+pqCgwNXTpUKCMf/3R/uvf/2raywtLc1IMunp6Zesxxhjli5dagICAsypU6eMMcacOHHC1KpVy/zjH/+46DavvPKK6dKli2v+UnUWFBSYmjVrusKuMb8EFH9//wqFGWOMadasmXnttdfKjH3zzTfGx8fH9fn94YcfjJ+fn0lOTjbG/N9xefnll13bnDlzxjRp0sQVxCryWQWqG5eZgAq49dZb5XA4XPPdunXT3r17tWXLFhljdMMNN7juzQgICFBKSor279/vWr9mzZrq1KmTa3779u3y8fFRr169LvjzUlNT9cUXX5TZZ5s2bSSpzH7P36ckhYWFKTc396p6jYyMVGBg4AX3uX//fp05c0a33Xaba7mfn59uvvlmpaenS5LS09MveLx+3d+7775bpr++ffuqtLRUmZmZl6zP4XCoZ8+eSk5O1vHjx5WWlqZRo0appKRE6enpSk5O1o033lip+2TOP55hYWGSVKHj2b9/f/n6+mrFihWSpCVLligwMFB9+vRxrfPxxx+rR48eCg0NVUBAgKZMmaKsrKwK1bV//34VFxeXOY7169dX69atK7T9xdx8881q37693nvvPUnS+++/r6ZNm6pnz55l1jv/5/r6+qpr166u33dFP6tAdeIGYOAq+fj4KDU1VT4+PmXGz/9j6u/vX+aPu7+//yX3WVpaqnvuuUezZs0qt+zcH1nplyBxPofDodLS0iuq/9cutU/z/+8TOr+Xc+Pnxs6tcymlpaV66qmnNG7cuHLLmjZtetntY2JiNG/ePG3YsEFRUVGqW7euevbsqZSUFCUnJysmJuay+7iQ83s/109FjmfNmjX14IMPatGiRRo0aJAWLVqkRx55RL6+v/wn9uuvv9agQYM0ffp09e3bV8HBwVq8eHGF73upyDGtrCeffFJvvvmmnnvuOc2fP1+PP/54ud/vhZx/fCryWQWqE2dmgAr4+uuvy823atVK0dHRKikpUW5urlq2bFlmCg0Nvej+OnbsqNLSUqWkpFxw+Y033qi0tDRFRkaW22+dOnUqXLefn59KSkoqvP7ltGzZUjVr1tSXX37pGjtz5oy2bNmitm3bSpLatWt3weN1vnP9/bq3c/u/nJiYGKWlpenjjz92BZdevXpp7dq12rRp00XPeEm/BI+qPCbnDBkyRKtWrVJaWpq++OILDRkyxLVs48aNatasmSZPnqyuXbuqVatWOnToUIX33bJlS/n5+ZU5jseOHdOePXsqvI+L9T106FBlZWXp9ddfV1pammJjY8utc/7PPXv2rFJTU11nX6rqswpcDcIMUAHZ2dmaMGGCMjIy9OGHH+qNN95QfHy8brjhBg0ZMkTDhw/X0qVLlZmZqc2bN2vWrFlauXLlRfcXGRmp2NhYjRgxQsuXL1dmZqaSk5P13//935KkuLg4/fTTTxo8eLC+/fZbHThwQGvWrNGIESOu6A9xZGSk1q1bp6NHj+rYsWNXfRzq1Kmjp59+WhMnTtSqVav0/fffa+TIkTp9+rSeeOIJSdKoUaO0f/9+1/FatGhRue9LmTRpkr766ivFxcVp+/bt2rt3r1asWKGxY8dWqI4OHTqoQYMG+uCDD1xhJiYmRsuXL1dhYaF69Ohx0W0jIyO1Y8cOZWRkKC8v75JPFF2JXr16KSQkREOGDFFkZKRuvfVW17KWLVsqKytLixcv1v79+/X6669r2bJlFd53QECAnnjiCU2cOFHr1q3Trl279Nhjj13R4/2RkZFav369Dh8+XOZJo3r16mngwIGaOHGi+vTpoyZNmpTb9q233tKyZcu0e/duxcXF6dixYxoxYoSkqvusAleDMANUwPDhw1VYWKibb75ZcXFxGjt2rH7/+99LkubPn6/hw4frD3/4g1q3bq17771X33zzjSIiIi65z7lz5+rBBx/U6NGj1aZNG40cOdL1aHJ4eLg2btyokpIS9e3bVx06dFB8fLyCg4Ov6A/Yq6++qqSkJEVERCg6OrryB+A8L7/8sn73u99p2LBhuvHGG7Vv3z6tXr1a9erVk/TLZaIlS5bo008/VVRUlN5++23NnDmzzD46deqklJQU7d27V7fffruio6M1ZcqUCl+WcDgcrrMvt99+u2ufwcHBio6OVlBQ0EW3HTlypFq3bq2uXbvquuuu08aNGytzGC5Y0+DBg/Xdd9+VOSsjSffdd5+eeeYZjRkzRp07d9amTZs0ZcqUK9r/n/70J/Xs2VP33nuv7rzzTvXo0UNdunSp8PYzZszQwYMHdf311+u6664rs+yJJ55QcXGxK6D82ssvv6xZs2YpKipKGzZs0CeffKKGDRtKqrrPKnA1HKY6L8YCXiAmJkadO3fm21PhtT744APFx8fryJEjZS7zHTx4UM2bN9e2bdsu+0oIwJ24ARgA/k2dPn1amZmZSkxM1FNPPVWh+5UAT8Q5QODfSPv27cs8Qnv+9MEHH7i7PEnShg0bLlqju15L0K9fv4vW8+tLaO5Q2WP2yiuvqHPnzgoJCXF9YzJgIy4zAf9GDh06dNEbXkNCQsp8v4y7FBYW6vDhwxdd3rJly2tYzS8OHz6swsLCCy6rX7++6tevf40rKssTjxlwLRFmAACA1bjMBAAArEaYAQAAViPMAAAAqxFmAACA1QgzAADAaoQZAABgNcIMAACwGmEGAABY7f8B2NqyBXAeUpYAAAAASUVORK5CYII=", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["sns.histplot(df, x=\"percent_node_with_valid_type\")"]}, {"cell_type": "code", "execution_count": 118, "id": "90a9d9a3", "metadata": {}, "outputs": [{"data": {"text/plain": ["<Axes: xlabel='percent_node_with_valid_version', ylabel='Count'>"]}, "execution_count": 118, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["sns.histplot(df, x=\"percent_node_with_valid_version\")"]}, {"cell_type": "markdown", "id": "4d763954", "metadata": {}, "source": ["## Hard workflows"]}, {"cell_type": "code", "execution_count": 119, "id": "0c4a08d8", "metadata": {}, "outputs": [], "source": ["df = df_prime[df_prime[\"difficulty\"] == \"hard\"]"]}, {"cell_type": "code", "execution_count": 120, "id": "521a045f", "metadata": {}, "outputs": [{"data": {"text/plain": ["<Axes: xlabel='llm_score', ylabel='Count'>"]}, "execution_count": 120, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["sns.histplot(df, x=\"llm_score\", bins=20)"]}, {"cell_type": "code", "execution_count": 121, "id": "19ab5cdf", "metadata": {}, "outputs": [{"data": {"text/plain": ["np.float64(0.55)"]}, "execution_count": 121, "metadata": {}, "output_type": "execute_result"}], "source": ["(df[\"llm_score\"]>=7).sum()/len(df)"]}, {"cell_type": "code", "execution_count": 122, "id": "516e335b", "metadata": {}, "outputs": [{"data": {"text/plain": ["<Axes: xlabel='top_level_keys_present', ylabel='Count'>"]}, "execution_count": 122, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["sns.histplot(df, x=\"top_level_keys_present\")"]}, {"cell_type": "code", "execution_count": 123, "id": "4cd2f1aa", "metadata": {}, "outputs": [{"data": {"text/plain": ["<Axes: xlabel='percent_node_with_valid_type', ylabel='Count'>"]}, "execution_count": 123, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["sns.histplot(df, x=\"percent_node_with_valid_type\")"]}, {"cell_type": "code", "execution_count": 124, "id": "d7940155", "metadata": {}, "outputs": [{"data": {"text/plain": ["<Axes: xlabel='percent_node_with_valid_version', ylabel='Count'>"]}, "execution_count": 124, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["sns.histplot(df, x=\"percent_node_with_valid_version\")"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.9"}}, "nbformat": 4, "nbformat_minor": 5}