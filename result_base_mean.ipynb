{"cells": [{"cell_type": "code", "execution_count": 1, "id": "cf9d20bd", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import seaborn as sns"]}, {"cell_type": "code", "execution_count": 2, "id": "2a29a094", "metadata": {}, "outputs": [], "source": ["df = pd.read_json(\"result_openai_orr_20B-mean.json\")"]}, {"cell_type": "code", "execution_count": 3, "id": "55ed4ffb", "metadata": {}, "outputs": [{"data": {"application/vnd.microsoft.datawrangler.viewer.v0+json": {"columns": [{"name": "index", "rawType": "int64", "type": "integer"}, {"name": "prompt", "rawType": "object", "type": "string"}, {"name": "index", "rawType": "int64", "type": "integer"}, {"name": "llm_score", "rawType": "float64", "type": "float"}, {"name": "json_parsed", "rawType": "int64", "type": "integer"}, {"name": "top_level_keys_present", "rawType": "float64", "type": "float"}, {"name": "workflow_name_valid", "rawType": "int64", "type": "integer"}, {"name": "num_nodes", "rawType": "float64", "type": "float"}, {"name": "num_nodes_with_valid_type", "rawType": "float64", "type": "float"}, {"name": "num_nodes_with_valid_version", "rawType": "float64", "type": "float"}, {"name": "num_nodes_with_valid_structure", "rawType": "float64", "type": "float"}, {"name": "num_nodes_with_parameters", "rawType": "float64", "type": "float"}, {"name": "num_connections", "rawType": "float64", "type": "float"}, {"name": "num_connections_with_valid_structure", "rawType": "float64", "type": "float"}, {"name": "num_connections_with_valid_target_node", "rawType": "float64", "type": "float"}, {"name": "active_field_boolean", "rawType": "float64", "type": "float"}, {"name": "percent_node_with_valid_type", "rawType": "float64", "type": "float"}, {"name": "percent_node_with_valid_version", "rawType": "float64", "type": "float"}, {"name": "percent_node_with_valid_structure", "rawType": "float64", "type": "float"}, {"name": "percent_connections_with_valid_structure", "rawType": "float64", "type": "float"}, {"name": "percent_connections_with_valid_target_node", "rawType": "float64", "type": "float"}, {"name": "percent_node_with_parameters", "rawType": "float64", "type": "float"}, {"name": "difficulty", "rawType": "object", "type": "string"}], "ref": "caf05165-d459-4a40-ac4b-f553e6e829a3", "rows": [["0", "Create a JSON object representing an n8n workflow for backing up other workflows, including nodes for scheduling, fetching workflows, looping, executing workflow backups, and sending Telegram and Gmail notifications. Ensure connections between nodes are defined to create a functional workflow.\n", "38", "7.75", "1", "4.0", "1", "6.75", "6.0", "6.0", "6.75", "6.75", "5.75", "5.75", "5.75", "1.0", "88.0952380952", "88.0952380952", "100.0", "100.0", "100.0", "100.0", "hard"], ["1", "Create a manually triggered workflow that first sets a `woocommerce url` variable. Then, make an authenticated HTTP POST request using WordPress API credentials to the `wp-json/rank-math-api/v1/update-meta` endpoint on that URL, updating Rank Math SEO meta for `post_id` 246 with a specific title, description, and canonical URL, configured to retry on failure.", "12", "10.0", "1", "4.0", "1", "3.0", "2.3333333333", "2.0", "3.0", "3.0", "2.0", "2.0", "2.0", "1.0", "77.7777777778", "66.6666666667", "100.0", "100.0", "100.0", "100.0", "medium"], ["2", "Create a workflow that manually triggers and sets a hardcoded phone number to \"+34605281220\". Then, use the `uproc` node with the `miquel-uproc` credential and `getPhoneParsed` tool to parse and validate this number, followed by an If node that checks if the phone is valid based on the parsing result.", "13", "10.0", "1", "4.0", "1", "4.0", "3.75", "3.5", "4.0", "4.0", "3.0", "3.0", "3.0", "1.0", "93.75", "87.5", "100.0", "100.0", "100.0", "100.0", "medium"], ["3", "Create a workflow that triggers on Google Sheets updates, checks for Telegram links, retrieves reactions, and updates the Google Sheet. The workflow uses HTTP requests, code, conditionals, and Google Sheets nodes to automate the process of extracting and storing Telegram message reaction data.\n", "26", "6.5", "1", "4.0", "1", "5.5", "5.25", "5.25", "5.5", "5.5", "4.5", "4.5", "4.5", "1.0", "95.8333333333", "95.8333333333", "100.0", "100.0", "100.0", "100.0", "hard"], ["4", "Create a workflow that, when manually triggered, reads the file `/files/tmp/tst1.mp4` from disk, then sends it as `multipart/form-data` to `https://api.elevenlabs.io/v1/speech-to-text` (POST method) using the 'Eleven Labs' credential, specifying `model_id` as `scribe_v1`.", "25", "9.5", "1", "4.0", "1", "3.0", "2.75", "2.75", "3.0", "3.0", "2.0", "2.0", "2.0", "1.0", "91.6666666667", "91.6666666667", "100.0", "100.0", "100.0", "100.0", "medium"]], "shape": {"columns": 22, "rows": 5}}, "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>prompt</th>\n", "      <th>index</th>\n", "      <th>llm_score</th>\n", "      <th>json_parsed</th>\n", "      <th>top_level_keys_present</th>\n", "      <th>workflow_name_valid</th>\n", "      <th>num_nodes</th>\n", "      <th>num_nodes_with_valid_type</th>\n", "      <th>num_nodes_with_valid_version</th>\n", "      <th>num_nodes_with_valid_structure</th>\n", "      <th>...</th>\n", "      <th>num_connections_with_valid_structure</th>\n", "      <th>num_connections_with_valid_target_node</th>\n", "      <th>active_field_boolean</th>\n", "      <th>percent_node_with_valid_type</th>\n", "      <th>percent_node_with_valid_version</th>\n", "      <th>percent_node_with_valid_structure</th>\n", "      <th>percent_connections_with_valid_structure</th>\n", "      <th>percent_connections_with_valid_target_node</th>\n", "      <th>percent_node_with_parameters</th>\n", "      <th>difficulty</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>Create a JSON object representing an n8n workf...</td>\n", "      <td>38</td>\n", "      <td>7.75</td>\n", "      <td>1</td>\n", "      <td>4.0</td>\n", "      <td>1</td>\n", "      <td>6.75</td>\n", "      <td>6.000000</td>\n", "      <td>6.00</td>\n", "      <td>6.75</td>\n", "      <td>...</td>\n", "      <td>5.75</td>\n", "      <td>5.75</td>\n", "      <td>1.0</td>\n", "      <td>88.095238</td>\n", "      <td>88.095238</td>\n", "      <td>100.0</td>\n", "      <td>100.0</td>\n", "      <td>100.0</td>\n", "      <td>100.0</td>\n", "      <td>hard</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>Create a manually triggered workflow that firs...</td>\n", "      <td>12</td>\n", "      <td>10.00</td>\n", "      <td>1</td>\n", "      <td>4.0</td>\n", "      <td>1</td>\n", "      <td>3.00</td>\n", "      <td>2.333333</td>\n", "      <td>2.00</td>\n", "      <td>3.00</td>\n", "      <td>...</td>\n", "      <td>2.00</td>\n", "      <td>2.00</td>\n", "      <td>1.0</td>\n", "      <td>77.777778</td>\n", "      <td>66.666667</td>\n", "      <td>100.0</td>\n", "      <td>100.0</td>\n", "      <td>100.0</td>\n", "      <td>100.0</td>\n", "      <td>medium</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>Create a workflow that manually triggers and s...</td>\n", "      <td>13</td>\n", "      <td>10.00</td>\n", "      <td>1</td>\n", "      <td>4.0</td>\n", "      <td>1</td>\n", "      <td>4.00</td>\n", "      <td>3.750000</td>\n", "      <td>3.50</td>\n", "      <td>4.00</td>\n", "      <td>...</td>\n", "      <td>3.00</td>\n", "      <td>3.00</td>\n", "      <td>1.0</td>\n", "      <td>93.750000</td>\n", "      <td>87.500000</td>\n", "      <td>100.0</td>\n", "      <td>100.0</td>\n", "      <td>100.0</td>\n", "      <td>100.0</td>\n", "      <td>medium</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>Create a workflow that triggers on Google Shee...</td>\n", "      <td>26</td>\n", "      <td>6.50</td>\n", "      <td>1</td>\n", "      <td>4.0</td>\n", "      <td>1</td>\n", "      <td>5.50</td>\n", "      <td>5.250000</td>\n", "      <td>5.25</td>\n", "      <td>5.50</td>\n", "      <td>...</td>\n", "      <td>4.50</td>\n", "      <td>4.50</td>\n", "      <td>1.0</td>\n", "      <td>95.833333</td>\n", "      <td>95.833333</td>\n", "      <td>100.0</td>\n", "      <td>100.0</td>\n", "      <td>100.0</td>\n", "      <td>100.0</td>\n", "      <td>hard</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>Create a workflow that, when manually triggere...</td>\n", "      <td>25</td>\n", "      <td>9.50</td>\n", "      <td>1</td>\n", "      <td>4.0</td>\n", "      <td>1</td>\n", "      <td>3.00</td>\n", "      <td>2.750000</td>\n", "      <td>2.75</td>\n", "      <td>3.00</td>\n", "      <td>...</td>\n", "      <td>2.00</td>\n", "      <td>2.00</td>\n", "      <td>1.0</td>\n", "      <td>91.666667</td>\n", "      <td>91.666667</td>\n", "      <td>100.0</td>\n", "      <td>100.0</td>\n", "      <td>100.0</td>\n", "      <td>100.0</td>\n", "      <td>medium</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>5 rows × 22 columns</p>\n", "</div>"], "text/plain": ["                                              prompt  index  llm_score  \\\n", "0  Create a JSON object representing an n8n workf...     38       7.75   \n", "1  Create a manually triggered workflow that firs...     12      10.00   \n", "2  Create a workflow that manually triggers and s...     13      10.00   \n", "3  Create a workflow that triggers on Google Shee...     26       6.50   \n", "4  Create a workflow that, when manually triggere...     25       9.50   \n", "\n", "   json_parsed  top_level_keys_present  workflow_name_valid  num_nodes  \\\n", "0            1                     4.0                    1       6.75   \n", "1            1                     4.0                    1       3.00   \n", "2            1                     4.0                    1       4.00   \n", "3            1                     4.0                    1       5.50   \n", "4            1                     4.0                    1       3.00   \n", "\n", "   num_nodes_with_valid_type  num_nodes_with_valid_version  \\\n", "0                   6.000000                          6.00   \n", "1                   2.333333                          2.00   \n", "2                   3.750000                          3.50   \n", "3                   5.250000                          5.25   \n", "4                   2.750000                          2.75   \n", "\n", "   num_nodes_with_valid_structure  ...  num_connections_with_valid_structure  \\\n", "0                            6.75  ...                                  5.75   \n", "1                            3.00  ...                                  2.00   \n", "2                            4.00  ...                                  3.00   \n", "3                            5.50  ...                                  4.50   \n", "4                            3.00  ...                                  2.00   \n", "\n", "   num_connections_with_valid_target_node  active_field_boolean  \\\n", "0                                    5.75                   1.0   \n", "1                                    2.00                   1.0   \n", "2                                    3.00                   1.0   \n", "3                                    4.50                   1.0   \n", "4                                    2.00                   1.0   \n", "\n", "   percent_node_with_valid_type  percent_node_with_valid_version  \\\n", "0                     88.095238                        88.095238   \n", "1                     77.777778                        66.666667   \n", "2                     93.750000                        87.500000   \n", "3                     95.833333                        95.833333   \n", "4                     91.666667                        91.666667   \n", "\n", "   percent_node_with_valid_structure  \\\n", "0                              100.0   \n", "1                              100.0   \n", "2                              100.0   \n", "3                              100.0   \n", "4                              100.0   \n", "\n", "   percent_connections_with_valid_structure  \\\n", "0                                     100.0   \n", "1                                     100.0   \n", "2                                     100.0   \n", "3                                     100.0   \n", "4                                     100.0   \n", "\n", "   percent_connections_with_valid_target_node  percent_node_with_parameters  \\\n", "0                                       100.0                         100.0   \n", "1                                       100.0                         100.0   \n", "2                                       100.0                         100.0   \n", "3                                       100.0                         100.0   \n", "4                                       100.0                         100.0   \n", "\n", "   difficulty  \n", "0        hard  \n", "1      medium  \n", "2      medium  \n", "3        hard  \n", "4      medium  \n", "\n", "[5 rows x 22 columns]"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["df.head()"]}, {"cell_type": "markdown", "id": "e459bb7b", "metadata": {}, "source": ["## Overall"]}, {"cell_type": "code", "execution_count": 4, "id": "aeaeeef2", "metadata": {}, "outputs": [{"data": {"text/plain": ["<Axes: xlabel='llm_score', ylabel='Count'>"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["sns.histplot(df, x=\"llm_score\", bins=20)"]}, {"cell_type": "code", "execution_count": 5, "id": "8825d978", "metadata": {}, "outputs": [{"data": {"text/plain": ["np.float64(0.7619047619047619)"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["(df[\"llm_score\"]>=7).sum()/len(df)"]}, {"cell_type": "code", "execution_count": 6, "id": "c1ecdd0c", "metadata": {}, "outputs": [{"data": {"text/plain": ["<Axes: xlabel='top_level_keys_present', ylabel='Count'>"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAjMAAAGxCAYAAACXwjeMAAAAOnRFWHRTb2Z0d2FyZQBNYXRwbG90bGliIHZlcnNpb24zLjEwLjAsIGh0dHBzOi8vbWF0cGxvdGxpYi5vcmcvlHJYcgAAAAlwSFlzAAAPYQAAD2EBqD+naQAAKlxJREFUeJzt3X1Y1HW+//HXKISYgGk6QKJg4Q2ampKKHVNT8GDHU2t7zDS1zHLF2zwdbyuxq8VyT2imudmW2rUi7abtupuppEkaWUjeheZaoZKBHLwBVMSE7++Pfs42gXIjzMyHfT6ua66r+X6/M983n1Se13dmwGZZliUAAABDNXD3AAAAADeCmAEAAEYjZgAAgNGIGQAAYDRiBgAAGI2YAQAARiNmAACA0YgZAABgNC93D1DXysrK9MMPP8jPz082m83d4wAAgCqwLEtFRUUKDg5WgwbXv/ZS72Pmhx9+UEhIiLvHAAAANZCdna1WrVpd95h6HzN+fn6SfloMf39/N08DAACqorCwUCEhIY7v49dT72Pm6ktL/v7+xAwAAIapyltEeAMwAAAwGjEDAACMRswAAACjETMAAMBoxAwAADAaMQMAAIxGzAAAAKMRMwAAwGjEDAAAMBoxAwAAjEbMAAAAoxEzAADAaMQMAAAwGjEDAACM5uXuAQAAQO05ceKE8vPzXXa+W2+9Va1bt3bZ+SpCzAAAUE+cOHFCHTp0VHHxRZed09e3sb7++rBbg4aYAQCgnsjPz1dx8UX1Gjdf/kGhdX6+wpxj+vztBcrPzydmAABA7fEPClWz1u3dPYbL8AZgAABgNGIGAAAYjZgBAABGI2YAAIDRiBkAAGA0YgYAABiNmAEAAEYjZgAAgNGIGQAAYDRiBgAAGI2YAQAARiNmAACA0YgZAABgNGIGAAAYjZgBAABGI2YAAIDRiBkAAGA0YgYAABiNmAEAAEYjZgAAgNGIGQAAYDRiBgAAGI2YAQAARiNmAACA0YgZAABgNGIGAAAYjZgBAABGI2YAAIDRiBkAAGA0YgYAABjNrTETHx8vm83mdAsMDHTstyxL8fHxCg4Olq+vr/r376/MzEw3TgwAADyN26/MdOrUSTk5OY7bwYMHHfsWLVqkxMRELVu2TOnp6QoMDFR0dLSKiorcODEAAPAkbo8ZLy8vBQYGOm4tWrSQ9NNVmSVLlmjevHkaNmyYOnfurDVr1ujixYtKSkpy89QAAMBTuD1mjh49quDgYIWFhWnEiBH67rvvJElZWVnKzc1VTEyM41gfHx/169dPaWlp13y+kpISFRYWOt0AAED95daY6dWrl9555x1t2bJFb775pnJzc9WnTx+dPn1aubm5kiS73e70GLvd7thXkYULFyogIMBxCwkJqdOvAQAAuJdbYyY2NlYPPfSQ7rzzTg0aNEgffPCBJGnNmjWOY2w2m9NjLMsqt+3n5syZo4KCAsctOzu7boYHAAAewe0vM/3czTffrDvvvFNHjx51fKrpl1dh8vLyyl2t+TkfHx/5+/s73QAAQP3lUTFTUlKiw4cPKygoSGFhYQoMDFRKSopj/+XLl5Wamqo+ffq4cUoAAOBJvNx58meeeUZDhw5V69atlZeXpxdffFGFhYUaO3asbDabpk+froSEBIWHhys8PFwJCQlq3LixRo4c6c6xAQCAB3FrzHz//fd65JFHlJ+frxYtWqh3797avXu32rRpI0maOXOmiouLFRcXp7Nnz6pXr17aunWr/Pz83Dk2AADwIG6NmeTk5Ovut9lsio+PV3x8vGsGAgAAxvGo98wAAABUFzEDAACMRswAAACjETMAAMBoxAwAADAaMQMAAIxGzAAAAKMRMwAAwGjEDAAAMBoxAwAAjEbMAAAAoxEzAADAaMQMAAAwGjEDAACMRswAAACjETMAAMBoxAwAADAaMQMAAIxGzAAAAKMRMwAAwGjEDAAAMBoxAwAAjEbMAAAAoxEzAADAaMQMAAAwGjEDAACMRswAAACjETMAAMBoxAwAADAaMQMAAIxGzAAAAKMRMwAAwGjEDAAAMBoxAwAAjEbMAAAAoxEzAADAaMQMAAAwGjEDAACMRswAAACjETMAAMBoxAwAADAaMQMAAIxGzAAAAKMRMwAAwGjEDAAAMBoxAwAAjEbMAAAAoxEzAADAaMQMAAAwGjEDAACMRswAAACjETMAAMBoHhMzCxculM1m0/Tp0x3bLMtSfHy8goOD5evrq/79+yszM9N9QwIAAI/jETGTnp6ulStXqkuXLk7bFy1apMTERC1btkzp6ekKDAxUdHS0ioqK3DQpAADwNG6PmfPnz2vUqFF68803dcsttzi2W5alJUuWaN68eRo2bJg6d+6sNWvW6OLFi0pKSnLjxAAAwJO4PWYmTZqk+++/X4MGDXLanpWVpdzcXMXExDi2+fj4qF+/fkpLS7vm85WUlKiwsNDpBgAA6i8vd548OTlZX375pdLT08vty83NlSTZ7Xan7Xa7XcePH7/mcy5cuFALFiyo3UEBAIDHctuVmezsbE2bNk1//OMf1ahRo2seZ7PZnO5bllVu28/NmTNHBQUFjlt2dnatzQwAADyP267MZGRkKC8vTz169HBsKy0t1SeffKJly5bpyJEjkn66QhMUFOQ4Ji8vr9zVmp/z8fGRj49P3Q0OAAA8ituuzAwcOFAHDx7Uvn37HLfIyEiNGjVK+/btU9u2bRUYGKiUlBTHYy5fvqzU1FT16dPHXWMDAAAP47YrM35+furcubPTtptvvlnNmzd3bJ8+fboSEhIUHh6u8PBwJSQkqHHjxho5cqQ7RgYAAB7IrW8ArszMmTNVXFysuLg4nT17Vr169dLWrVvl5+fn7tEAAICH8KiY2bFjh9N9m82m+Ph4xcfHu2UeAADg+dz+c2YAAABuBDEDAACMRswAAACjETMAAMBoxAwAADAaMQMAAIxGzAAAAKMRMwAAwGjEDAAAMBoxAwAAjEbMAAAAoxEzAADAaMQMAAAwGjEDAACMRswAAACjETMAAMBoxAwAADAaMQMAAIxGzAAAAKMRMwAAwGjEDAAAMBoxAwAAjEbMAAAAoxEzAADAaMQMAAAwGjEDAACMRswAAACjETMAAMBoxAwAADAaMQMAAIxGzAAAAKMRMwAAwGjEDAAAMBoxAwAAjEbMAAAAoxEzAADAaMQMAAAwGjEDAACMRswAAACjETMAAMBoxAwAADBajWKmbdu2On36dLnt586dU9u2bW94KAAAgKqqUcwcO3ZMpaWl5baXlJTo5MmTNzwUAABAVXlV5+CNGzc6/nvLli0KCAhw3C8tLdW2bdsUGhpaa8MBAABUplox8+CDD0qSbDabxo4d67TP29tboaGheuWVV2ptOAAAgMpUK2bKysokSWFhYUpPT9ett95aJ0MBAABUVbVi5qqsrKzangMAAKBGahQzkrRt2zZt27ZNeXl5jis2V7399ts3PBgAAEBV1ChmFixYoBdeeEGRkZEKCgqSzWar7bkAAACqpEYx8/vf/16rV6/W6NGja3seAACAaqnRz5m5fPmy+vTpU9uzAAAAVFuNYmb8+PFKSkqq7VkAAACqrUYvM126dEkrV67URx99pC5dusjb29tpf2JiYpWeZ8WKFVqxYoWOHTsmSerUqZOef/55xcbGSpIsy9KCBQu0cuVKnT17Vr169dLy5cvVqVOnmowNAADqoRrFzIEDB9StWzdJ0ldffeW0rzpvBm7VqpVeeukl3XHHHZKkNWvW6IEHHtDevXvVqVMnLVq0SImJiVq9erXatWunF198UdHR0Tpy5Ij8/PxqMjoAAKhnahQzH3/8ca2cfOjQoU73f/vb32rFihXavXu3IiIitGTJEs2bN0/Dhg2T9FPs2O12JSUlacKECbUyAwAAMFuN3jNTF0pLS5WcnKwLFy4oKipKWVlZys3NVUxMjOMYHx8f9evXT2lpadd8npKSEhUWFjrdAABA/VWjKzMDBgy47stJ27dvr/JzHTx4UFFRUbp06ZKaNGmi999/XxEREY5gsdvtTsfb7XYdP378ms+3cOFCLViwoMrnBwAAZqtRzFx9v8xVP/74o/bt26evvvqq3C+grEz79u21b98+nTt3TuvXr9fYsWOVmprq2P/LaLIs67ohNWfOHM2YMcNxv7CwUCEhIdWaCQAAmKNGMbN48eIKt8fHx+v8+fPVeq6bbrrJ8QbgyMhIpaen69VXX9WsWbMkSbm5uQoKCnIcn5eXV+5qzc/5+PjIx8enWjMAAABz1ep7Zh599NEb/r1MlmWppKREYWFhCgwMVEpKimPf5cuXlZqayg/sAwAADjX+RZMV+eyzz9SoUaMqHz937lzFxsYqJCRERUVFSk5O1o4dO7R582bZbDZNnz5dCQkJCg8PV3h4uBISEtS4cWONHDmyNscGAAAGq1HMXP2o9FWWZSknJ0d79uzRc889V+XnOXXqlEaPHq2cnBwFBASoS5cu2rx5s6KjoyVJM2fOVHFxseLi4hw/NG/r1q38jBkAAOBQo5gJCAhwut+gQQO1b99eL7zwgtNHqSvz1ltvXXe/zWZTfHy84uPjazImAAD4F1CjmFm1alVtzwEAAFAjN/SemYyMDB0+fFg2m00RERG66667amsuAACAKqlRzOTl5WnEiBHasWOHmjZtKsuyVFBQoAEDBig5OVktWrSo7TkBAAAqVKOPZk+ZMkWFhYXKzMzUmTNndPbsWX311VcqLCzU1KlTa3tGAACAa6rRlZnNmzfro48+UseOHR3bIiIitHz58mq9ARgAAOBG1ejKTFlZmby9vctt9/b2VllZ2Q0PBQAAUFU1ipn77rtP06ZN0w8//ODYdvLkST399NMaOHBgrQ0HAABQmRrFzLJly1RUVKTQ0FDdfvvtuuOOOxQWFqaioiK99tprtT0jAADANdXoPTMhISH68ssvlZKSoq+//lqWZSkiIkKDBg2q7fkAAACuq1pXZrZv366IiAgVFhZKkqKjozVlyhRNnTpVd999tzp16qSdO3fWyaAAAAAVqVbMLFmyRE8++aT8/f3L7QsICNCECROUmJhYa8MBAABUploxs3//fv37v//7NffHxMQoIyPjhocCAACoqmrFzKlTpyr8SPZVXl5e+r//+78bHgoAAKCqqhUzt912mw4ePHjN/QcOHFBQUNANDwUAAFBV1YqZIUOG6Pnnn9elS5fK7SsuLtb8+fP1H//xH7U2HAAAQGWq9dHsZ599Vhs2bFC7du00efJktW/fXjabTYcPH9by5ctVWlqqefPm1dWsAAAA5VQrZux2u9LS0jRx4kTNmTNHlmVJkmw2mwYPHqzXX39ddru9TgYFAACoSLV/aF6bNm20adMmnT17Vt98840sy1J4eLhuueWWupgPAADgumr0E4Al6ZZbbtHdd99dm7MAAABUW41+NxMAAICnIGYAAIDRiBkAAGA0YgYAABiNmAEAAEYjZgAAgNGIGQAAYDRiBgAAGI2YAQAARiNmAACA0YgZAABgNGIGAAAYjZgBAABGI2YAAIDRiBkAAGA0YgYAABiNmAEAAEYjZgAAgNGIGQAAYDRiBgAAGI2YAQAARiNmAACA0YgZAABgNGIGAAAYjZgBAABGI2YAAIDRiBkAAGA0YgYAABiNmAEAAEYjZgAAgNGIGQAAYDRiBgAAGM2tMbNw4ULdfffd8vPzU8uWLfXggw/qyJEjTsdYlqX4+HgFBwfL19dX/fv3V2ZmppsmBgAAnsatMZOamqpJkyZp9+7dSklJ0ZUrVxQTE6MLFy44jlm0aJESExO1bNkypaenKzAwUNHR0SoqKnLj5AAAwFN4ufPkmzdvdrq/atUqtWzZUhkZGbr33ntlWZaWLFmiefPmadiwYZKkNWvWyG63KykpSRMmTHDH2AAAwIN41HtmCgoKJEnNmjWTJGVlZSk3N1cxMTGOY3x8fNSvXz+lpaW5ZUYAAOBZ3Hpl5ucsy9KMGTP0b//2b+rcubMkKTc3V5Jkt9udjrXb7Tp+/HiFz1NSUqKSkhLH/cLCwjqaGAAAeAKPuTIzefJkHThwQOvWrSu3z2azOd23LKvctqsWLlyogIAAxy0kJKRO5gUAAJ7BI2JmypQp2rhxoz7++GO1atXKsT0wMFDSP6/QXJWXl1fuas1Vc+bMUUFBgeOWnZ1dd4MDAAC3c2vMWJalyZMna8OGDdq+fbvCwsKc9oeFhSkwMFApKSmObZcvX1Zqaqr69OlT4XP6+PjI39/f6QYAAOovt75nZtKkSUpKStJf//pX+fn5Oa7ABAQEyNfXVzabTdOnT1dCQoLCw8MVHh6uhIQENW7cWCNHjnTn6AAAwEO4NWZWrFghSerfv7/T9lWrVumxxx6TJM2cOVPFxcWKi4vT2bNn1atXL23dulV+fn4unhYAAHgit8aMZVmVHmOz2RQfH6/4+Pi6HwgAABjHI94ADAAAUFPEDAAAMBoxAwAAjEbMAAAAoxEzAADAaMQMAAAwGjEDAACMRswAAACjETMAAMBoxAwAADAaMQMAAIxGzAAAAKMRMwAAwGjEDAAAMBoxAwAAjEbMAAAAoxEzAADAaMQMAAAwGjEDAACMRswAAACjETMAAMBoxAwAADAaMQMAAIxGzAAAAKMRMwAAwGjEDAAAMBoxAwAAjEbMAAAAoxEzAADAaMQMAAAwGjEDAACMRswAAACjETMAAMBoxAwAADAaMQMAAIxGzAAAAKMRMwAAwGjEDAAAMBoxAwAAjEbMAAAAoxEzAADAaMQMAAAwGjEDAACMRswAAACjETMAAMBoxAwAADAaMQMAAIxGzAAAAKMRMwAAwGjEDAAAMBoxAwAAjEbMAAAAo7k1Zj755BMNHTpUwcHBstls+stf/uK037IsxcfHKzg4WL6+vurfv78yMzPdMywAAPBIbo2ZCxcuqGvXrlq2bFmF+xctWqTExEQtW7ZM6enpCgwMVHR0tIqKilw8KQAA8FRe7jx5bGysYmNjK9xnWZaWLFmiefPmadiwYZKkNWvWyG63KykpSRMmTHDlqAAAwEN57HtmsrKylJubq5iYGMc2Hx8f9evXT2lpaW6cDAAAeBK3Xpm5ntzcXEmS3W532m6323X8+PFrPq6kpEQlJSWO+4WFhXUzIAAA8Agee2XmKpvN5nTfsqxy235u4cKFCggIcNxCQkLqekQAAOBGHhszgYGBkv55heaqvLy8cldrfm7OnDkqKChw3LKzs+t0TgAA4F4eGzNhYWEKDAxUSkqKY9vly5eVmpqqPn36XPNxPj4+8vf3d7oBAID6y63vmTl//ry++eYbx/2srCzt27dPzZo1U+vWrTV9+nQlJCQoPDxc4eHhSkhIUOPGjTVy5Eg3Tg0AADyJW2Nmz549GjBggOP+jBkzJEljx47V6tWrNXPmTBUXFysuLk5nz55Vr169tHXrVvn5+blrZAAA4GHcGjP9+/eXZVnX3G+z2RQfH6/4+HjXDQUAAIzise+ZAQAAqApiBgAAGI2YAQAARiNmAACA0YgZAABgNGIGAAAYjZgBAABGI2YAAIDRiBkAAGA0YgYAABiNmAEAAEYjZgAAgNGIGQAAYDRiBgAAGI2YAQAARiNmAACA0YgZAABgNGIGAAAYjZgBAABGI2YAAIDRiBkAAGA0YgYAABiNmAEAAEYjZgAAgNGIGQAAYDRiBgAAGI2YAQAARiNmAACA0YgZAABgNGIGAAAYjZgBAABGI2YAAIDRiBkAAGA0YgYAABiNmAEAAEYjZgAAgNGIGQAAYDQvdw8AADfixIkTys/Pd9n5br31VrVu3dpl5wNQOWIGgLFOnDihDh06qrj4osvO6evbWF9/fZigATwIMQPAWPn5+Souvqhe4+bLPyi0zs9XmHNMn7+9QPn5+cQM4EGIGQDG8w8KVbPW7d09BgA34Q3AAADAaMQMAAAwGi8z3SA+SQEAgHsRMzeAT1IAAOB+xMwN4JMUAAC4HzFTC/gkBQAA7sMbgAEAgNGIGQAAYDReZgJcyNWffpP4BBw8H58KxY0iZgAXccen3yQ+AQfPxqdCURuIGcBFXP3pN4lPwMHz8alQ1AYjYub111/X7373O+Xk5KhTp05asmSJ+vbt6+6xgBrh029Aefy9wI3w+DcAv/vuu5o+fbrmzZunvXv3qm/fvoqNjdWJEyfcPRoAAPAAHh8ziYmJeuKJJzR+/Hh17NhRS5YsUUhIiFasWOHu0QAAgAfw6Ji5fPmyMjIyFBMT47Q9JiZGaWlpbpoKAAB4Eo9+z0x+fr5KS0tlt9udttvtduXm5lb4mJKSEpWUlDjuFxQUSJIKCwtrfb7z589Lks4cP6IrJcW1/vy/VJj700trGRkZjnPXtQYNGqisrMwl56rv5zty5Igk1/15ker/nxlXr2l9X093nJP/h7XLXet5/vz5Wv8+e/X5LMuq/GDLg508edKSZKWlpTltf/HFF6327dtX+Jj58+dbkrhx48aNGzdu9eCWnZ1daS949JWZW2+9VQ0bNix3FSYvL6/c1Zqr5syZoxkzZjjul5WV6cyZM2revLlsNlutzldYWKiQkBBlZ2fL39+/Vp8b/8Q6uwbr7Bqss2uwzq5Rl+tsWZaKiooUHBxc6bEeHTM33XSTevTooZSUFP3qV79ybE9JSdEDDzxQ4WN8fHzk4+PjtK1p06Z1Oab8/f35y+ICrLNrsM6uwTq7BuvsGnW1zgEBAVU6zqNjRpJmzJih0aNHKzIyUlFRUVq5cqVOnDih3/zmN+4eDQAAeACPj5mHH35Yp0+f1gsvvKCcnBx17txZmzZtUps2bdw9GgAA8AAeHzOSFBcXp7i4OHePUY6Pj4/mz59f7mUt1C7W2TVYZ9dgnV2DdXYNT1lnm2VV5TNPAAAAnsmjf2geAABAZYgZAABgNGIGAAAYjZi5hhUrVqhLly6Oz85HRUXpww8/vO5jUlNT1aNHDzVq1Eht27bV73//exdNa67qrvOGDRsUHR2tFi1aOI7fsmWLCyc2U03+PF/16aefysvLS926davbIeuBmqxzSUmJ5s2bpzZt2sjHx0e333673n77bRdNbKaarPPatWvVtWtXNW7cWEFBQXr88cd1+vRpF01cPyxcuFA2m03Tp0+/7nHu+F5IzFxDq1at9NJLL2nPnj3as2eP7rvvPj3wwAPKzMys8PisrCwNGTJEffv21d69ezV37lxNnTpV69evd/HkZqnuOn/yySeKjo7Wpk2blJGRoQEDBmjo0KHau3eviyc3S3XX+aqCggKNGTNGAwcOdNGkZqvJOg8fPlzbtm3TW2+9pSNHjmjdunXq0KGDC6c2T3XXedeuXRozZoyeeOIJZWZm6s9//rPS09M1fvx4F09urvT0dK1cuVJdunS57nFu+154479B6V/HLbfcYv3hD3+ocN/MmTOtDh06OG2bMGGC1bt3b1eMVq9cb50rEhERYS1YsKAOJ6qfqrLODz/8sPXss89a8+fPt7p27eqaweqZ663zhx9+aAUEBFinT5928VT1z/XW+Xe/+53Vtm1bp21Lly61WrVq5YrRjFdUVGSFh4dbKSkpVr9+/axp06Zd81h3fS/kykwVlJaWKjk5WRcuXFBUVFSFx3z22WeKiYlx2jZ48GDt2bNHP/74oyvGNF5V1vmXysrKVFRUpGbNmtXxdPVHVdd51apV+vbbbzV//nwXTld/VGWdN27cqMjISC1atEi33Xab2rVrp2eeeUbFxa75rer1QVXWuU+fPvr++++1adMmWZalU6dO6b333tP999/v4mnNNGnSJN1///0aNGhQpce663uhET80z10OHjyoqKgoXbp0SU2aNNH777+viIiICo/Nzc0t98sv7Xa7rly5ovz8fAUFBbliZCNVZ51/6ZVXXtGFCxc0fPjwOp7SfNVZ56NHj2r27NnauXOnvLz4Z6I6qrPO3333nXbt2qVGjRrp/fffV35+vuLi4nTmzBneN1OJ6qxznz59tHbtWj388MO6dOmSrly5ov/8z//Ua6+95uKpzZOcnKwvv/xS6enpVTreXd8LuTJzHe3bt9e+ffu0e/duTZw4UWPHjtWhQ4euefwvfyu39f9/HmFt/7bu+qa663zVunXrFB8fr3fffVctW7Z0waRmq+o6l5aWauTIkVqwYIHatWvnhknNVp0/z2VlZbLZbFq7dq169uypIUOGKDExUatXr+bqTCWqs86HDh3S1KlT9fzzzysjI0ObN29WVlYWv+OvEtnZ2Zo2bZr++Mc/qlGjRlV+nFu+F9bpi1j1zMCBA62nnnqqwn19+/a1pk6d6rRtw4YNlpeXl3X58mVXjFdvXG+dr0pOTrZ8fX2tv//97y6aqv651jqfPXvWkmQ1bNjQcbPZbI5t27Ztc8O05rren+cxY8ZYt99+u9O2Q4cOWZKsf/zjH64Yr9643jo/+uij1q9//WunbTt37rQkWT/88IMrxjPS+++/X+7fAkmWzWazGjZsaF25cqXcY9z1vZDrx9VgWZZKSkoq3BcVFaW//e1vTtu2bt2qyMhIeXt7u2K8euN66yz9dEVm3LhxWrduHa9534BrrbO/v78OHjzotO3111/X9u3b9d577yksLMxVI9YL1/vzfM899+jPf/6zzp8/ryZNmkiS/vGPf6hBgwZq1aqVK8c03vXW+eLFi+VeLm3YsKHjcajYwIEDy/1b8Pjjj6tDhw6aNWuWYw1/zm3fC+sskww3Z84c65NPPrGysrKsAwcOWHPnzrUaNGhgbd261bIsy5o9e7Y1evRox/Hfffed1bhxY+vpp5+2Dh06ZL311luWt7e39d5777nrSzBCddc5KSnJ8vLyspYvX27l5OQ4bufOnXPXl2CE6q7zL/Fppqqp7joXFRVZrVq1sn79619bmZmZVmpqqhUeHm6NHz/eXV+CEaq7zqtWrbK8vLys119/3fr222+tXbt2WZGRkVbPnj3d9SUY65efZvKU74VcmbmGU6dOafTo0crJyVFAQIC6dOmizZs3Kzo6WpKUk5OjEydOOI4PCwvTpk2b9PTTT2v58uUKDg7W0qVL9dBDD7nrSzBCddf5jTfe0JUrVzRp0iRNmjTJsX3s2LFavXq1q8c3RnXXGTVT3XVu0qSJUlJSNGXKFEVGRqp58+YaPny4XnzxRXd9CUao7jo/9thjKioq0rJly/Tf//3fatq0qe677z69/PLL7voS6g1P+V7Ib80GAABG49NMAADAaMQMAAAwGjEDAACMRswAAACjETMAAMBoxAwAADAaMQMAAIxGzAAAAKMRM8C/qNDQUC1ZssSl5+zfv7+mT59epWPdMR8AMxEzgAeozjd5oKaOHTsmm82mffv2uXsUoFYRMwBQx3788Ud3jwDUa8QM4GaPPfaYUlNT9eqrr8pms8lms+nYsWNKTU1Vz5495ePjo6CgIM2ePVtXrlxxPK5///6aPHmyJk+erKZNm6p58+Z69tlnVdNft1ZQUKCnnnpKLVu2lL+/v+677z7t379fknTkyBHZbDZ9/fXXTo9JTExUaGio45yHDh3SkCFD1KRJE9ntdo0ePVr5+fk1XBlnq1atUkBAgFJSUio91zvvvKPmzZurpKTE6TkeeughjRkzRpK0f/9+DRgwQH5+fvL391ePHj20Z8+eSudYvXq1mjZtqr/85S9q166dGjVqpOjoaGVnZzuOiY+PV7du3fT222+rbdu28vHxkWVZ113jqsyUlpame++9V76+vgoJCdHUqVN14cIFx/7Q0FAlJCRo3Lhx8vPzU+vWrbVy5UrH/rCwMEnSXXfdJZvNpv79+1d1+QGPRswAbvbqq68qKipKTz75pHJycpSTkyNvb28NGTJEd999t/bv368VK1borbfeKvfblNesWSMvLy99/vnnWrp0qRYvXqw//OEP1Z7Bsizdf//9ys3N1aZNm5SRkaHu3btr4MCBOnPmjNq3b68ePXpo7dq1To9LSkrSyJEjZbPZlJOTo379+qlbt27as2ePNm/erFOnTmn48OE3tD6S9L//+7965plntGXLFkVHR1d6rv/6r/9SaWmpNm7c6HiO/Px8/f3vf9fjjz8uSRo1apRatWql9PR0ZWRkaPbs2fL29q7SPBcvXtRvf/tbrVmzRp9++qkKCws1YsQIp2O++eYb/elPf9L69esdL+tcb40rm+ngwYMaPHiwhg0bpgMHDujdd9/Vrl27NHnyZKfzvvLKK4qMjNTevXsVFxeniRMnOiL0iy++kCR99NFHysnJ0YYNG6rzvwHwXBYAt+vXr581bdo0x/25c+da7du3t8rKyhzbli9fbjVp0sQqLS11PKZjx45Ox8yaNcvq2LFjlc7Zpk0ba/HixZZlWda2bdssf39/69KlS07H3H777dYbb7xhWZZlJSYmWm3btnXsO3LkiCXJyszMtCzLsp577jkrJibG6fHZ2dmWJOvIkSMVfp1VmW/27NlWUFCQdeDAAce+qpxr4sSJVmxsrGP/kiVLrLZt2zrWy8/Pz1q9enWVZvm5VatWWZKs3bt3O7YdPnzYkmR9/vnnlmVZ1vz58y1vb28rLy/PcUxV1vh6M40ePdp66qmnnLbt3LnTatCggVVcXGxZ1k9r9uijjzr2l5WVWS1btrRWrFhhWZZlZWVlWZKsvXv3VvvrBjwZV2YAD3T48GFFRUXJZrM5tt1zzz06f/68vv/+e8e23r17Ox0TFRWlo0ePqrS0tFrny8jI0Pnz59W8eXM1adLEccvKytK3334rSRoxYoSOHz+u3bt3S5LWrl2rbt26KSIiwvEcH3/8sdPjO3ToIEmO56iuV155RW+88YZ27dqlO++802neys715JNPauvWrTp58qSkn16meuyxxxzrNWPGDI0fP16DBg3SSy+9VK0Zvby8FBkZ6bjfoUMHNW3aVIcPH3Zsa9OmjVq0aOE0c2VrfL2ZMjIytHr1aqfHDh48WGVlZcrKynIc16VLF8d/22w2BQYGKi8vr8pfG2AiL3cPAKA8y7KcIuXqNknltteGsrIyBQUFaceOHeX2NW3aVJIUFBSkAQMGKCkpSb1799a6des0YcIEp+cYOnSoXn755XLPERQUVKO5+vbtqw8++EB/+tOfNHv27Gqd66677lLXrl31zjvvaPDgwTp48KD+9re/OY6Lj4/XyJEj9cEHH+jDDz/U/PnzlZycrF/96ldVmq2i/w8/33bzzTc77avKGl9vprKyMk2YMEFTp04t9/jWrVs7/vuXL5XZbDaVlZVV6WsCTEXMAB7gpptucrqaEhERofXr1ztFTVpamvz8/HTbbbc5jrt6leTn98PDw9WwYcNqnb979+7Kzc2Vl5eXQkNDr3ncqFGjNGvWLD3yyCP69ttvnd4n0r17d61fv16hoaHy8qqdf1p69uypKVOmaPDgwWrYsKH+53/+p1rnGj9+vBYvXqyTJ09q0KBBCgkJcdrfrl07tWvXTk8//bQeeeQRrVq1qkoxc+XKFe3Zs0c9e/aU9NMbpM+dO+e4OlSRqq7xtWbq3r27MjMzdccdd1Q637XcdNNNklTtK3eAp+NlJsADhIaG6vPPP9exY8eUn5+vuLg4ZWdna8qUKfr666/117/+VfPnz9eMGTPUoME//9pmZ2drxowZOnLkiNatW6fXXntN06ZNq/b5Bw0apKioKD344IPasmWLjh07prS0ND377LNOn6YZNmyYCgsLNXHiRA0YMMAprCZNmqQzZ87okUce0RdffKHvvvtOW7du1bhx427om2dUVJQ+/PBDvfDCC1q8eHG1zjVq1CidPHlSb775psaNG+fYXlxcrMmTJ2vHjh06fvy4Pv30U6Wnp6tjx45Vmsnb21tTpkzR559/ri+//FKPP/64evfu7YibilS2xpXNNGvWLH322WeaNGmS9u3bp6NHj2rjxo2aMmVKldeyZcuW8vX1dbxhuqCgoMqPBTwZMQN4gGeeeUYNGzZURESEWrRooR9//FGbNm3SF198oa5du+o3v/mNnnjiCT377LNOjxszZoyKi4vVs2dPTZo0SVOmTNFTTz1V7fPbbDZt2rRJ9957r8aNG6d27dppxIgROnbsmOx2u+M4f39/DR06VPv379eoUaOcniM4OFiffvqpSktLNXjwYHXu3FnTpk1TQECAU4DVxD333KMPPvhAzz33nJYuXVrlc/n7++uhhx5SkyZN9OCDDzq2N2zYUKdPn9aYMWPUrl07DR8+XLGxsVqwYEGV5mncuLFmzZqlkSNHKioqSr6+vkpOTr7uYypb48pm6tKli1JTU3X06FH17dtXd911l5577rlqvYTn5eWlpUuX6o033lBwcLAeeOCBKj8W8GQ2y6rhD6UA4Fb9+/dXt27d+JH/lYiOjlbHjh21dOnSWnm+1atXa/r06Tp37lytPB+AG8d7ZgDUS2fOnNHWrVu1fft2LVu2zN3jAKhDxAxQD+3cuVOxsbHX3H/+/HkXTlOeK+br3r27zp49q5dfflnt27ev8uNiY2O1c+fOCvfNnTtXwcHBNzwbgNrFy0xAPVRcXOz4+SoVuZFPxNQGT57v5MmTKi4urnBfs2bN1KxZMxdPBKAyxAwAADAan2YCAABGI2YAAIDRiBkAAGA0YgYAABiNmAEAAEYjZgAAgNGIGQAAYDRiBgAAGO3/AWfNHGyCmIBwAAAAAElFTkSuQmCC", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["sns.histplot(df, x=\"top_level_keys_present\")"]}, {"cell_type": "code", "execution_count": 7, "id": "a18db11e", "metadata": {}, "outputs": [{"data": {"text/plain": ["<Axes: xlabel='percent_node_with_valid_type', ylabel='Count'>"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["sns.histplot(df, x=\"percent_node_with_valid_type\")"]}, {"cell_type": "code", "execution_count": 8, "id": "3c7c398e", "metadata": {}, "outputs": [{"data": {"text/plain": ["<Axes: xlabel='percent_node_with_valid_version', ylabel='Count'>"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["sns.histplot(df, x=\"percent_node_with_valid_version\")"]}, {"cell_type": "code", "execution_count": 9, "id": "4c46bdcf", "metadata": {}, "outputs": [{"data": {"text/plain": ["<Axes: xlabel='percent_node_with_valid_structure', ylabel='Count'>"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["sns.histplot(df, x=\"percent_node_with_valid_structure\")"]}, {"cell_type": "code", "execution_count": 10, "id": "44883d67", "metadata": {}, "outputs": [{"data": {"text/plain": ["<Axes: xlabel='percent_node_with_parameters', ylabel='Count'>"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["sns.histplot(df, x=\"percent_node_with_parameters\")"]}, {"cell_type": "code", "execution_count": 11, "id": "048db9e6", "metadata": {}, "outputs": [{"data": {"text/plain": ["<Axes: xlabel='percent_connections_with_valid_structure', ylabel='Count'>"]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["sns.histplot(df, x=\"percent_connections_with_valid_structure\")"]}, {"cell_type": "code", "execution_count": 12, "id": "24225a83", "metadata": {}, "outputs": [{"data": {"text/plain": ["<Axes: xlabel='percent_connections_with_valid_target_node', ylabel='Count'>"]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["sns.histplot(df, x=\"percent_connections_with_valid_target_node\")"]}, {"cell_type": "code", "execution_count": 13, "id": "9bb20feb", "metadata": {}, "outputs": [], "source": ["df_prime = df"]}, {"cell_type": "markdown", "id": "a691c4b9", "metadata": {}, "source": ["## Easy workflows"]}, {"cell_type": "code", "execution_count": 14, "id": "db12a210", "metadata": {}, "outputs": [], "source": ["df = df_prime[df_prime[\"difficulty\"] == \"easy\"]"]}, {"cell_type": "code", "execution_count": 15, "id": "b99f16aa", "metadata": {}, "outputs": [{"data": {"text/plain": ["<Axes: xlabel='llm_score', ylabel='Count'>"]}, "execution_count": 15, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["sns.histplot(df, x=\"llm_score\", bins=20)"]}, {"cell_type": "code", "execution_count": 16, "id": "28199c28", "metadata": {}, "outputs": [{"data": {"text/plain": ["np.float64(0.7619047619047619)"]}, "execution_count": 16, "metadata": {}, "output_type": "execute_result"}], "source": ["(df[\"llm_score\"]>=7).sum()/len(df)"]}, {"cell_type": "code", "execution_count": 17, "id": "03e96ef0", "metadata": {}, "outputs": [{"data": {"text/plain": ["<Axes: xlabel='top_level_keys_present', ylabel='Count'>"]}, "execution_count": 17, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["sns.histplot(df, x=\"top_level_keys_present\")"]}, {"cell_type": "code", "execution_count": 18, "id": "0991d8aa", "metadata": {}, "outputs": [{"data": {"text/plain": ["<Axes: xlabel='percent_node_with_valid_type', ylabel='Count'>"]}, "execution_count": 18, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["sns.histplot(df, x=\"percent_node_with_valid_type\")"]}, {"cell_type": "code", "execution_count": 19, "id": "7c475b8e", "metadata": {}, "outputs": [{"data": {"text/plain": ["<Axes: xlabel='percent_node_with_valid_version', ylabel='Count'>"]}, "execution_count": 19, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["sns.histplot(df, x=\"percent_node_with_valid_version\")"]}, {"cell_type": "markdown", "id": "9291756b", "metadata": {}, "source": ["## Medium workflows"]}, {"cell_type": "code", "execution_count": 20, "id": "7c7d96c7", "metadata": {}, "outputs": [], "source": ["df = df_prime[df_prime[\"difficulty\"] == \"medium\"]"]}, {"cell_type": "code", "execution_count": 21, "id": "44472701", "metadata": {}, "outputs": [{"data": {"text/plain": ["<Axes: xlabel='llm_score', ylabel='Count'>"]}, "execution_count": 21, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["sns.histplot(df, x=\"llm_score\", bins=20)"]}, {"cell_type": "code", "execution_count": 22, "id": "09842340", "metadata": {}, "outputs": [{"data": {"text/plain": ["np.float64(0.9047619047619048)"]}, "execution_count": 22, "metadata": {}, "output_type": "execute_result"}], "source": ["(df[\"llm_score\"]>=7).sum()/len(df)"]}, {"cell_type": "code", "execution_count": 23, "id": "a4688dfe", "metadata": {}, "outputs": [{"data": {"text/plain": ["<Axes: xlabel='top_level_keys_present', ylabel='Count'>"]}, "execution_count": 23, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["sns.histplot(df, x=\"top_level_keys_present\")"]}, {"cell_type": "code", "execution_count": 24, "id": "8066e818", "metadata": {}, "outputs": [{"data": {"text/plain": ["<Axes: xlabel='percent_node_with_valid_type', ylabel='Count'>"]}, "execution_count": 24, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["sns.histplot(df, x=\"percent_node_with_valid_type\")"]}, {"cell_type": "code", "execution_count": 25, "id": "90a9d9a3", "metadata": {}, "outputs": [{"data": {"text/plain": ["<Axes: xlabel='percent_node_with_valid_version', ylabel='Count'>"]}, "execution_count": 25, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["sns.histplot(df, x=\"percent_node_with_valid_version\")"]}, {"cell_type": "markdown", "id": "4d763954", "metadata": {}, "source": ["## Hard workflows"]}, {"cell_type": "code", "execution_count": 26, "id": "0c4a08d8", "metadata": {}, "outputs": [], "source": ["df = df_prime[df_prime[\"difficulty\"] == \"hard\"]"]}, {"cell_type": "code", "execution_count": 27, "id": "521a045f", "metadata": {}, "outputs": [{"data": {"text/plain": ["<Axes: xlabel='llm_score', ylabel='Count'>"]}, "execution_count": 27, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAjcAAAGxCAYAAACeKZf2AAAAOnRFWHRTb2Z0d2FyZQBNYXRwbG90bGliIHZlcnNpb24zLjEwLjAsIGh0dHBzOi8vbWF0cGxvdGxpYi5vcmcvlHJYcgAAAAlwSFlzAAAPYQAAD2EBqD+naQAALjpJREFUeJzt3X9UVXW+//HXSfBACuaPgMMIgmmI+CMHmsL8VRQubVw2ebtN5Y/JnDVO5i+W2aDda/aLpus45LVgmFFJrWyty7Wb19/TCOXVJlEox9CxyYTLQFyaFH8eFPb3D7+e5gz4AzznbPj4fKy11/Lz2Z/92e+9PcLLvfc5x2FZliUAAABD3GB3AQAAAL5EuAEAAEYh3AAAAKMQbgAAgFEINwAAwCiEGwAAYBTCDQAAMArhBgAAGCXI7gICrbGxUX/9618VFhYmh8NhdzkAAOAqWJalEydOKDo6WjfccPlrM9dduPnrX/+qmJgYu8sAAACtUFFRoZ49e152zHUXbsLCwiRdODnh4eE2VwMAAK5GXV2dYmJiPL/HL+e6CzcXb0WFh4cTbgAAaGeu5pESHigGAABGIdwAAACjEG4AAIBRCDcAAMAohBsAAGAUwg0AADAK4QYAABiFcAMAAIxCuAEAAEYh3AAAAKO0mXCTlZUlh8OhOXPmXHZcUVGRkpOTFRISot69eys3NzcwBQIAgHahTYSbPXv2KC8vT4MGDbrsuCNHjmjs2LEaPny4SkpKtGDBAs2aNUsFBQUBqhQAALR1toebkydP6rHHHtNvf/tbde3a9bJjc3NzFRsbq+zsbCUmJmratGmaOnWqlixZEqBqAQBAW2d7uJkxY4buv/9+***********************************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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["sns.histplot(df, x=\"llm_score\", bins=20)"]}, {"cell_type": "code", "execution_count": 28, "id": "19ab5cdf", "metadata": {}, "outputs": [{"data": {"text/plain": ["np.float64(0.6190476190476191)"]}, "execution_count": 28, "metadata": {}, "output_type": "execute_result"}], "source": ["(df[\"llm_score\"]>=7).sum()/len(df)"]}, {"cell_type": "code", "execution_count": 29, "id": "516e335b", "metadata": {}, "outputs": [{"data": {"text/plain": ["<Axes: xlabel='top_level_keys_present', ylabel='Count'>"]}, "execution_count": 29, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["sns.histplot(df, x=\"top_level_keys_present\")"]}, {"cell_type": "code", "execution_count": 30, "id": "4cd2f1aa", "metadata": {}, "outputs": [{"data": {"text/plain": ["<Axes: xlabel='percent_node_with_valid_type', ylabel='Count'>"]}, "execution_count": 30, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAioAAAGxCAYAAABMeZ2uAAAAOnRFWHRTb2Z0d2FyZQBNYXRwbG90bGliIHZlcnNpb24zLjEwLjAsIGh0dHBzOi8vbWF0cGxvdGxpYi5vcmcvlHJYcgAAAAlwSFlzAAAPYQAAD2EBqD+naQAAKgBJREFUeJzt3Xl01PW9//HXGMIQyMImIYFA2PdNUIQiBBcobii1CrKEIlSUVVqkKXID1Ba0x6VVixevBRW4eMsmepG1BBAEWQViZA0EIRiDwASIk5B8fn/4Yy5jWCYh4fuJeT7OmXOc7/c7M+/5mJDnmdVljDECAACw0C1ODwAAAHA1hAoAALAWoQIAAKxFqAAAAGsRKgAAwFqECgAAsBahAgAArEWoAAAAa5VzeoAbkZ+frxMnTigsLEwul8vpcQAAQACMMcrKylJ0dLRuueXaj5mU6lA5ceKEYmJinB4DAAAUwbFjx1S7du1rHlOqQyUsLEzSj3c0PDzc4WkAAEAgPB6PYmJifH/Hr6VUh8qlp3vCw8MJFQAASplAXrbBi2kBAIC1CBUAAGAtQgUAAFiLUAEAANYiVAAAgLUIFQAAYC1CBQAAWItQAQAA1iJUAACAtQgVAABgLUdDZfLkyXK5XH6nmjVrOjkSAACwiOPf9dOiRQutXr3adz4oKMjBaQAAgE0cD5Vy5crxKAoAALgix1+jcuDAAUVHR6tevXrq27evDh8+7PRIAADAEo4+otKxY0e9//77aty4sb799lu9+OKL6ty5s5KTk1WtWrUCx3u9Xnm9Xt95j8dzM8cFikVaWpoyMzOdHqNUqF69uurUqeP0GPiZ4XewcJz+PXQZY4xjt/4T58+fV4MGDfT8889r3LhxBfZPnjxZU6ZMKbD97NmzCg8PvxkjAjckLS1NTZs2U3b2BadHKRVCQirq669TiBUUG34HC68kfg89Ho8iIiIC+vvt+GtULlepUiW1atVKBw4cuOL+hIQEv4DxeDyKiYm5WeMBNywzM1PZ2RfUcUiiwqNinR7Hap70I9ryzynKzMwkVFBs+B0sHBt+D60KFa/Xq5SUFN11111X3O92u+V2u2/yVEDxC4+KVdU6TZweAyiz+B0sPRx9Me3vf/97rVu3TqmpqdqyZYsee+wxeTwexcfHOzkWAACwhKOPqHzzzTfq16+fMjMzdeutt+rOO+/U5s2bVbduXSfHAgAAlnA0VObPn+/kzQMAAMs5/jkqAAAAV0OoAAAAaxEqAADAWoQKAACwFqECAACsRagAAABrESoAAMBahAoAALAWoQIAAKxFqAAAAGsRKgAAwFqECgAAsBahAgAArEWoAAAAaxEqAADAWoQKAACwFqECAACsRagAAABrESoAAMBahAoAALAWoQIAAKxFqAAAAGsRKgAAwFqECgAAsBahAgAArEWoAAAAaxEqAADAWoQKAACwFqECAACsRagAAABrESoAAMBahAoAALAWoQIAAKxFqAAAAGsRKgAAwFqECgAAsBahAgAArEWoAAAAaxEqAADAWoQKAACwFqECAACsRagAAABrESoAAMBahAoAALAWoQIAAKxFqAAAAGsRKgAAwFqECgAAsBahAgAArEWoAAAAaxEqAADAWoQKAACwFqECAACsRagAAABrESoAAMBahAoAALAWoQIAAKxFqAAAAGsRKgAAwFqECgAAsJY1oTJt2jS5XC6NHTvW6VEAAIAlrAiVrVu3aubMmWrdurXTowAAAIs4Hirnzp1T//799c4776hKlSpOjwMAACxSzukBRowYoQceeED33nuvXnzxxWse6/V65fV6fec9Hk9JjwcApUJaWpoyMzOdHsN6KSkpTo+AQnI0VObPn68dO3Zo69atAR0/bdo0TZkypYSnAoDSJS0tTU2bNlN29gWnRyk1cr05To+AADkWKseOHdOYMWO0cuVKVahQIaDLJCQkaNy4cb7zHo9HMTExJTUiAJQKmZmZys6+oI5DEhUeFev0OFZL3/O59i6dqYsXLzo9CgLkWKhs375dGRkZat++vW9bXl6e1q9frzfffFNer1dBQUF+l3G73XK73Td7VAAoFcKjYlW1ThOnx7CaJ/2I0yOgkBwLlXvuuUd79uzx2/ab3/xGTZs21YQJEwpECgAAKHscC5WwsDC1bNnSb1ulSpVUrVq1AtsBAEDZ5PjbkwEAAK7G8bcnXy4pKcnpEQAAgEV4RAUAAFiLUAEAANYiVAAAgLUIFQAAYC1CBQAAWItQAQAA1iJUAACAtQgVAABgLUIFAABYi1ABAADWIlQAAIC1CBUAAGAtQgUAAFiLUAEAANYiVAAAgLUIFQAAYC1CBQAAWItQAQAA1iJUAACAtQgVAABgLUIFAABYi1ABAADWIlQAAIC1CBUAAGAtQgUAAFiLUAEAANYiVAAAgLUIFQAAYC1CBQAAWItQAQAA1iJUAACAtQgVAABgLUIFAABYi1ABAADWIlQAAIC1CBUAAGAtQgUAAFiLUAEAANYiVAAAgLUIFQAAYC1CBQAAWItQAQAA1iJUAACAtQgVAABgLUIFAABYi1ABAADWIlQAAIC1CBUAAGAtQgUAAFiLUAEAANYiVAAAgLUIFQAAYC1CBQAAWItQAQAA1iJUAACAtQgVAABgLUIFAABYi1ABAADWIlQAAIC1HA2VGTNmqHXr1goPD1d4eLg6deqkTz/91MmRAACARRwNldq1a2v69Onatm2btm3bprvvvlu9e/dWcnKyk2MBAABLlHPyxh966CG/83/+8581Y8YMbd68WS1atHBoKgAAYAtHQ+VyeXl5+te//qXz58+rU6dOVzzG6/XK6/X6zns8nps1HgCHpKSkOD2C9Vgj/Jw5Hip79uxRp06d9MMPPyg0NFSLFy9W8+bNr3jstGnTNGXKlJs8IQAnZJ89JcmlAQMGOD1KqZHrzXF6BKDYOR4qTZo00a5du3TmzBktXLhQ8fHxWrdu3RVjJSEhQePGjfOd93g8iomJuZnjArhJci9kSTJq++QE3VqvqdPjWC19z+fau3SmLl686PQoQLFzPFTKly+vhg0bSpI6dOigrVu36m9/+5v+8z//s8Cxbrdbbrf7Zo8IwEGhNeqoap0mTo9hNU/6EadHAEqMdZ+jYozxex0KAAAouxx9ROWPf/yjevXqpZiYGGVlZWn+/PlKSkrS8uXLnRwLAABYwtFQ+fbbbzVw4EClp6crIiJCrVu31vLly3Xfffc5ORYAALCEo6Hy7rvvOnnzAADActa9RgUAAOCSIoVK/fr1derUqQLbz5w5o/r169/wUAAAAFIRQ+XIkSPKy8srsN3r9er48eM3PBQAAIBUyNeoLF261PffK1asUEREhO98Xl6e1qxZo9jY2GIbDgAAlG2FCpVHHnlEkuRyuRQfH++3Lzg4WLGxsXrllVeKbTgAAFC2FSpU8vPzJUn16tXT1q1bVb169RIZCgAAQCri25NTU1OLew4AAIACivw5KmvWrNGaNWuUkZHhe6Tlkn/+8583PBgAAECRQmXKlCmaOnWqOnTooKioKLlcruKeCwAAoGih8vbbb2v27NkaOHBgcc8DAADgU6TPUcnJyVHnzp2LexYAAAA/RQqVoUOHat68ecU9CwAAgJ8iPfXzww8/aObMmVq9erVat26t4OBgv/2vvvpqsQwHAADKtiKFyu7du9W2bVtJ0t69e/328cJaAABQXIoUKmvXri3uOQAAAAoo0mtUAAAAboYiPaLSvXv3az7F8+9//7vIAwEAAFxSpFC59PqUS3Jzc7Vr1y7t3bu3wJcVAgAAFFWRQuW111674vbJkyfr3LlzNzQQAADAJcX6GpUBAwbwPT8AAKDYFGuofP7556pQoUJxXiUAACjDivTUT58+ffzOG2OUnp6ubdu2adKkScUyGAAAQJFCJSIiwu/8LbfcoiZNmmjq1Knq0aNHsQwGAABQpFCZNWtWcc8BAABQQJFC5ZLt27crJSVFLpdLzZs3V7t27YprLgAAgKKFSkZGhvr27aukpCRVrlxZxhidPXtW3bt31/z583XrrbcW95wAAKAMKtK7fkaNGiWPx6Pk5GR9//33On36tPbu3SuPx6PRo0cX94wAAKCMKtIjKsuXL9fq1avVrFkz37bmzZvrrbfe4sW0AACg2BTpEZX8/HwFBwcX2B4cHKz8/PwbHgoAAEAqYqjcfffdGjNmjE6cOOHbdvz4cT333HO65557im04AABQthUpVN58801lZWUpNjZWDRo0UMOGDVWvXj1lZWXpjTfeKO4ZAQBAGVWk16jExMRox44dWrVqlb7++msZY9S8eXPde++9xT0fAAAowwr1iMq///1vNW/eXB6PR5J03333adSoURo9erRuv/12tWjRQhs2bCiRQQEAQNlTqFB5/fXXNWzYMIWHhxfYFxERoaefflqvvvpqsQ0HAADKtkKFypdffqlf/vKXV93fo0cPbd++/YaHAgAAkAoZKt9+++0V35Z8Sbly5fTdd9/d8FAAAABSIUOlVq1a2rNnz1X37969W1FRUTc8FAAAgFTIULn//vv1H//xH/rhhx8K7MvOzlZiYqIefPDBYhsOAACUbYV6e/ILL7ygRYsWqXHjxho5cqSaNGkil8ullJQUvfXWW8rLy9PEiRNLalYAAFDGFCpUIiMjtWnTJj3zzDNKSEiQMUaS5HK51LNnT/3jH/9QZGRkiQwKAADKnkJ/4FvdunW1bNkynT59WgcPHpQxRo0aNVKVKlVKYj4AAFCGFemTaSWpSpUquv3224tzFgAAAD9F+q4fAACAm4FQAQAA1iJUAACAtQgVAABgLUIFAABYi1ABAADWIlQAAIC1CBUAAGAtQgUAAFiLUAEAANYiVAAAgLUIFQAAYC1CBQAAWItQAQAA1iJUAACAtQgVAABgLUIFAABYy9FQmTZtmm6//XaFhYWpRo0aeuSRR7Rv3z4nRwIAABZxNFTWrVunESNGaPPmzVq1apUuXryoHj166Pz5806OBQAALFHOyRtfvny53/lZs2apRo0a2r59u7p27erQVAAAwBZWvUbl7NmzkqSqVas6PAkAALCBo4+oXM4Yo3HjxqlLly5q2bLlFY/xer3yer2+8x6Pp0RnSktLU2ZmZonexs+F1+uV2+12egzrpaSkOD0CAJQq1oTKyJEjtXv3bn322WdXPWbatGmaMmXKTZknLS1NTZs2U3b2hZtye6WeyyUZ4/QUpUauN8fpEQCgVLAiVEaNGqWlS5dq/fr1ql279lWPS0hI0Lhx43znPR6PYmJiSmSmzMxMZWdfUMchiQqPii2R2/i5SN/zufYunam2T07QrfWaOj2O1S6t1cWLF50eBQBKBUdDxRijUaNGafHixUpKSlK9evWuebzb7b7pTy+ER8Wqap0mN/U2SxtP+hFJUmiNOqzVdVxaKwBAYBwNlREjRmjevHn66KOPFBYWppMnT0qSIiIiFBIS4uRoAADAAo6+62fGjBk6e/as4uLiFBUV5Tt9+OGHTo4FAAAs4fhTPwAAAFdj1eeoAAAAXI5QAQAA1iJUAACAtQgVAABgLUIFAABYi1ABAADWIlQAAIC1CBUAAGAtQgUAAFiLUAEAANYiVAAAgLUIFQAAYC1CBQAAWItQAQAA1iJUAACAtQgVAABgLUIFAABYi1ABAADWIlQAAIC1CBUAAGAtQgUAAFiLUAEAANYiVAAAgLUIFQAAYC1CBQAAWItQAQAA1iJUAACAtQgVAABgLUIFAABYi1ABAADWIlQAAIC1CBUAAGAtQgUAAFiLUAEAANYiVAAAgLUIFQAAYC1CBQAAWItQAQAA1iJUAACAtQgVAABgLUIFAABYi1ABAADWIlQAAIC1CBUAAGAtQgUAAFiLUAEAANYiVAAAgLUIFQAAYC1CBQAAWItQAQAA1iJUAACAtQgVAABgLUIFAABYi1ABAADWIlQAAIC1CBUAAGAtQgUAAFiLUAEAANYiVAAAgLUIFQAAYC1HQ2X9+vV66KGHFB0dLZfLpSVLljg5DgAAsIyjoXL+/Hm1adNGb775ppNjAAAAS5Vz8sZ79eqlXr16OTkCAACwmKOhUlher1der9d33uPxODgNAAAoaaXqxbTTpk1TRESE7xQTE+P0SAAAoASVqlBJSEjQ2bNnfadjx445PRIAAChBpeqpH7fbLbfb7fQYAADgJilVj6gAAICyxdFHVM6dO6eDBw/6zqempmrXrl2qWrWq6tSp4+BkAADABo6GyrZt29S9e3ff+XHjxkmS4uPjNXv2bIemAgAAtnA0VOLi4mSMcXIEAABgMV6jAgAArEWoAAAAaxEqAADAWoQKAACwFqECAACsRagAAABrESoAAMBahAoAALAWoQIAAKxFqAAAAGsRKgAAwFqECgAAsBahAgAArEWoAAAAaxEqAADAWoQKAACwFqECAACsRagAAABrESoAAMBahAoAALAWoQIAAKxFqAAAAGsRKgAAwFqECgAAsBahAgAArEWoAAAAaxEqAADAWoQKAACwFqECAACsRagAAABrESoAAMBahAoAALAWoQIAAKxFqAAAAGsRKgAAwFqECgAAsBahAgAArEWoAAAAaxEqAADAWoQKAACwFqECAACsRagAAABrESoAAMBahAoAALAWoQIAAKxFqAAAAGsRKgAAwFqECgAAsBahAgAArEWoAAAAaxEqAADAWoQKAACwFqECAACsRagAAABrESoAAMBahAoAALAWoQIAAKxFqAAAAGsRKgAAwFqOh8o//vEP1atXTxUqVFD79u21YcMGp0cCAACWcDRUPvzwQ40dO1YTJ07Uzp07ddddd6lXr15KS0tzciwAAGAJR0Pl1Vdf1VNPPaWhQ4eqWbNmev311xUTE6MZM2Y4ORYAALCEY6GSk5Oj7du3q0ePHn7be/TooU2bNjk0FQAAsEk5p244MzNTeXl5ioyM9NseGRmpkydPXvEyXq9XXq/Xd/7s2bOSJI/HU+zznTt3TpL0/dF9uujNLvbr/znxpB+VJJ09fkDB5VwOT2M31ipwrFXgWKvAsVaF4zn540sxzp07V6x/ay9dlzHm+gcbhxw/ftxIMps2bfLb/uKLL5omTZpc8TKJiYlGEidOnDhx4sTpZ3A6duzYdXvBsUdUqlevrqCgoAKPnmRkZBR4lOWShIQEjRs3znc+Pz9f33//vapVqyaXq2yXscfjUUxMjI4dO6bw8HCnxymVWMPiwTreONbwxrGGxaOk1tEYo6ysLEVHR1/3WMdCpXz58mrfvr1WrVqlRx991Ld91apV6t279xUv43a75Xa7/bZVrly5JMcsdcLDw/mlvEGsYfFgHW8ca3jjWMPiURLrGBEREdBxjoWKJI0bN04DBw5Uhw4d1KlTJ82cOVNpaWkaPny4k2MBAABLOBoqTzzxhE6dOqWpU6cqPT1dLVu21LJly1S3bl0nxwIAAJZwNFQk6dlnn9Wzzz7r9BilntvtVmJiYoGnxhA41rB4sI43jjW8caxh8bBhHV3GBPLeIAAAgJvP8e/6AQAAuBpCBQAAWItQAQAA1iJUSpHJkyfL5XL5nWrWrOnbb4zR5MmTFR0drZCQEMXFxSk5OdnBie10/PhxDRgwQNWqVVPFihXVtm1bbd++3befdby+2NjYAj+LLpdLI0aMkMQaBuLixYt64YUXVK9ePYWEhKh+/fqaOnWq8vPzfcewjteXlZWlsWPHqm7dugoJCVHnzp21detW337WsKD169froYceUnR0tFwul5YsWeK3P5A183q9GjVqlKpXr65KlSrp4Ycf1jfffFMyA9/Ix+Dj5kpMTDQtWrQw6enpvlNGRoZv//Tp001YWJhZuHCh2bNnj3niiSdMVFSU8Xg8Dk5tl++//97UrVvXDB482GzZssWkpqaa1atXm4MHD/qOYR2vLyMjw+/ncNWqVUaSWbt2rTGGNQzEiy++aKpVq2Y++eQTk5qaav71r3+Z0NBQ8/rrr/uOYR2v7/HHHzfNmzc369atMwcOHDCJiYkmPDzcfPPNN8YY1vBKli1bZiZOnGgWLlxoJJnFixf77Q9kzYYPH25q1aplVq1aZXbs2GG6d+9u2rRpYy5evFjs8xIqpUhiYqJp06bNFffl5+ebmjVrmunTp/u2/fDDDyYiIsK8/fbbN2lC+02YMMF06dLlqvtZx6IZM2aMadCggcnPz2cNA/TAAw+YIUOG+G3r06ePGTBggDGGn8VAXLhwwQQFBZlPPvnEb3ubNm3MxIkTWcMA/DRUAlmzM2fOmODgYDN//nzfMcePHze33HKLWb58ebHPyFM/pcyBAwcUHR2tevXqqW/fvjp8+LAkKTU1VSdPnlSPHj18x7rdbnXr1k2bNm1yalzrLF26VB06dNCvf/1r1ahRQ+3atdM777zj2886Fl5OTo7mzJmjIUOGyOVysYYB6tKli9asWaP9+/dLkr788kt99tlnuv/++yXxsxiIixcvKi8vTxUqVPDbHhISos8++4w1LIJA1mz79u3Kzc31OyY6OlotW7YskXUlVEqRjh076v3339eKFSv0zjvv6OTJk+rcubNOnTrl+3LHn36hY2RkZIEvfizLDh8+rBkzZqhRo0ZasWKFhg8frtGjR+v999+XJNaxCJYsWaIzZ85o8ODBkljDQE2YMEH9+vVT06ZNFRwcrHbt2mns2LHq16+fJNYxEGFhYerUqZP+9Kc/6cSJE8rLy9OcOXO0ZcsWpaens4ZFEMianTx5UuXLl1eVKlWuekxxcvyTaRG4Xr16+f67VatW6tSpkxo0aKD33ntPd955pyQV+BZpY0yZ/2bpy+Xn56tDhw76y1/+Iklq166dkpOTNWPGDA0aNMh3HOsYuHfffVe9evUq8C2orOG1ffjhh5ozZ47mzZunFi1aaNeuXRo7dqyio6MVHx/vO451vLYPPvhAQ4YMUa1atRQUFKTbbrtNTz75pHbs2OE7hjUsvKKsWUmtK4+olGKVKlVSq1atdODAAd+7f35asxkZGQXKuCyLiopS8+bN/bY1a9ZMaWlpksQ6FtLRo0e1evVqDR061LeNNQzM+PHj9Yc//EF9+/ZVq1atNHDgQD333HOaNm2aJNYxUA0aNNC6det07tw5HTt2TF988YVyc3NVr1491rAIAlmzmjVrKicnR6dPn77qMcWJUCnFvF6vUlJSFBUV5fulXLVqlW9/Tk6O1q1bp86dOzs4pV1+8YtfaN++fX7b9u/f7/siTNaxcGbNmqUaNWrogQce8G1jDQNz4cIF3XKL/z/BQUFBvrcns46FU6lSJUVFRen06dNasWKFevfuzRoWQSBr1r59ewUHB/sdk56err1795bMuhb7y3NRYn73u9+ZpKQkc/jwYbN582bz4IMPmrCwMHPkyBFjzI9vKYuIiDCLFi0ye/bsMf369Svzb8P7qS+++MKUK1fO/PnPfzYHDhwwc+fONRUrVjRz5szxHcM6BiYvL8/UqVPHTJgwocA+1vD64uPjTa1atXxvT160aJGpXr26ef75533HsI7Xt3z5cvPpp5+aw4cPm5UrV5o2bdqYO+64w+Tk5BhjWMMrycrKMjt37jQ7d+40ksyrr75qdu7caY4ePWqMCWzNhg8fbmrXrm1Wr15tduzYYe6++27engzjey97cHCwiY6ONn369DHJycm+/fn5+SYxMdHUrFnTuN1u07VrV7Nnzx4HJ7bTxx9/bFq2bGncbrdp2rSpmTlzpt9+1jEwK1asMJLMvn37CuxjDa/P4/GYMWPGmDp16pgKFSqY+vXrm4kTJxqv1+s7hnW8vg8//NDUr1/flC9f3tSsWdOMGDHCnDlzxrefNSxo7dq1RlKBU3x8vDEmsDXLzs42I0eONFWrVjUhISHmwQcfNGlpaSUyL9+eDAAArMVrVAAAgLUIFQAAYC1CBQAAWItQAQAA1iJUAACAtQgVAABgLUIFAABYi1ABAADWIlQA3JCkpCS5XC6dOXPmpt/25MmT1bZt22sec+TIEblcLu3ateumzHQlP51z8ODBeuSRR655mbi4OI0dO7ZE5wJKA0IFKKVcLpeWLFni9BiO+v3vf681a9b4zgcSADb429/+ptmzZxfb9cXGxur1118vtusDbFLO6QGAn5O8vDy5XK4C34qLkhEaGqrQ0FCnxyi0iIgIp0cASg3+NUWZFhcXp5EjR2rkyJGqXLmyqlWrphdeeEGXvgIrJydHzz//vGrVqqVKlSqpY8eOSkpK8l1+9uzZqly5sj755BM1b95cbrdbR48eldfr1fPPP6+YmBi53W41atRI7777ru9yX331le6//36FhoYqMjJSAwcOVGZmpt9co0eP1vPPP6+qVauqZs2amjx5sm9/bGysJOnRRx+Vy+Xynb+WS08/fPDBB4qNjVVERIT69u2rrKws3zFer1ejR49WjRo1VKFCBXXp0kVbt271u55ly5apcePGCgkJUffu3XXkyJECt7Vp0yZ17dpVISEhiomJ0ejRo3X+/PnrzvjGG2+oVatWvvNLliyRy+XSW2+95dvWs2dPJSQk+N2nS//93nvv6aOPPpLL5ZLL5fL7f3X48GF1795dFStWVJs2bfT5559fd56zZ88qJCREy5cv99u+aNEiVapUSefOnZMkTZgwQY0bN1bFihVVv359TZo0Sbm5uVe93p8+8nP+/HkNGjRIoaGhioqK0iuvvHLd2S6Ji4vT0aNH9dxzz/nu9/nz5xUeHq4FCxb4Hfvxxx+rUqVKysrK8j0lNn/+fHXu3FkVKlRQixYt/NZMuv7PKlDiSuSrDoFSolu3biY0NNSMGTPGfP3112bOnDmmYsWKvm9UfvLJJ03nzp3N+vXrzcGDB81f//pX43a7zf79+40xxsyaNcsEBwebzp07m40bN5qvv/7anDt3zjz++OMmJibGLFq0yBw6dMisXr3azJ8/3xhjzIkTJ0z16tVNQkKCSUlJMTt27DD33Xef6d69u99c4eHhZvLkyWb//v3mvffeMy6Xy6xcudIYY0xGRoaRZGbNmmXS09NNRkbGde9rYmKiCQ0NNX369DF79uwx69evNzVr1jR//OMffceMHj3aREdHm2XLlpnk5GQTHx9vqlSpYk6dOmWMMSYtLc243W6/9YqMjDSSzOnTp40xxuzevduEhoaa1157zezfv99s3LjRtGvXzgwePPi6M+7evdu4XC7z3XffGWOMGTt2rKlevbr59a9/bYwxJjc314SGhppPP/3Ud5/atGljjPnxq+sff/xx88tf/tKkp6eb9PR04/V6TWpqqpFkmjZtaj755BOzb98+89hjj5m6deua3Nzc6870q1/9ygwYMKDAtn79+vnO/+lPfzIbN240qampZunSpSYyMtK89NJLfmt/aU5jjImPjze9e/f2nX/mmWdM7dq1zcqVK83u3bvNgw8+6Pu5vJ5Tp06Z2rVrm6lTp/rutzHGDBs2zNx///1+xz766KNm0KBBxhjjW5fatWubBQsWmK+++soMHTrUhIWFmczMTGNMYD+rQEkjVFCmdevWzTRr1szk5+f7tk2YMME0a9bMHDx40LhcLnP8+HG/y9xzzz0mISHBGPNjqEgyu3bt8u3ft2+fkWRWrVp1xducNGmS6dGjh9+2Y8eOGUlm3759vrm6dOnid8ztt99uJkyY4DsvySxevDjg+5qYmGgqVqxoPB6Pb9v48eNNx44djTHGnDt3zgQHB5u5c+f69ufk5Jjo6Gjz8ssvG2OMSUhIuOJ6XR4qAwcONL/97W/9bnvDhg3mlltuMdnZ2decMT8/31SvXt0sWLDAGGNM27ZtzbRp00yNGjWMMcZs2rTJlCtXzmRlZfnu07UCwJj/+4P8X//1X75tycnJRpJJSUm55jzGGLNo0SITGhpqzp8/b4wx5uzZs6ZChQrmf//3f696mZdfftm0b9/ed/5ac2ZlZZny5cv7QtaYH+MjJCQkoFAxxpi6deua1157zW/bli1bTFBQkO/n97vvvjPBwcEmKSnJGPN/6zJ9+nTfZXJzc03t2rV9kRXIzypQ0njqB2XenXfeKZfL5TvfqVMnHThwQNu2bZMxRo0bN/a9FiI0NFTr1q3ToUOHfMeXL19erVu39p3ftWuXgoKC1K1btyve3vbt27V27Vq/62zatKkk+V3v5dcpSVFRUcrIyLih+xobG6uwsLArXuehQ4eUm5urX/ziF779wcHBuuOOO5SSkiJJSklJueJ6/fT+zZ492+/+9ezZU/n5+UpNTb3mfC6XS127dlVSUpLOnDmj5ORkDR8+XHl5eUpJSVFSUpJuu+22Ir0u5fL1jIqKkqSA1vOBBx5QuXLltHTpUknSwoULFRYWph49eviOWbBggbp06aKaNWsqNDRUkyZNUlpaWkBzHTp0SDk5OX7rWLVqVTVp0iSgy1/NHXfcoRYtWuj999+XJH3wwQeqU6eOunbt6nfc5bdbrlw5dejQwff/O9CfVaAk8WJa4BqCgoK0fft2BQUF+W2//A9lSEiI3x/ukJCQa15nfn6+HnroIb300ksF9l36Ayr9GAmXc7lcys/PL9T8P3Wt6zT//3U5l9+XS9svbbt0zLXk5+fr6aef1ujRowvsq1OnznUvHxcXp5kzZ2rDhg1q06aNKleurK5du2rdunVKSkpSXFzcda/jSi6/75fuTyDrWb58eT322GOaN2+e+vbtq3nz5umJJ55QuXI//vO5efNm9e3bV1OmTFHPnj0VERGh+fPnB/w6k0DWtKiGDh2qN998U3/4wx80a9Ys/eY3vynw//dKLl+fQH5WgZLEIyoo8zZv3lzgfKNGjdSuXTvl5eUpIyNDDRs29DvVrFnzqtfXqlUr5efna926dVfcf9tttyk5OVmxsbEFrrdSpUoBzx0cHKy8vLyAj7+ehg0bqnz58vrss89823Jzc7Vt2zY1a9ZMktS8efMrrtflLt2/n963S9d/PXFxcUpOTtaCBQt8UdKtWzetXr1amzZtuuojVdKPUVGca3JJ//79tXz5ciUnJ2vt2rXq37+/b9/GjRtVt25dTZw4UR06dFCjRo109OjRgK+7YcOGCg4O9lvH06dPa//+/QFfx9Xu94ABA5SWlqa///3vSk5OVnx8fIFjLr/dixcvavv27b5HTYrrZxW4EYQKyrxjx45p3Lhx2rdvn/77v/9bb7zxhsaMGaPGjRurf//+GjRokBYtWqTU1FRt3bpVL730kpYtW3bV64uNjVV8fLyGDBmiJUuWKDU1VUlJSfqf//kfSdKIESP0/fffq1+/fvriiy90+PBhrVy5UkOGDCnUH9nY2FitWbNGJ0+e1OnTp294HSpVqqRnnnlG48eP1/Lly/XVV19p2LBhunDhgp566ilJ0vDhw3Xo0CHfes2bN6/A54FMmDBBn3/+uUaMGKFdu3bpwIEDWrp0qUaNGhXQHC1btlS1atU0d+5cX6jExcVpyZIlys7OVpcuXa562djYWO3evVv79u1TZmbmNd95UxjdunVTZGSk+vfvr9jYWN15552+fQ0bNlRaWprmz5+vQ4cO6e9//7sWL14c8HWHhobqqaee0vjx47VmzRrt3btXgwcPLtRb3GNjY7V+/XodP37c7x05VapUUZ8+fTR+/Hj16NFDtWvXLnDZt956S4sXL9bXX3+tESNG6PTp0xoyZIik4vtZBW4EoYIyb9CgQcrOztYdd9yhESNGaNSoUfrtb38rSZo1a5YGDRqk3/3ud2rSpIkefvhhbdmyRTExMde8zhkzZuixxx7Ts88+q6ZNm2rYsGG+t+dGR0dr48aNysvLU8+ePdWyZUuNGTNGERERhfrj9Morr2jVqlWKiYlRu3btir4Al5k+fbp+9atfaeDAgbrtttt08OBBrVixQlWqVJH041M3Cxcu1Mcff6w2bdro7bff1l/+8he/62jdurXWrVunAwcO6K677lK7du00adKkgJ8qcLlcvkdN7rrrLt91RkREqF27dgoPD7/qZYcNG6YmTZqoQ4cOuvXWW7Vx48aiLMMVZ+rXr5++/PJLv0dTJKl379567rnnNHLkSLVt21abNm3SpEmTCnX9f/3rX9W1a1c9/PDDuvfee9WlSxe1b98+4MtPnTpVR44cUYMGDXTrrbf67XvqqaeUk5Pji4+fmj59ul566SW1adNGGzZs0EcffaTq1atLKr6fVeBGuExJPkEKWC4uLk5t27blUz3xszV37lyNGTNGJ06c8Hvq7ciRI6pXr5527tx53a8hAJzEi2kB4GfowoULSk1N1bRp0/T0008H9PogwEY8dgf8TLRo0cLvbaSXn+bOnev0eJKkDRs2XHVGpz4Kv1evXled56dPazmhqGv28ssvq23btoqMjPR9ki9QGvHUD/AzcfTo0au+eDQyMtLv81Ockp2drePHj191f8OGDW/iND86fvy4srOzr7ivatWqqlq16k2eyJ+NawbcTIQKAACwFk/9AAAAaxEqAADAWoQKAACwFqECAACsRagAAABrESoAAMBahAoAALAWoQIAAKz1/wApBQxx8eISwgAAAABJRU5ErkJggg==", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["sns.histplot(df, x=\"percent_node_with_valid_type\")"]}, {"cell_type": "code", "execution_count": 31, "id": "d7940155", "metadata": {}, "outputs": [{"data": {"text/plain": ["<Axes: xlabel='percent_node_with_valid_version', ylabel='Count'>"]}, "execution_count": 31, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["sns.histplot(df, x=\"percent_node_with_valid_version\")"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.9"}}, "nbformat": 4, "nbformat_minor": 5}