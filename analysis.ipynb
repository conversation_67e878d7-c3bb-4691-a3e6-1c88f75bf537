{"cells": [{"cell_type": "code", "execution_count": 27, "id": "0427e816", "metadata": {}, "outputs": [], "source": ["import json\n", "import seaborn as sns\n", "import pandas as pd"]}, {"cell_type": "code", "execution_count": 28, "id": "9b2e955e", "metadata": {}, "outputs": [], "source": ["df = pd.read_json(\"result_openai_orr_20B-0.json\")"]}, {"cell_type": "code", "execution_count": 29, "id": "87ef84fc", "metadata": {}, "outputs": [{"data": {"application/vnd.microsoft.datawrangler.viewer.v0+json": {"columns": [{"name": "index", "rawType": "int64", "type": "integer"}, {"name": "index", "rawType": "int64", "type": "integer"}, {"name": "prompt", "rawType": "object", "type": "string"}, {"name": "json", "rawType": "object", "type": "string"}, {"name": "thinking", "rawType": "object", "type": "string"}, {"name": "difficulty", "rawType": "object", "type": "string"}, {"name": "generated_json", "rawType": "object", "type": "string"}, {"name": "llm_score", "rawType": "int64", "type": "integer"}, {"name": "json_parsed", "rawType": "float64", "type": "float"}, {"name": "top_level_keys_present", "rawType": "float64", "type": "float"}, {"name": "workflow_name_valid", "rawType": "float64", "type": "float"}, {"name": "num_nodes", "rawType": "float64", "type": "float"}, {"name": "num_nodes_with_valid_type", "rawType": "float64", "type": "float"}, {"name": "num_nodes_with_valid_version", "rawType": "float64", "type": "float"}, {"name": "num_nodes_with_valid_structure", "rawType": "float64", "type": "float"}, {"name": "num_nodes_with_parameters", "rawType": "float64", "type": "float"}, {"name": "num_connections", "rawType": "float64", "type": "float"}, {"name": "num_connections_with_valid_structure", "rawType": "float64", "type": "float"}, {"name": "num_connections_with_valid_target_node", "rawType": "float64", "type": "float"}, {"name": "active_field_boolean", "rawType": "float64", "type": "float"}, {"name": "evaluation_results", "rawType": "object", "type": "unknown"}, {"name": "percent_node_with_valid_type", "rawType": "float64", "type": "float"}, {"name": "percent_node_with_valid_version", "rawType": "float64", "type": "float"}, {"name": "percent_node_with_valid_structure", "rawType": "float64", "type": "float"}, {"name": "percent_connections_with_valid_structure", "rawType": "float64", "type": "float"}, {"name": "percent_connections_with_valid_target_node", "rawType": "float64", "type": "float"}, {"name": "percent_node_with_parameters", "rawType": "float64", "type": "float"}], "ref": "5f11e03e-b790-42bf-acf1-2680264ec648", "rows": [["0", "38", "Create a JSON object representing an n8n workflow for backing up other workflows, including nodes for scheduling, fetching workflows, looping, executing workflow backups, and sending Telegram and Gmail notifications. Ensure connections between nodes are defined to create a functional workflow.\n", "{\"name\": \"Main workflow loop backup\", \"nodes\": [{\"parameters\": {}, \"name\": \"On clicking 'execute'\", \"type\": \"n8n-nodes-base.manualTrigger\", \"typeVersion\": 1, \"position\": [1640, 580]}, {\"parameters\": {\"filters\": {}, \"requestOptions\": {}}, \"name\": \"n8n\", \"type\": \"n8n-nodes-base.n8n\", \"typeVersion\": 1, \"position\": [2040, 680]}, {\"parameters\": {\"options\": {}}, \"name\": \"Loop Over Items\", \"type\": \"n8n-nodes-base.splitInBatches\", \"typeVersion\": 3, \"position\": [2240, 680]}, {\"parameters\": {\"rule\": {\"interval\": [{\"triggerAtHour\": 1, \"triggerAtMinute\": 33}]}}, \"name\": \"Schedule Trigger\", \"type\": \"n8n-nodes-base.scheduleTrigger\", \"typeVersion\": 1.2, \"position\": [1620, 800]}, {\"parameters\": {\"content\": \"## Main workflow loop\", \"height\": 416.1856906618075, \"width\": 1272.6408145680155, \"color\": 7}, \"name\": \"Sticky Note2\", \"type\": \"n8n-nodes-base.stickyNote\", \"typeVersion\": 1, \"position\": [1620, 500]}, {\"parameters\": {\"workflowId\": \"=rkExj7m8P27lT2xs\", \"options\": {}}, \"name\": \"Execute Workflow\", \"type\": \"n8n-nodes-base.executeWorkflow\", \"typeVersion\": 1, \"position\": [2460, 720]}, {\"parameters\": {\"sendTo\": \"<EMAIL>\", \"subject\": \"=Backup Started {{ $execution.id }}\", \"emailType\": \"text\", \"message\": \"=:information_source:  Starting Workflow Backup [{{ $execution.id }}]\", \"options\": {}}, \"name\": \"Gmail\", \"type\": \"n8n-nodes-base.gmail\", \"typeVersion\": 2.1, \"position\": [1860, 220]}, {\"parameters\": {\"sendTo\": \"<EMAIL>\", \"subject\": \"=Backup Started {{ $execution.id }}\", \"emailType\": \"text\", \"message\": \"=\\u2705 Backup has completed - {{ $('n8n').all().length }} workflows have been processed.\", \"options\": {}}, \"name\": \"Gmail1\", \"type\": \"n8n-nodes-base.gmail\", \"typeVersion\": 2.1, \"position\": [2460, 360]}, {\"parameters\": {\"sendTo\": \"<EMAIL>\", \"subject\": \"=Backup Started {{ $execution.id }}\", \"emailType\": \"text\", \"message\": \"=:x: Failed to backup {{ $('Loop Over Items').item.json.id }}\", \"options\": {}}, \"name\": \"Gmail2\", \"type\": \"n8n-nodes-base.gmail\", \"typeVersion\": 2.1, \"position\": [2660, 1020]}, {\"parameters\": {\"chatId\": \"489359810\", \"text\": \"=:information_source:  Starting Workflow Backup [{{ $execution.id }}]\", \"additionalFields\": {}}, \"name\": \"Telegram\", \"type\": \"n8n-nodes-base.telegram\", \"typeVersion\": 1.2, \"position\": [1860, 680]}, {\"parameters\": {\"chatId\": \"489359810\", \"text\": \"=Backup n8n.psyii.od.ua/workflow Done {{ $execution.id }}\", \"additionalFields\": {}}, \"name\": \"Telegram1\", \"type\": \"n8n-nodes-base.telegram\", \"typeVersion\": 1.2, \"position\": [2460, 520]}, {\"parameters\": {\"chatId\": \"489359810\", \"text\": \"=:x: Failed to backup {{ $('Loop Over Items').item.json.id }}\", \"additionalFields\": {}}, \"name\": \"Telegram2\", \"type\": \"n8n-nodes-base.telegram\", \"typeVersion\": 1.2, \"position\": [2720, 780]}], \"connections\": {\"On clicking 'execute'\": {\"main\": [[{\"node\": \"Telegram\", \"type\": \"main\", \"index\": 0}]]}, \"n8n\": {\"main\": [[{\"node\": \"Loop Over Items\", \"type\": \"main\", \"index\": 0}]]}, \"Loop Over Items\": {\"main\": [[{\"node\": \"Telegram1\", \"type\": \"main\", \"index\": 0}], [{\"node\": \"Execute Workflow\", \"type\": \"main\", \"index\": 0}]]}, \"Schedule Trigger\": {\"main\": [[{\"node\": \"Telegram\", \"type\": \"main\", \"index\": 0}]]}, \"Execute Workflow\": {\"main\": [[{\"node\": \"Loop Over Items\", \"type\": \"main\", \"index\": 0}], [{\"node\": \"Telegram2\", \"type\": \"main\", \"index\": 0}]]}, \"Gmail\": {\"main\": [[]]}, \"Gmail2\": {\"main\": [[]]}, \"Telegram\": {\"main\": [[{\"node\": \"n8n\", \"type\": \"main\", \"index\": 0}]]}, \"Telegram2\": {\"main\": [[{\"node\": \"Loop Over Items\", \"type\": \"main\", \"index\": 0}]]}}, \"active\": false}", "Okay, let's design this workflow step-by-step for backing up n8n workflows.\n\nFirst things first, I need to consider how this workflow will be triggered. A backup process usually needs to run periodically, so a scheduling mechanism is essential. However, it's also very useful to have a way to manually kick it off for testing or immediate needs.\n\nSo, I'll start by adding two trigger nodes.\n1.  One will be a **Schedule Trigger** node. This is a very common and reliable way to initiate workflows at predefined times or intervals. Its `type` is `n8n-nodes-base.scheduleTrigger` and it's using `typeVersion` 1.2. I'd configure its parameters to `rule.interval` so it runs at a specific time, in this case, set for 1:33 (likely AM or PM, depending on the server's clock). This ensures our backup runs regularly, perhaps overnight, without manual intervention.\n2.  The other trigger will be a **Manual Trigger** node, with `type` `n8n-nodes-base.manualTrigger` and `typeVersion` 1. I'll name it \"On clicking 'execute'\". This is perfect for development, debugging, or when I just want to run a backup on demand outside of the schedule.\n\nNow that we have the triggers, the very first thing I want to do when the backup process starts is to send a notification. This gives me immediate feedback that the workflow has begun. I want both a quick chat message and potentially an email, although the current setup only uses Telegram for the initial start notification.\n\nSo, from both the \"Schedule Trigger\" node and the \"On clicking 'execute'\" node, I'll connect their `main` output to a **Telegram** node. This node has a `type` of `n8n-nodes-base.telegram` and `typeVersion` 1.2. I'll configure its `chatId` to a specific chat ID (which would be a generic placeholder here, not a real value) and its `text` parameter to clearly state that the workflow backup is starting. I'll use an expression like `\":information_source: Starting Workflow Backup [{{ $execution.id }}]\"` so I can track the specific execution ID right from the start. This is great for monitoring.\n\nNext, the core of this workflow is to fetch all other workflows so we can back them up. To do this, I need to interact with the n8n instance itself. So, from the \"Telegram\" node's `main` output, I'll connect it to an **n8n** node. This node's `type` is `n8n-nodes-base.n8n` with `typeVersion` 1. It's designed specifically for managing n8n resources like workflows, credentials, etc. I would configure this node to list or retrieve workflows. It requires an n8n API credential, which would be linked here as a placeholder. The `parameters` for `filters` and `requestOptions` are empty, meaning it's likely set to fetch all workflows, or at least a default list.\n\nOnce I've fetched a list of workflows, I need to process each one individually. I can't just send them all to a backup process at once; each one needs its own action. This is a classic use case for a loop or a batching node. So, from the \"n8n\" node's `main` output, I'll connect it to a **Loop Over Items** node. This node is actually of `type` `n8n-nodes-base.splitInBatches` with `typeVersion` 3. The name \"Loop Over Items\" is a custom name given to it. Its purpose is to take a list of items (in this case, workflows fetched by the n8n node) and process them one by one or in small batches. The `options` parameter is empty, suggesting it will process them individually or with default batching behavior, which is usually one by one for this kind of operation.\n\nNow, for each workflow item coming out of the \"Loop Over Items\" node, I need to trigger the actual backup. The user prompt mentions \"executing workflow backups,\" implying that there might be a separate, dedicated workflow that handles the actual backup logic (e.g., exporting the workflow JSON, saving it to a file system, cloud storage, etc.). This makes a lot of sense for modularity.\n\nSo, from the \"Loop Over Items\" node's `main` output, I'll connect it to an **Execute Workflow** node. This node has a `type` of `n8n-nodes-base.executeWorkflow` and `typeVersion` 1. The key parameter here is `workflowId`. It's configured to trigger a specific workflow (represented by a placeholder ID like `=rkExj7m8P27lT2xs`). This means for every workflow fetched by the \"n8n\" node, this \"Execute Workflow\" node will run that specific backup workflow. It also has `onError: \"continueErrorOutput\"`, which is crucial. If the backup of a single workflow fails, this setting ensures the main workflow doesn't stop; instead, it sends the error down a specific error path while the loop continues processing the remaining workflows.\n\nWhat about notifications after the loop? After all workflows have been processed (or attempted to be processed) by the \"Execute Workflow\" node, I want to know the whole backup job is done. There are two scenarios here: success and failure for individual workflow backups.\n\nLet's handle the success path for the loop first. From the \"Loop Over Items\" node, I'll create a second connection from its `main` output to a **Telegram1** node. This connection specifically handles the case when the \"Loop Over Items\" node has finished processing *all* its items. This Telegram node (`type`: `n8n-nodes-base.telegram`, `typeVersion`: 1.2) will send a completion message. The text, `\"=Backup n8n.psyii.od.ua/workflow Done {{ $execution.id }}\"` and `\"=Backup has completed - {{ $('n8n').all().length }} workflows have been processed.\"` (from the disabled Gmail node) indicates success and gives a count of processed workflows, which is useful.\n\nNow, what if an individual workflow backup fails within the loop? We used `onError: \"continueErrorOutput\"` on the \"Execute Workflow\" node. This means it will output items that failed to its `error` output. So, from the \"Execute Workflow\" node's `error` output, I'll connect it to a **Telegram2** node. This Telegram node (`type`: `n8n-nodes-base.telegram`, `typeVersion`: 1.2) is configured to send a message indicating a failure for a specific workflow, using an expression like `\"=X: Failed to backup {{ $('Loop Over Items').item.json.id }}\"`. This gives immediate alerts for problems. It's currently `disabled: true`, meaning it won't send messages, but the setup is there.\n\nAdditionally, for error reporting, there's a **Gmail2** node (`type`: `n8n-nodes-base.gmail`, `typeVersion`: 2.1) connected from the \"Execute Workflow\" node's `error` output. This provides an alternative or additional notification channel for failures. Its `message` is set to `\"=X: Failed to backup {{ $('Loop Over Items').item.json.id }}\"`, similar to the Telegram error message. This ensures detailed failure reports via email.\n\nThere are also a couple of disabled Gmail nodes:\n*   A **Gmail** node (`type`: `n8n-nodes-base.gmail`, `typeVersion`: 2.1) is present, configured to send a \"Backup Started\" email. It's connected from \"On clicking 'execute'\"'s `main` output, but it's currently disabled. This suggests it was considered as an alternative or additional initial notification method but then perhaps switched to Telegram for primary use.\n*   A **Gmail1** node (`type`: `n8n-nodes-base.gmail`, `typeVersion`: 2.1) is also disabled and configured to send a \"Backup has completed\" email. It would typically be connected after the \"Loop Over Items\" completes, similar to the \"Telegram1\" node. Its `message` expression correctly counts workflows using `$('n8n').all().length`.\n\nFinally, there's a **Sticky Note2** node (`type`: `n8n-nodes-base.stickyNote`, `typeVersion`: 1) which isn't functional. It's purely for documentation or visual organization within the workflow canvas, likely just containing a title like \"## Main workflow loop\". It helps to segment and label different parts of the workflow visually.Okay, let's design this workflow step-by-step for backing up n8n workflows.\n\nFirst things first, I need to consider how this workflow will be triggered. A backup process usually needs to run periodically, so a scheduling mechanism is essential. However, it's also very useful to have a way to manually kick it off for testing or immediate needs.\n\nSo, I'll start by adding two trigger nodes:\n1.  One will be a **Schedule Trigger** node. This is a very common and reliable way to initiate workflows at predefined times or intervals. Its `type` is `n8n-nodes-base.scheduleTrigger` and it's using `typeVersion` 1.2. I'd configure its parameters for `rule.interval` so it runs at a specific time, in this case, set for 1:33 (likely AM or PM, depending on the server's clock). This ensures our backup runs regularly, perhaps overnight, without manual intervention.\n2.  The other trigger will be a **Manual Trigger** node, with `type` `n8n-nodes-base.manualTrigger` and `typeVersion` 1. I'll name it \"On clicking 'execute'\". This is perfect for development, debugging, or when I just want to run a backup on demand outside of the schedule.\n\nNow that we have the triggers, the very first thing I want to do when the backup process starts is to send a notification. This gives me immediate feedback that the workflow has begun. I want both a quick chat message and potentially an email, although the current setup primarily uses Telegram for the initial start notification.\n\nSo, from both the \"Schedule Trigger\" node and the \"On clicking 'execute'\" node, I'll connect their `main` output to a **Telegram** node. This node has a `type` of `n8n-nodes-base.telegram` and `typeVersion` 1.2. I'll configure its `chatId` to a specific chat ID (which would be a generic placeholder here, not a real value) and its `text` parameter to clearly state that the workflow backup is starting. I'll use an expression like `\":information_source: Starting Workflow Backup [{{ $execution.id }}]\"` so I can track the specific execution ID right from the start. This is great for monitoring.\n\nNext, the core of this workflow is to fetch all other workflows so we can back them up. To do this, I need to interact with the n8n instance itself. So, from the \"Telegram\" node's `main` output, I'll connect it to an **n8n** node. This node's `type` is `n8n-nodes-base.n8n` with `typeVersion` 1. It's designed specifically for managing n8n resources like workflows, credentials, etc. I would configure this node to list or retrieve workflows. It requires an n8n API credential, which would be linked here as a placeholder. The `parameters` for `filters` and `requestOptions` are empty, meaning it's likely set to fetch all workflows, or at least a default list.\n\nOnce I've fetched a list of workflows, I need to process each one individually. I can't just send them all to a backup process at once; each one needs its own action. This is a classic use case for a loop or a batching node. So, from the \"n8n\" node's `main` output, I'll connect it to a **Loop Over Items** node. This node is actually of `type` `n8n-nodes-base.splitInBatches` with `typeVersion` 3. The name \"Loop Over Items\" is a custom name given to it. Its purpose is to take a list of items (in this case, workflows fetched by the n8n node) and process them one by one or in small batches. The `options` parameter is empty, suggesting it will process them individually or with default batching behavior, which is usually one by one for this kind of operation.\n\nNow, for each workflow item coming out of the \"Loop Over Items\" node, I need to trigger the actual backup. The user prompt mentions \"executing workflow backups,\" implying that there might be a separate, dedicated workflow that handles the actual backup logic (e.g., exporting the workflow JSON, saving it to a file system, cloud storage, etc.). This makes a lot of sense for modularity.\n\nSo, from the \"Loop Over Items\" node's `main` output, I'll connect it to an **Execute Workflow** node. This node has a `type` of `n8n-nodes-base.executeWorkflow` and `typeVersion` 1. The key parameter here is `workflowId`. It's configured to trigger a specific workflow (represented by a placeholder ID like `=rkExj7m8P27lT2xs`). This means for every workflow fetched by the \"n8n\" node, this \"Execute Workflow\" node will run that specific backup workflow. It also has `onError: \"continueErrorOutput\"`, which is crucial. If the backup of a single workflow fails, this setting ensures the main workflow doesn't stop; instead, it sends the error down a specific error path while the loop continues processing the remaining workflows.\n\nWhat about notifications after the loop? After all workflows have been processed (or attempted to be processed) by the \"Execute Workflow\" node, I want to know the whole backup job is done. There are two scenarios here: success and failure for individual workflow backups.\n\nLet's handle the completion path for the overall loop. From the \"Loop Over Items\" node, I'll create a second connection from its `main` output to a **Telegram1** node. This connection specifically handles the case when the \"Loop Over Items\" node has finished processing *all* its items. This Telegram node (`type`: `n8n-nodes-base.telegram`, `typeVersion`: 1.2) will send a completion message. The text, `\"=Backup n8n.psyii.od.ua/workflow Done {{ $execution.id }}\"` and potentially `\"=Backup has completed - {{ $('n8n').all().length }} workflows have been processed.\"` (from the disabled Gmail node) indicates success and gives a count of processed workflows, which is useful.\n\nNow, what if an individual workflow backup fails within the loop? We used `onError: \"continueErrorOutput\"` on the \"Execute Workflow\" node. This means it will output items that failed to its `error` output. So, from the \"Execute Workflow\" node's `error` output, I'll connect it to a **Telegram2** node. This Telegram node (`type`: `n8n-nodes-base.telegram`, `typeVersion`: 1.2) is configured to send a message indicating a failure for a specific workflow, using an expression like `\"=X: Failed to backup {{ $('Loop Over Items').item.json.id }}\"`. This gives immediate alerts for problems. It's currently `disabled: true`, meaning it won't send messages, but the setup for it is there.\n\nAdditionally, for error reporting, there's a **Gmail2** node (`type`: `n8n-nodes-base.gmail`, `typeVersion`: 2.1) connected from the \"Execute Workflow\" node's `error` output. This provides an alternative or additional notification channel for failures. Its `message` is set to `\"=X: Failed to backup {{ $('Loop Over Items').item.json.id }}\"`, similar to the Telegram error message. This ensures detailed failure reports via email.\n\nThere are also a couple of disabled Gmail nodes in the provided JSON:\n*   A **Gmail** node (`type`: `n8n-nodes-base.gmail`, `typeVersion`: 2.1) is present, configured to send a \"Backup Started\" email. It's connected from \"On clicking 'execute'\"'s `main` output, but it's currently disabled. This suggests it was considered as an alternative or additional initial notification method but then perhaps switched to Telegram for primary use.\n*   A **Gmail1** node (`type`: `n8n-nodes-base.gmail`, `typeVersion`: 2.1) is also disabled and configured to send a \"Backup has completed\" email. It would typically be connected after the \"Loop Over Items\" completes, similar to the \"Telegram1\" node. Its `message` expression correctly counts workflows using `$('n8n').all().length`.\n\nFinally, there's a **Sticky Note2** node (`type`: `n8n-nodes-base.stickyNote`, `typeVersion`: 1) which isn't functional. It's purely for documentation or visual organization within the workflow canvas, likely just containing a title like \"## Main workflow loop\". It helps to segment and label different parts of the workflow visually.", "hard", "{\n  \"name\": \"Backup Workflows\",\n  \"nodes\": [\n    {\n      \"parameters\": {\n        \"cronTime\": \"* 2 * * *\",\n        \"timezone\": \"\",\n        \"start\": false,\n        \"stop\": false,\n        \"inExpressions\": false\n      },\n      \"name\": \"Schedule\",\n      \"type\": \"n8n-nodes-base.cron\",\n      \"typeVersion\": 1,\n      \"position\": [\n        200,\n        300\n      ]\n    },\n    {\n      \"parameters\": {\n        \"url\": \"https://YOUR-N8N-INSTANCE/api/workflows\",\n        \"method\": \"GET\",\n        \"responseFormat\": \"json\",\n        \"simpleHttpRequestOptions\": {}\n      },\n      \"name\": \"Get Workflows\",\n      \"type\": \"n8n-nodes-base.httpRequest\",\n      \"typeVersion\": 1,\n      \"position\": [\n        400,\n        300\n      ]\n    },\n    {\n      \"parameters\": {\n        \"batchSize\": 1\n      },\n      \"name\": \"Loop Workflows\",\n      \"type\": \"n8n-nodes-base.splitInBatches\",\n      \"typeVersion\": 1,\n      \"position\": [\n        600,\n        300\n      ]\n    },\n    {\n      \"parameters\": {\n        \"url\": \"https://YOUR-N8N-INSTANCE/api/workflows/{{$json.id}}\",\n        \"method\": \"GET\",\n        \"responseFormat\": \"json\",\n        \"simpleHttpRequestOptions\": {}\n      },\n      \"name\": \"Get Workflow\",\n      \"type\": \"n8n-nodes-base.httpRequest\",\n      \"typeVersion\": 1,\n      \"position\": [\n        800,\n        300\n      ]\n    },\n    {\n      \"parameters\": {\n        \"functionCode\": \"items[0].json.data = items[0].json;\\nitems[0].binary = { data: Buffer.from(JSON.stringify(items[0].json, null, 2)) };\\nreturn items[0];\"\n      },\n      \"name\": \"Prepare Backup\",\n      \"type\": \"n8n-nodes-base.function\",\n      \"typeVersion\": 1,\n      \"position\": [\n        1000,\n        300\n      ]\n    },\n    {\n      \"parameters\": {\n        \"fileName\": \"workflow-backup-{{$json.id}}.json\",\n        \"binaryPropertyName\": \"data\",\n        \"destination\": \"/tmp\"\n      },\n      \"name\": \"Save Backup\",\n      \"type\": \"n8n-nodes-base.writeBinaryFile\",\n      \"typeVersion\": 1,\n      \"position\": [\n        1200,\n        300\n      ]\n    },\n    {\n      \"parameters\": {\n        \"chatId\": \"{{your_chat_id}}\",\n        \"message\": \"Workflow backup completed for workflow ID {{$json.id}}.\"\n      },\n      \"name\": \"Telegram Notification\",\n      \"type\": \"n8n-nodes-base.telegram\",\n      \"typeVersion\": 1,\n      \"position\": [\n        1400,\n        200\n      ]\n    },\n    {\n      \"parameters\": {\n        \"subject\": \"Workflow Backup Completed\",\n        \"text\": \"Backup has been created for workflow ID {{$json.id}}.\",\n        \"to\": \"{{<EMAIL>}}\",\n        \"from\": \"{{<EMAIL>}}\",\n        \"attachments\": [\n          {\n            \"filename\": \"workflow-backup-{{$json.id}}.json\",\n            \"content\": \"{{$binary.data.base64}}\",\n            \"encoding\": \"base64\"\n          }\n        ]\n      },\n      \"name\": \"Send Email\",\n      \"type\": \"n8n-nodes-base.gmail\",\n      \"typeVersion\": 1,\n      \"position\": [\n        1400,\n        400\n      ]\n    }\n  ],\n  \"connections\": {\n    \"Schedule\": {\n      \"main\": [\n        [\n          {\n            \"node\": \"Get Workflows\",\n            \"type\": \"main\",\n            \"index\": 0\n          }\n        ]\n      ]\n    },\n    \"Get Workflows\": {\n      \"main\": [\n        [\n          {\n            \"node\": \"Loop Workflows\",\n            \"type\": \"main\",\n            \"index\": 0\n          }\n        ]\n      ]\n    },\n    \"Loop Workflows\": {\n      \"main\": [\n        [\n          {\n            \"node\": \"Get Workflow\",\n            \"type\": \"main\",\n            \"index\": 0\n          }\n        ]\n      ]\n    },\n    \"Get Workflow\": {\n      \"main\": [\n        [\n          {\n            \"node\": \"Prepare Backup\",\n            \"type\": \"main\",\n            \"index\": 0\n          }\n        ]\n      ]\n    },\n    \"Prepare Backup\": {\n      \"main\": [\n        [\n          {\n            \"node\": \"Save Backup\",\n            \"type\": \"main\",\n            \"index\": 0\n          }\n        ]\n      ]\n    },\n    \"Save Backup\": {\n      \"main\": [\n        [\n          {\n            \"node\": \"Telegram Notification\",\n            \"type\": \"main\",\n            \"index\": 0\n          },\n          {\n            \"node\": \"Send Email\",\n            \"type\": \"main\",\n            \"index\": 0\n          }\n        ]\n      ]\n    }\n  },\n  \"active\": false\n}", "10", "1.0", "4.0", "1.0", "8.0", "8.0", "8.0", "8.0", "8.0", "7.0", "7.0", "7.0", "1.0", null, "100.0", "100.0", "100.0", "100.0", "100.0", "100.0"], ["1", "20", "Generate a JSON object for an inactive workflow named '+11 AI-Powered RAG Workflow For Stock Earnings Report Analysis', with `settings.executionOrder` set to 'v1', empty `nodes` and `connections`, and a `triggerCount` of 0.", "{\"name\": \"+11 AI-Powered RAG Workflow For Stock Earnings Report Analysis\", \"nodes\": [], \"connections\": {}, \"active\": false}", "Alright, let's get started on designing this workflow. Looking at the prompt and the provided JSON, it's clear we're dealing with a foundational structure rather than a fully built-out process. This workflow, named \"+11 AI-Powered RAG Workflow For Stock Earnings Report Analysis,\" is currently a blank canvas, ready for its logic to be added.\n\nThe very first thing I notice is that the workflow is marked as `active: false`. This is a critical starting point. It tells me that this workflow is not intended to run at the moment. It's either still under development, paused, or perhaps a template. As a designer, this means I don't need to worry about immediate execution or optimization for production performance; my focus is on establishing its basic identity and settings.\n\nNext, let's consider the workflow's identity. We have a `name` set to \"+11 AI-Powered RAG Workflow For Stock Earnings Report Analysis\". This is a very descriptive name, immediately conveying the workflow's high-level purpose: integrating AI for Retrieval Augmented Generation, specifically targeting stock earnings reports. Good naming is essential for organization, especially in a system with many workflows. The `id`, `versionId`, `createdAt`, and `updatedAt` fields are all system-generated and managed. They provide a unique identifier for this particular workflow instance and its version, along with timestamps for creation and last modification. I don't set these manually, but they're crucial for the n8n system to track and manage workflows internally.\n\nMoving on, the `settings` object currently only contains `executionOrder: \"v1\"`. This determines how the nodes within the workflow are processed. \"v1\" is typically the default or legacy execution order, which usually implies a top-down, sequential flow, or following explicit connections. For a workflow that has no nodes yet, this setting doesn't have an immediate functional impact, but it's good practice to have it defined. If the workflow were to become very complex with parallel branches, I might consider `v2` for more fine-grained control over execution paths, but `v1` is perfectly suitable for a simple or new workflow.\n\nNow, we get to the core components of any n8n workflow: `nodes` and `connections`. The JSON explicitly shows `nodes: []` and `connections: {}`. This means there are absolutely no nodes or connections defined within this workflow yet. It's truly an empty shell.\n\nIf I were to start building out this \"AI-Powered RAG Workflow For Stock Earnings Report Analysis,\" my mental process would immediately jump to what nodes *would* be needed, even though they aren't present in the current JSON:\n\n1.  **Trigger Node**: Every workflow needs a starting point. Given the name, a `Cron` node (type: `n8n-nodes-base.cron`, typeVersion: `1.0`) might be suitable for scheduling regular checks for new earnings reports. Alternatively, an `HTTP Request` node (type: `n8n-nodes-base.httpRequest`, typeVersion: `3.0`) might act as a webhook if another system pushes reports, or even an `IMAP` node (type: `n8n-nodes-base.imap`, typeVersion: `2.0`) if reports arrive via email. The specific choice would depend on how the earnings reports are sourced.\n\n2.  **Data Ingestion/Retrieval**: Once triggered, I'd need to get the earnings report data. If it's a PDF, I'd consider an `HTTP Request` node to download it from a URL, or a `Read Binary File` node (type: `n8n-nodes-base.readBinaryFile`, typeVersion: `1.0`) if the files are stored locally.\n\n3.  **Document Processing**: Earnings reports are likely in PDF or similar formats. To use them for RAG, I'd need to extract the text. A `Convert Document` node (type: `n8n-nodes-base.convertDocument`, typeVersion: `1.0`) could be used, or a custom `Code` node (type: `n8n-nodes-base.code`, typeVersion: `1.0`) if specialized parsing is required.\n\n4.  **Embedding and Vectorization**: For RAG, the extracted text needs to be converted into numerical vectors (embeddings). An `OpenAI` node (type: `n8n-nodes-base.openAi`, typeVersion: `1.0` or `2.0` depending on features like batching) with an \"Embeddings\" operation, or a `Cohere` node (type: `n8n-nodes-base.cohere`, typeVersion: `1.0`) would be my choice. This node would take the text chunks and output their vector representations.\n\n5.  **Vector Database Interaction**: These embeddings need to be stored and retrieved efficiently. I'd connect the embedding output to a vector database node, such as `ChromaDB` (type: `n8n-nodes-base.chromaDb`, typeVersion: `1.0`), `Pinecone` (type: `n8n-nodes-base.pinecone`, typeVersion: `1.0`), or `Weaviate` (type: `n8n-nodes-base.weaviate`, typeVersion: `1.0`). The operation here would initially be \"Upsert\" to store the earnings report data, and later \"Query\" or \"Search\" to retrieve relevant information based on a user's question.\n\n6.  **AI Model (LLM) for RAG**: Once relevant chunks are retrieved from the vector database, they need to be fed into a large language model along with the user's query. An `OpenAI Chat` node (type: `n8n-nodes-base.openAiChat`, typeVersion: `2.0`) would be ideal for this, using the \"Chat\" operation. The prompt would be carefully constructed to include the retrieved context.\n\n7.  **Output/Action Node**: Finally, what happens with the generated insights? Perhaps a `Slack` node (type: `n8n-nodes-base.slack`, typeVersion: `2.0`) to post summaries, an `Email Send` node (type: `n8n-nodes-base.emailSend`, typeVersion: `1.0`) to notify stakeholders, or an `HTTP Request` node to update an internal system.\n\nHowever, as per the provided JSON, none of these hypothetical nodes or connections exist. The `nodes` array is empty, and consequently, the `connections` object is also empty. This is the state we're representing: a workflow placeholder.\n\nLastly, `triggerCount: 0` reinforces the inactive and empty state. It indicates that this workflow has not been successfully triggered or executed yet. `staticData`, `meta`, `pinData`, and `tags` are also empty or null, which is expected for a workflow in this initial state, as they are used for optional additional data, UI organization, or categorization once the workflow is more developed.\n\nSo, essentially, what we're defining here is the metadata for a *future* complex workflow, currently sitting as an empty canvas, waiting for its AI-powered RAG logic to be meticulously designed and implemented.", "easy", "{\n  \"name\": \"+11 AI-Powered RAG Workflow For Stock Earnings Report Analysis\",\n  \"nodes\": [],\n  \"connections\": {},\n  \"active\": false,\n  \"settings\": {\n    \"executionOrder\": \"v1\"\n  },\n  \"triggerCount\": 0\n}", "10", "1.0", "4.0", "1.0", "0.0", "0.0", "0.0", "0.0", "0.0", "0.0", "0.0", "0.0", "1.0", null, null, null, null, null, null, null], ["2", "12", "Generate a JSON object for an inactive workflow named 'CRAWL dataa QUAN LEN', with `createdAt` and `updatedAt` timestamps of '2025-03-28T03:54:16.496Z', `triggerCount` 0, and `settings.executionOrder` 'v1'. Ensure `meta` and `staticData` are null, `connections`, `nodes`, `pinData`, and `tags` are empty, and provide placeholder `id` and `versionId` strings.", "{\"name\": \"CRAWL dataa QUAN LEN\", \"nodes\": [], \"connections\": {}, \"active\": false}", "Okay, let's start building this workflow definition from the ground up, thinking about each piece as we lay it down.\n\nThe very first thing I need to consider is the overall state of this workflow. The prompt explicitly says it's for an \"inactive workflow\". So, right at the top level, I'll set the `active` property to `false`. This ensures that even if it were complete and had nodes, it wouldn't run until someone manually toggles it on.\n\nNext, let's get the identifying details in place. The prompt gives me a clear `name`: \"CRAWL dataa QUAN LEN\". This is straightforward; it's just a string that will appear in the workflow list.\n\nThen, I see `createdAt` and `updatedAt` timestamps. Both are specified as '2025-03-28T03:54:16.496Z'. This tells me that this workflow is being defined at a specific moment, and since both timestamps are the same, it implies it's a fresh definition that hasn't been modified since its initial creation. I'll place these exact timestamp strings.\n\nThe request also asks for placeholder `id` and `versionId`. These are critical for the system to uniquely identify this particular workflow instance and its specific version. For the `id`, I'll just use a generic placeholder string, something like \"f4lL0OCunm8opuxQ\" which looks like a typical short ID. For the `versionId`, a UUID format is common, so a placeholder like \"8dee12d6-4b1b-476e-8951-b6bcd12190d0\" fits perfectly. These aren't user-specific values, just system-generated unique identifiers.\n\nMoving on, the prompt states `triggerCount` should be 0. This makes perfect sense for a new, inactive workflow; it hasn't had any successful triggers yet. So, I'll set `triggerCount` to `0`.\n\nNow, let's consider the content of the workflow itself. The prompt asks that `meta` and `staticData` are `null`. These fields are for storing additional, less structural metadata or persistent data that the workflow might need, but for many workflows, they aren't necessary. Setting them to `null` indicates there's no specific meta-information or static state defined for this workflow at this point.\n\nThen, it specifies `connections`, `nodes`, `pinData`, and `tags` must be empty.\n*   `connections`: This is an object that defines how data flows between nodes. Since the workflow is being built as conceptually \"empty\" – meaning no operational steps or `nodes` – there won't be any connections. So, an empty object `{}` is the correct representation.\n*   `nodes`: This is the core of any workflow, where all the individual processing steps are defined. The prompt implies an empty workflow by not specifying any nodes. Therefore, an empty array `[]` is exactly what I need here. This means the workflow doesn't perform any actions or operations currently. If I were designing a functional workflow, this is where I'd start adding a 'Start' node, maybe a 'HTTP Request' node, or a 'Set' node, and so on, but for this particular request, it remains empty.\n*   `pinData`: This is often related to user interface preferences, like which items are \"pinned\" in the editor view. For a programmatic generation, an empty object `{}` is the appropriate default.\n*   `tags`: These are for categorization, like \"CRM\", \"Marketing\", \"Scheduled\". The prompt doesn't provide any, so an empty array `[]` is correct.\n\nFinally, I need to address the `settings`. The prompt specifically requests `settings.executionOrder` to be 'v1'. This property dictates how the workflow engine processes the nodes, particularly relevant for complex flows with branches. 'v1' is a common default or a specific version of the execution logic. So, I'll create a `settings` object and place `executionOrder: \"v1\"` inside it. This is a crucial, workflow-wide configuration.\n\nAnd with that, I've covered all the requirements for this workflow definition. It's a complete, albeit empty and inactive, workflow structure.", "easy", "{\n  \"id\": \"placeholder-id\",\n  \"versionId\": \"placeholder-versionId\",\n  \"name\": \"CRAWL dataa QUAN LEN\",\n  \"nodes\": [],\n  \"connections\": {},\n  \"createdAt\": \"2025-03-28T03:54:16.496Z\",\n  \"updatedAt\": \"2025-03-28T03:54:16.496Z\",\n  \"triggerCount\": 0,\n  \"settings\": {\n    \"executionOrder\": \"v1\"\n  },\n  \"meta\": null,\n  \"staticData\": null,\n  \"pinData\": {},\n  \"tags\": [],\n  \"active\": false\n}", "10", "1.0", "4.0", "1.0", "0.0", "0.0", "0.0", "0.0", "0.0", "0.0", "0.0", "0.0", "1.0", null, null, null, null, null, null, null], ["3", "41", "Generate an n8n workflow that provides daily stock trade recommendations, scheduled to run every weekday morning at 9:45 AM. It should use an Anthropic AI agent to analyze specific assets like Microsoft, Meta, Nvidia, Schwab Dividend ETF, Vanguard SP500, and Vanguard Total Market, leveraging specialized NASDAQ, NYSE, and NYSE Arca stock analysis tools, along with news sources, and then deliver the recommendations via email.", "{\"name\": \"Trade Recommendations\", \"nodes\": [{\"parameters\": {\"rule\": {\"interval\": [{\"triggerAtHour\": 9, \"triggerAtMinute\": 45}]}}, \"name\": \"Schedule Trigger\", \"type\": \"n8n-nodes-base.scheduleTrigger\", \"typeVersion\": 1.2, \"position\": [-480, -40]}, {\"parameters\": {\"promptType\": \"define\", \"text\": \"={{ $json.Microsoft }}{{ $json.Meta }}{{ $json.Nvidia }}{{ $json['Schwab Dividend ETF'] }}{{ $json['Vanguard SP500'] }}{{ $json['Vanguard Total Market'] }}\", \"options\": {\"systemMessage\": \"# Stock Trading Analysis Agent\\n\\nYou are an advanced AI agent specialized in stock market analysis. Your task is to analyze a given table of exchange/stock pairs and provide buy, sell, or hold recommendations based on current trading day data and news analysis.\\n\\n## Your Capabilities\\n\\nYou have access to the following tools:\\n\\n1. **News Analysis Tools**:\\n   - Wiki News headlines scraper\\n   - CNN news headlines API\\n   - NewsAPI for comprehensive headline coverage\\n\\n2. **Stock Analysis Tools**:\\n   - NYSE Stock Analysis Tool (for NYSE-listed stocks)\\n   - NASDAQ Stock Analysis Tool (for NASDAQ-listed stocks)\\n   - NYSE Arca Stock Analysis Tool (for NYSE Arac listed ETFs)\\n\\n## Input Format\\n\\nYou will receive a table of exchange/stock pairs in the following format:\\n\\n```\\nExchange | Stock Symbol\\n---------|-------------\\nNYSE     | DIS         \\nNASDAQ   | AAPL        \\n```\\n\\n## Your Task Workflow\\n\\nFor each stock in the input table, perform the following analysis steps:\\n\\n1. **Identify Exchange and Use Appropriate Tool**:\\n   - For NYSE stocks, use the NYSE Stock Analysis Tool\\n   - For NASDAQ stocks, use the NASDAQ Stock Analysis Tool\\n   - For NYSE Arca ETFs, use the NYSE Arca Stock Analysis Tool\\n\\n2. **Gather Current Trading Data**:\\n   - Retrieve current stock price\\n   - Get day's high and low\\n   - Calculate day's trading volume compared to 30-day average\\n   - Identify key technical indicators (50-day and 200-day moving averages, RSI, MACD)\\n   - Note any unusual trading patterns or volume spikes\\n\\n3. **News Sentiment Analysis**:\\n   - Search for company-specific news across all news sources\\n   - Search for industry-related news\\n   - Search for market-wide news that might impact the stock\\n   - Analyze sentiment (positive, negative, neutral) of headlines\\n   - Identify any breaking news that could significantly impact trading\\n\\n4. **Fundamental Analysis**:\\n   - Check for recent earnings reports or upcoming earnings dates\\n   - Review recent analyst ratings changes\\n   - Check for dividend announcements\\n   - Review P/E ratio compared to industry average\\n\\n5. **Generate Recommendation**:\\n   - Synthesize all data points into a BUY, SELL, or HOLD recommendation\\n   - Provide confidence level (HIGH, MEDIUM, LOW)\\n   - List top 3 factors influencing your decision\\n\\n## Output Format\\n\\nFor each stock, provide your analysis in the following format:\\n\\n```\\nANALYSIS FOR: [STOCK SYMBOL] ([COMPANY NAME])\\nEXCHANGE: [EXCHANGE]\\nCURRENT PRICE: $[PRICE] ([PERCENTAGE CHANGE])\\nTRADING VOLUME: [VOLUME] ([PERCENTAGE VS 30-DAY AVG])\\nKEY TECHNICALS: [SUMMARY OF TECHNICAL INDICATORS]\\nNEWS SENTIMENT: [POSITIVE/NEGATIVE/NEUTRAL] - [BRIEF EXPLANATION]\\nFUNDAMENTAL OUTLOOK: [BRIEF SUMMARY]\\nRECOMMENDATION: [BUY/SELL/HOLD] (CONFIDENCE: [HIGH/MEDIUM/LOW])\\nPRIMARY FACTORS:\\n1. [FACTOR 1]\\n2. [FACTOR 2]\\n3. [FACTOR 3]\\nADDITIONAL NOTES: [ANY SPECIAL CONSIDERATIONS]\\n```\\n\\n## Additional Instructions\\n\\n1. **Time Sensitivity**: Prioritize breaking news and unusual trading patterns that have emerged during the current trading day.\\n\\n2. **Contextual Analysis**: Consider broader market trends - is this a sector-wide movement or stock-specific?\\n\\n3. **Risk Assessment**: Include any notable risk factors that could impact your recommendation.\\n\\n4. **Technical Analysis Weight**: For volatile stocks, weight technical indicators more heavily. For stable blue-chip stocks, weight fundamentals more heavily.\\n\\n5. **Confirmation Patterns**: Look for confirmation across multiple data sources before making high-confidence recommendations.\\n\\n6. **Contrary Indicators**: Explicitly note when you find conflicting signals and explain your reasoning for your final recommendation.\\n\\n7. **Transparency**: Always list the specific data points that led to your recommendation.\\n\\n## Example Process\\n\\n1. Receive stock: NYSE | KO | Coca-Cola Company\\n2. Use NYSE Stock Analysis Tool to gather trading data\\n3. Scan all news sources for \\\"Coca-Cola\\\" or \\\"KO\\\" related headlines\\n4. Analyze beverage industry news for relevant context\\n5. Check for earnings reports or analyst actions\\n6. Synthesize data to form recommendation\\n7. Format output according to the specification\\n\\nAlways provide reasoning that would be valuable to both novice and experienced investors. Focus on actionable insights rather than just data reporting.\"}}, \"name\": \"AI Agent\", \"type\": \"@n8n/n8n-nodes-langchain.agent\", \"typeVersion\": 1.7, \"position\": [-80, -40]}, {\"parameters\": {\"model\": \"claude-3-5-sonnet-20241022\", \"options\": {}}, \"name\": \"Anthropic Chat Model\", \"type\": \"@n8n/n8n-nodes-langchain.lmChatAnthropic\", \"typeVersion\": 1.2, \"position\": [-160, 180]}, {\"parameters\": {\"name\": \"NASDAQ\", \"description\": \"The NASDAQ Stock Analysis Tool provides comprehensive financial data analysis capabilities for stocks traded on the NASDAQ exchange. This tool enables automated workflows to gather, analyze, and visualize stock market data, helping users make informed investment decisions.\", \"workflowId\": {\"__rl\": true, \"value\": \"dsJL3YGPqIm5nDDS\", \"mode\": \"list\", \"cachedResultName\": \"Technical Stock Analyst v2 NASDAQ\"}}, \"name\": \"NASDAQ Stock Analysis Tool\", \"type\": \"@n8n/n8n-nodes-langchain.toolWorkflow\", \"typeVersion\": 1.3, \"position\": [400, 180]}, {\"parameters\": {\"name\": \"NYSE\", \"description\": \"The NYSE Stock Analysis Tool provides comprehensive financial data analysis capabilities for stocks traded on the NYSE exchange. This tool enables automated workflows to gather, analyze, and visualize stock market data, helping users make informed investment decisions.\", \"workflowId\": {\"__rl\": true, \"value\": \"EZGUZkOScAYwc2lH\", \"mode\": \"list\", \"cachedResultName\": \"Technical Stock Analyst v2 NYSE\"}}, \"name\": \"NYSE Stock Analysis Tool\", \"type\": \"@n8n/n8n-nodes-langchain.toolWorkflow\", \"typeVersion\": 1.3, \"position\": [400, 340]}, {\"parameters\": {\"chatId\": \"XXXXXXXXXXXXXXXXXXXXX\", \"text\": \"={{ $json.output }}\", \"additionalFields\": {}}, \"name\": \"Telegram\", \"type\": \"n8n-nodes-base.telegram\", \"typeVersion\": 1.2, \"position\": [400, -180]}, {\"parameters\": {\"assignments\": {\"assignments\": [{\"id\": \"a98b6a16-ba95-49a3-9106-8188fff5ec63\", \"name\": \"Microsoft\", \"value\": \"NASDAQ:MSFT\", \"type\": \"string\"}, {\"id\": \"c659d8e3-de8e-47c6-9ebb-45f233643e9c\", \"name\": \"Meta\", \"value\": \"NASDAQ:META\", \"type\": \"string\"}, {\"id\": \"5d1d5b90-e99d-438f-8691-88cbff52ab9c\", \"name\": \"Nvidia\", \"value\": \"NASDAQ:NVDA\", \"type\": \"string\"}, {\"id\": \"973d6861-440e-4db2-9e25-3494d2634f4e\", \"name\": \"Schwab Dividend ETF\", \"value\": \"AMEX:SCHD\", \"type\": \"string\"}, {\"id\": \"c43ce796-418e-408d-a238-ed592b471479\", \"name\": \"Vanguard SP500\", \"value\": \"AMEX:VOO\", \"type\": \"string\"}, {\"id\": \"cf0c26b7-0371-431e-a0d5-b15688a543b7\", \"name\": \"Vanguard Total Market\", \"value\": \"AMEX:VTI\", \"type\": \"string\"}]}, \"options\": {}}, \"name\": \"Edit Fields\", \"type\": \"n8n-nodes-base.set\", \"typeVersion\": 3.4, \"position\": [-280, -40]}, {\"parameters\": {\"name\": \"NewsAPI\", \"description\": \"Call this tool to use the NewsAPI to gather the latest news headlines\", \"workflowId\": {\"__rl\": true, \"value\": \"73n2334x1LFCBDFf\", \"mode\": \"list\", \"cachedResultName\": \"NewsAPI\"}}, \"name\": \"NewsAPI Tool\", \"type\": \"@n8n/n8n-nodes-langchain.toolWorkflow\", \"typeVersion\": 1.3, \"position\": [260, 260]}, {\"parameters\": {\"toolDescription\": \"Use this tool to get current news headlines from Wiki News\", \"url\": \"https://en.m.wikipedia.org/wiki/Portal:Current_events\"}, \"name\": \"Wiki Current Events\", \"type\": \"@n8n/n8n-nodes-langchain.toolHttpRequest\", \"typeVersion\": 1.1, \"position\": [120, 260]}, {\"parameters\": {\"toolDescription\": \"Get current headlines from CNN\", \"url\": \"https://lite.cnn.com/\"}, \"name\": \"CNN Headlines\", \"type\": \"@n8n/n8n-nodes-langchain.toolHttpRequest\", \"typeVersion\": 1.1, \"position\": [-20, 260]}, {\"parameters\": {\"sendTo\": \"XXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX\", \"subject\": \"=Stock Analysis:  {{ $('Schedule Trigger').item.json['Readable date'] }}\", \"emailType\": \"text\", \"message\": \"={{ $json.output }}\", \"options\": {}}, \"name\": \"Gmail\", \"type\": \"n8n-nodes-base.gmail\", \"typeVersion\": 2.1, \"position\": [400, -20]}, {\"parameters\": {\"name\": \"Arca\", \"description\": \"The NYSE Arca Stock Analysis Tool provides comprehensive financial data analysis capabilities for stocks traded on the NYSE Arca exchange. This tool enables automated workflows to gather, analyze, and visualize stock market data, helping users make informed investment decisions.\", \"workflowId\": {\"__rl\": true, \"value\": \"DQg8ZkQahZKli48m\", \"mode\": \"list\", \"cachedResultName\": \"Technical Stock Analyst v2 NYSE Arca\"}}, \"name\": \"NYSE Arca Stock Analysis Tool\", \"type\": \"@n8n/n8n-nodes-langchain.toolWorkflow\", \"typeVersion\": 1.3, \"position\": [540, 260]}], \"connections\": {\"Schedule Trigger\": {\"main\": [[{\"node\": \"Edit Fields\", \"type\": \"main\", \"index\": 0}]]}, \"Anthropic Chat Model\": {\"ai_languageModel\": [[{\"node\": \"AI Agent\", \"type\": \"ai_languageModel\", \"index\": 0}]]}, \"NASDAQ Stock Analysis Tool\": {\"ai_tool\": [[{\"node\": \"AI Agent\", \"type\": \"ai_tool\", \"index\": 0}]]}, \"NYSE Stock Analysis Tool\": {\"ai_tool\": [[{\"node\": \"AI Agent\", \"type\": \"ai_tool\", \"index\": 0}]]}, \"AI Agent\": {\"main\": [[{\"node\": \"Telegram\", \"type\": \"main\", \"index\": 0}, {\"node\": \"Gmail\", \"type\": \"main\", \"index\": 0}]]}, \"Edit Fields\": {\"main\": [[{\"node\": \"AI Agent\", \"type\": \"main\", \"index\": 0}]]}, \"NewsAPI Tool\": {\"ai_tool\": [[{\"node\": \"AI Agent\", \"type\": \"ai_tool\", \"index\": 0}]]}, \"Wiki Current Events\": {\"ai_tool\": [[{\"node\": \"AI Agent\", \"type\": \"ai_tool\", \"index\": 0}]]}, \"CNN Headlines\": {\"ai_tool\": [[{\"node\": \"AI Agent\", \"type\": \"ai_tool\", \"index\": 0}]]}, \"NYSE Arca Stock Analysis Tool\": {\"ai_tool\": [[{\"node\": \"AI Agent\", \"type\": \"ai_tool\", \"index\": 0}]]}}, \"active\": false}", "Alright, let's start designing this workflow from scratch, imagining I have this user prompt in front of me: \"Generate an n8n workflow that provides daily stock trade recommendations, scheduled to run every weekday morning at 9:45 AM. It should use an Anthropic AI agent to analyze specific assets like Microsoft, Meta, Nvidia, Schwab Dividend ETF, Vanguard SP500, and Vanguard Total Market, leveraging specialized NASDAQ, NYSE, and NYSE Arca stock analysis tools, along with news sources, and then deliver the recommendations via email.\"\n\nFirst things first, the user wants this workflow to run *daily* at a specific time. That immediately tells me I need a trigger that handles scheduling. So, I'd start by adding a **Schedule Trigger** node. This is a core n8n trigger, and looking at the JSON, it's `n8n-nodes-base.scheduleTrigger` with `typeVersion: 1.2`. This is perfect for setting up a recurring execution.\n\nNow, how to configure it? The prompt says \"every weekday morning at 9:45 AM.\" So, in the Schedule Trigger's parameters, I'd set the rule to trigger at 9 AM and 45 minutes. The JSON shows `{\"rule\": {\"interval\": [{\"triggerAtHour\": 9, \"triggerAtMinute\": 45}]}}`. This takes care of the exact time. To ensure it runs on weekdays, I'd typically also set the \"Weekdays\" option in the UI, but the JSON only explicitly states the hour and minute. Assuming the default behavior for a daily trigger respects weekday settings if configured.\n\nOkay, so we have the trigger. What's next? The AI agent needs to know *what* assets to analyze. The prompt lists specific stocks and ETFs: Microsoft, Meta, Nvidia, Schwab Dividend ETF, Vanguard SP500, and Vanguard Total Market. Instead of hardcoding these directly into the AI prompt, which can get messy, it's better to structure them as input for the agent. For this, a **Set** node is ideal. It's `n8n-nodes-base.set` with `typeVersion: 3.4`. This node allows me to define specific fields with fixed values that can then be passed to subsequent nodes.\n\nI'd connect the `Schedule Trigger`'s `main` output to the `Set` node's `main` input. This ensures that every time the schedule triggers, it first populates these asset details.\nInside the Set node, I'd configure assignments for each asset. For example, `Microsoft` would be `NASDAQ:MSFT`, `Meta` as `NASDAQ:META`, `Nvidia` as `NASDAQ:NVDA`, and the ETFs like `Schwab Dividend ETF` as `AMEX:SCHD`, `Vanguard SP500` as `AMEX:VOO`, and `Vanguard Total Market` as `AMEX:VTI`. This provides a clear, structured input to the AI agent, including the exchange, which is crucial for tool selection. The JSON reflects this perfectly in `parameters.assignments`.\n\nNow we get to the core of the workflow: the AI analysis. The prompt explicitly asks for an \"Anthropic AI agent to analyze... leveraging specialized NASDAQ, NYSE, and NYSE Arca stock analysis tools, along with news sources.\" This screams for an **AI Agent** node. The JSON uses `@n8n/n8n-nodes-langchain.agent` with `typeVersion: 1.7`. This is the right choice because it allows us to define an AI persona, provide a prompt, and crucially, give it access to tools.\n\nI'd connect the `Set` node's `main` output to the `AI Agent` node's `main` input. The AI Agent will then receive the structured asset information (Microsoft, Meta, etc.) from the Set node.\n\nWithin the AI Agent, the `promptType` is set to `define`, meaning we're giving it a specific instruction. The `text` parameter is crucial: `={{ $json.Microsoft }}{{ $json.Meta }}{{ $json.Nvidia }}{{ $json['Schwab Dividend ETF'] }}{{ $json['Vanguard SP500'] }}{{ $json['Vanguard Total Market'] }}`. This is how the AI Agent receives the list of assets from the previous `Set` node. It concatenates them into a single string.\nThe `systemMessage` is where the AI's persona and instructions are defined. This is a very detailed system message, acting as a complete guide for the AI. It outlines:\n*   Its role: \"Stock Trading Analysis Agent.\"\n*   Its capabilities: Lists the News Analysis Tools (Wiki News, CNN, NewsAPI) and Stock Analysis Tools (NYSE, NASDAQ, NYSE Arca). This is important because the agent needs to know what tools it has at its disposal.\n*   Input format: Specifies how the exchange and stock symbol will be provided.\n*   Task Workflow: This is critical, outlining the step-by-step process the agent should follow for each stock: identify exchange and use appropriate tool, gather current trading data, news sentiment analysis, fundamental analysis, and finally, generate a recommendation.\n*   Output Format: This ensures the recommendations are structured and consistent, which is great for parsing later or just for readability in an email.\n*   Additional Instructions: Provides nuances like time sensitivity, contextual analysis, risk assessment, and technical/fundamental analysis weighting.\nThis comprehensive system message is key to getting relevant and well-formatted recommendations from the AI.\n\nFor the AI Agent to *actually* work, it needs an underlying language model. The prompt specified \"Anthropic AI agent,\" so naturally, I'd add an **Anthropic Chat Model** node. This is `@n8n/n8n-nodes-langchain.lmChatAnthropic` with `typeVersion: 1.2`. It's configured to use `claude-3-5-sonnet-20241022`, which is a good, capable model for this kind of analytical task. I'd connect the `Anthropic Chat Model` node's `ai_languageModel` output to the `AI Agent` node's corresponding `ai_languageModel` input. An Anthropic API credential would be configured here, but its specific ID would be user-specific.\n\nNext, let's consider the tools the AI Agent needs. The prompt specifies NASDAQ, NYSE, and NYSE Arca stock analysis tools, plus news sources. These are represented by **Workflow Tool** nodes and **HTTP Request Tool** nodes.\n\nFirst, the **NASDAQ Stock Analysis Tool**. This is `@n8n/n8n-nodes-langchain.toolWorkflow` with `typeVersion: 1.3`. It's configured with a name \"NASDAQ\" and a description explaining its purpose: \"The NASDAQ Stock Analysis Tool provides comprehensive financial data analysis capabilities...\" Most importantly, it links to another n8n workflow by its `workflowId`. This means there's a separate, likely more complex, workflow that *actually* performs the NASDAQ stock analysis. I'd connect this node's `ai_tool` output to the `AI Agent` node's `ai_tool` input.\n\nSimilarly, I'd add a **NYSE Stock Analysis Tool**, also `@n8n/n8n-nodes-langchain.toolWorkflow` (`typeVersion: 1.3`). It has a similar configuration, name \"NYSE\", description, and links to a specific workflow for NYSE analysis via its `workflowId`. This also connects to the `AI Agent`'s `ai_tool` input.\n\nAnd for ETFs listed on NYSE Arca, an **NYSE Arca Stock Analysis Tool** is needed, also a `toolWorkflow` (`typeVersion: 1.3`). Its configuration, \"Arca\", description, and `workflowId` would be specific to NYSE Arca analysis. This too connects to the `AI Agent`'s `ai_tool` input.\n\nFor news sources, the prompt mentions \"news sources,\" and the system message within the AI Agent specified \"Wiki News headlines scraper,\" \"CNN news headlines API,\" and \"NewsAPI.\"\nSo, I'd include:\n*   A **NewsAPI Tool** (`@n8n/n8n-nodes-langchain.toolWorkflow`, `typeVersion: 1.3`). This is also a workflow tool, suggesting a separate workflow handles the NewsAPI integration. It's named \"NewsAPI\" and has a description. This connects as an `ai_tool` to the AI Agent. *Interestingly, in the provided JSON, this node is disabled (`\"disabled\": true`). I'd note that this means the AI agent won't actually be able to use this tool, even though it's mentioned in its system message. This could be an oversight or intentional during development.*\n*   A **Wiki Current Events** node (`@n8n/n8n-nodes-langchain.toolHttpRequest`, `typeVersion: 1.1`). This is an `httpRequestTool`, meaning it makes a direct HTTP request to a URL to fetch data. It's configured with a `toolDescription` and `url` pointing to Wikipedia's current events page. This also connects as an `ai_tool` to the AI Agent. *Again, this node is disabled in the JSON, meaning the AI won't use it.*\n*   A **CNN Headlines** node (`@n8n/n8n-nodes-langchain.toolHttpRequest`, `typeVersion: 1.1`). Similar to the Wiki tool, it's an `httpRequestTool` with a `toolDescription` and `url` for CNN. This also connects as an `ai_tool` to the AI Agent. *And yes, this one is also disabled.*\n\nIt's important to realize that even though the AI's system message describes these news tools, if the actual tool nodes are disabled in the workflow, the AI won't be able to call them. This is a critical detail in the workflow's actual execution. It means the AI would rely solely on the stock analysis tools to inform its recommendations, or perhaps struggle if it expects news access but can't get it. For a production workflow, I would definitely enable these if news sentiment is truly desired.\n\nFinally, how does the user get the recommendations? The prompt says \"deliver the recommendations via email.\" So, an **Gmail** node is the obvious choice. It's `n8n-nodes-base.gmail` with `typeVersion: 2.1`.\n\nI'd connect the `AI Agent`'s `main` output to the `Gmail` node's `main` input. The AI Agent will output the analysis and recommendations as a string (based on the `Output Format` in its system message), which will be available in `$json.output`.\nIn the Gmail node's parameters:\n*   `sendTo`: This would be the recipient's email address. It's a sensitive field, so I'd just note that the user's email would go here.\n*   `subject`: This is dynamically set: `=Stock Analysis: {{ $('Schedule Trigger').item.json['Readable date'] }}`. This is a smart move, as it makes the email subject line informative, including the date the analysis was run, pulling it from the initial `Schedule Trigger` node's output.\n*   `message`: This would be `={{ $json.output }}`, pulling the AI Agent's generated recommendation text.\n*   An OAuth2 credential for Gmail would be configured here.\n\nI also see a **Telegram** node (`n8n-nodes-base.telegram`, `typeVersion: 1.2`). This node is also connected from the `AI Agent`'s `main` output, sending `={{ $json.output }}`. This would allow sending the recommendations to a Telegram chat, which is a nice alternative or addition to email. However, in the provided JSON, this node is `disabled: true`. So, while it's present, it won't execute. This might have been for testing or an alternative delivery method that was later decided against or put on hold. If it were enabled, it would require a Telegram API credential and a specific `chatId`.\n\nSo, the overall flow: `Schedule Trigger` kicks off daily -> `Set` defines assets -> `AI Agent` receives assets, uses `Anthropic Chat Model` and various `Tool Workflow` and `HTTP Request Tool` nodes (some disabled) to perform analysis -> `AI Agent` outputs recommendations -> `Gmail` sends the recommendations via email. The disabled `Telegram` node shows a potential for multi-channel delivery.\n\nThis structure seems very robust for the given requirements. The use of separate workflow tools allows for modularity and reusability of the stock analysis logic, which is excellent design. The detailed system message for the AI is also a strong point, guiding its behavior effectively.Alright, let's start designing this workflow from the ground up, based on the user's request. The primary goal is to provide daily stock trade recommendations via email, using an AI agent and specific financial tools.\n\nFirst, the workflow needs to run \"every weekday morning at 9:45 AM.\" This immediately tells me I need a **Schedule Trigger** node. This is a fundamental n8n trigger, specifically `n8n-nodes-base.scheduleTrigger` with `typeVersion: 1.2`. It's perfect for setting up recurring executions. I'd configure its `rule` parameter to `{\"interval\": [{\"triggerAtHour\": 9, \"triggerAtMinute\": 45}]}`. This sets the precise time for the workflow to activate each day.\n\nNext, the AI agent needs to know *which* specific assets to analyze: Microsoft, Meta, Nvidia, Schwab Dividend ETF, Vanguard SP500, and Vanguard Total Market. Instead of cluttering the AI prompt directly, it's much cleaner and more manageable to define these inputs upfront. So, I'll add a **Set** node. The JSON indicates this is `n8n-nodes-base.set` with `typeVersion: 3.4`. This node allows us to define custom fields and values.\n\nI'd connect the `Schedule Trigger`'s `main` output to the `Set` node's `main` input. This way, when the schedule triggers, the workflow proceeds to define the assets.\nInside the `Set` node, I'd create a series of assignments. For each asset, I'd define a name (e.g., \"Microsoft\") and its corresponding stock symbol along with its primary exchange (e.g., \"NASDAQ:MSFT\"). This ensures the AI agent gets clear, structured input. So, `Microsoft` would be `NASDAQ:MSFT`, `Meta` as `NASDAQ:META`, `Nvidia` as `NASDAQ:NVDA`. For the ETFs, knowing their exchange is key for the correct tool, so `Schwab Dividend ETF` as `AMEX:SCHD`, `Vanguard SP500` as `AMEX:VOO`, and `Vanguard Total Market` as `AMEX:VTI`. The `Set` node's configuration in the JSON reflects these exact assignments, which is great.\n\nNow we get to the intelligence part: the \"Anthropic AI agent to analyze.\" This points directly to the **AI Agent** node. In the JSON, it's `@n8n/n8n-nodes-langchain.agent` with `typeVersion: 1.7`. This node is designed to orchestrate complex AI tasks, allowing an AI model to use tools.\n\nI'd connect the `Set` node's `main` output to the `AI Agent` node's `main` input. The `AI Agent` will then receive the structured list of assets.\nInside the `AI Agent` node, the `promptType` is `define`, meaning we're providing a custom prompt. The `text` field `={{ $json.Microsoft }}{{ $json.Meta }}{{ $json.Nvidia }}{{ $json['Schwab Dividend ETF'] }}{{ $json['Vanguard SP500'] }}{{ $json['Vanguard Total Market'] }}` dynamically pulls the asset symbols from the previous `Set` node, concatenating them.\nThe most critical part here is the `systemMessage`. This is a detailed instruction set for the AI. It defines:\n*   Its persona: \"Stock Trading Analysis Agent.\"\n*   Its capabilities: It lists all the tools available to it, specifically \"News Analysis Tools\" (Wiki News, CNN, NewsAPI) and \"Stock Analysis Tools\" (NYSE, NASDAQ, NYSE Arca). This is crucial because the agent needs to know what functions it can call upon.\n*   Input format: How the exchange and stock symbol pairs will be presented.\n*   A clear \"Task Workflow\": This outlines the logical steps the AI should follow for *each* stock, from identifying the exchange and using the correct tool, to gathering current data, performing news sentiment and fundamental analysis, and finally generating a recommendation.\n*   The required \"Output Format\": This is extremely important for consistency and downstream processing, ensuring the recommendation is structured predictably.\n*   \"Additional Instructions\": These are refinements, such as prioritizing time-sensitive data, considering market trends, assessing risk, and weighting technical vs. fundamental analysis.\nThis comprehensive system message is key to guiding the AI to produce valuable and well-structured trade recommendations.\n\nFor the `AI Agent` to function, it needs an actual language model. Since the prompt specified \"Anthropic AI agent,\" I'd add an **Anthropic Chat Model** node. This is `@n8n/n8n-nodes-langchain.lmChatAnthropic` with `typeVersion: 1.2`. It's configured to use the `claude-3-5-sonnet-20241022` model, which is a robust choice for analytical tasks. I'd connect this node's `ai_languageModel` output to the `AI Agent` node's `ai_languageModel` input. An Anthropic API credential would be configured here to allow communication with the Anthropic service.\n\nNow, for the \"specialized NASDAQ, NYSE, and NYSE Arca stock analysis tools, along with news sources\" mentioned in the prompt. These will be connected as tools to the AI Agent.\n\nFor the stock analysis:\n*   I'd add a **NASDAQ Stock Analysis Tool**. This is `@n8n/n8n-nodes-langchain.toolWorkflow` with `typeVersion: 1.3`. It's named \"NASDAQ\" and has a description explaining its purpose. Crucially, it uses a `workflowId` to point to another, presumably detailed, n8n workflow that handles the actual NASDAQ data fetching and analysis. This modularity is excellent for managing complexity. I'd connect its `ai_tool` output to the `AI Agent` node's `ai_tool` input.\n*   Similarly, a **NYSE Stock Analysis Tool** (`@n8n/n8n-nodes-langchain.toolWorkflow`, `typeVersion: 1.3`) is added. It's named \"NYSE\" and also points to a dedicated NYSE analysis workflow via its `workflowId`. This too connects as an `ai_tool` to the `AI Agent`.\n*   And for ETFs on NYSE Arca, an **NYSE Arca Stock Analysis Tool** (`@n8n/n8n-nodes-langchain.toolWorkflow`, `typeVersion: 1.3`) is included, named \"Arca\", with its own `workflowId`. This also connects as an `ai_tool` to the `AI Agent`.\n\nFor news sources:\n*   A **NewsAPI Tool** (`@n8n/n8n-nodes-langchain.toolWorkflow`, `typeVersion: 1.3`) is present. It's named \"NewsAPI\" and described as using the NewsAPI for headlines, linking to another workflow via `workflowId`. This would connect as an `ai_tool` to the `AI Agent`. *However, I notice in the JSON that this node is explicitly `disabled: true`. This means even though the AI's system message describes it, this tool won't actually be available to the agent during execution.*\n*   A **Wiki Current Events** node (`@n8n/n8n-nodes-langchain.toolHttpRequest`, `typeVersion: 1.1`) is included. This is an HTTP Request tool, configured with a description and a direct URL to Wikipedia's current events page. It would connect as an `ai_tool` to the `AI Agent`. *Like the NewsAPI tool, this one is also `disabled: true` in the JSON.*\n*   A **CNN Headlines** node (`@n8n/n8n-nodes-langchain.toolHttpRequest`, `typeVersion: 1.1`) is also present. Another HTTP Request tool, configured with a description and a URL for CNN headlines. It would also connect as an `ai_tool` to the `AI Agent`. *And this one too is `disabled: true`.*\n\nIt's a curious design choice that the news tools are disabled. If the user truly wants news to influence recommendations, these should be enabled. As it stands, the AI agent's analysis would rely primarily on the data it gets from the stock analysis tools.\n\nFinally, the user wants the recommendations delivered \"via email.\" So, I'll add a **Gmail** node. This is `n8n-nodes-base.gmail` with `typeVersion: 2.1`.\n\nI'd connect the `AI Agent`'s `main` output to the `Gmail` node's `main` input. The AI Agent will produce the stock recommendations as its output, which will be available as `$json.output`.\nInside the Gmail node's parameters:\n*   `sendTo`: This is where the recipient's email address would be configured. It's a placeholder for the actual user's email.\n*   `subject`: The subject line is dynamically set using an expression: `=Stock Analysis: {{ $('Schedule Trigger').item.json['Readable date'] }}`. This is clever, as it pulls the execution date from the very first node, making the email subjects informative.\n*   `message`: This is set to `={{ $json.output }}`, which injects the AI agent's full analysis and recommendations directly into the email body.\n*   A Gmail OAuth2 credential would be required here to authorize sending emails.\n\nI also see a **Telegram** node (`n8n-nodes-base.telegram`, `typeVersion: 1.2`) in the JSON. This node is also connected from the `AI Agent`'s `main` output, configured to send `={{ $json.output }}` to a specific chat ID. This would provide an alternative or additional delivery channel for the recommendations. *However, this node is also `disabled: true` in the provided JSON.* This suggests it might have been an option considered but not currently active, or perhaps for testing. If enabled, it would need a Telegram API credential and a specific `chatId`.\n\nThe overall workflow structure, with a schedule trigger, data preparation, an AI agent leveraging specific tools, and then delivery via email (with Telegram as a disabled option), aligns well with the user's prompt. The modularity of using workflow tools for stock analysis is a good design practice.", "hard", "{\n  \"name\": \"Daily Stock Trade Recommendations\",\n  \"nodes\": [\n    {\n      \"parameters\": {\n        \"cronExpression\": \"45 9 * * 1-5\",\n        \"timezone\": \"America/New_York\"\n      },\n      \"name\": \"<PERSON><PERSON> Trigger\",\n      \"type\": \"n8n-nodes-base.cron\",\n      \"typeVersion\": 1,\n      \"position\": [\n        0,\n        0\n      ]\n    },\n    {\n      \"parameters\": {\n        \"messages\": [\n          {\n            \"role\": \"user\",\n            \"content\": \"You are a stock market analyst. Provide daily trade recommendations for Microsoft (MSFT), Meta Platforms (META), Nvidia (NVDA), Schwab UDR Dividend ETF (SCHA), Vanguard S&P 500 ETF (VOO), and Vanguard Total Market ETF (VTI). Use NASDAQ, NYSE, NYSE Arca stock analysis tools, and incorporate current news sources. Return recommendations in concise format.\"\n          }\n        ],\n        \"model\": \"claude-3-5-sonnet-20240620\",\n        \"temperature\": 0.7,\n        \"maxTokens\": 500\n      },\n      \"name\": \"Anthropic\",\n      \"type\": \"n8n-nodes-hosted:Anthropic\",\n      \"typeVersion\": 1,\n      \"position\": [\n        200,\n        0\n      ]\n    },\n    {\n      \"parameters\": {\n        \"fromEmail\": \"<EMAIL>\",\n        \"toEmail\": \"<EMAIL>\",\n        \"subject\": \"Daily Stock Trade Recommendations\",\n        \"text\": \"{{ $node[\\\"Anthropic\\\"].json.response }}\"\n      },\n      \"name\": \"Send Email\",\n      \"type\": \"n8n-nodes-base.sendEmail\",\n      \"typeVersion\": 1,\n      \"position\": [\n        400,\n        0\n      ]\n    }\n  ],\n  \"connections\": {\n    \"Cron Trigger\": {\n      \"main\": [\n        [\n          {\n            \"node\": \"Anthropic\",\n            \"type\": \"main\",\n            \"index\": 0\n          }\n        ]\n      ]\n    },\n    \"Anthropic\": {\n      \"main\": [\n        [\n          {\n            \"node\": \"Send Email\",\n            \"type\": \"main\",\n            \"index\": 0\n          }\n        ]\n      ]\n    }\n  },\n  \"active\": false\n}", "10", "1.0", "4.0", "1.0", "3.0", "1.0", "1.0", "3.0", "3.0", "2.0", "2.0", "2.0", "1.0", null, "33.3333333333", "33.3333333333", "100.0", "100.0", "100.0", "100.0"], ["4", "1", "Generate an active n8n workflow named \"AppTest n8n Onboarding Scaling Automation Solutions\". It should start with a Webhook node configured at `onboarding/n8n/tests/ScalingAutomationSolutions` that connects to a \"Respond to Webhook\" node. The \"Respond to Webhook\" node must return an HTML document that renders a \"Scaling Automation Solutions\" quiz, which upon submission or task completion, posts data to `https://auto.crm-s.com/webhook/Onboarding/Update` and `https://auto.crm-s.com/webhook/Onboarding/UpdateTaskTable` respectively. The workflow should also be tagged with \"n8n\", \"Tests\", \"Day5\", and \"Onboarding\".", "{\"name\": \"AppTest n8n Onboarding Scaling Automation Solutions\", \"nodes\": [{\"parameters\": {\"path\": \"onboarding/n8n/tests/ScalingAutomationSolutions\", \"responseMode\": \"responseNode\", \"options\": {}}, \"name\": \"Webhook\", \"type\": \"n8n-nodes-base.webhook\", \"typeVersion\": 2, \"position\": [0, 0]}, {\"parameters\": {\"respondWith\": \"text\", \"responseBody\": \"<!DOCTYPE html>\\n<html lang=\\\"en\\\">\\n<head>\\n  <link type=\\\"image/png\\\" sizes=\\\"16x16\\\" rel=\\\"icon\\\" href=\\\"https://i.postimg.cc/gJf9MgWR/icons8-32.png\\\">\\n  <meta charset=\\\"UTF-8\\\">\\n  <title>Scaling Automation Solutions</title>\\n  <link href=\\\"https://fonts.googleapis.com/css2?family=Open+Sans:wght@400;600;700&display=swap\\\" rel=\\\"stylesheet\\\">\\n  <style>\\n    :root {\\n      --deep-blue: #081E2F;\\n      --primary-yellow: #FCB305;\\n      --red: #D32F2F;\\n      --red-hover: #C62828;\\n      --green: #2E7D32;\\n      --green-hover: #1B5E20;\\n      --blue: #197502;\\n      --blue-dark: #1555C0;\\n      --blue-light: #4245F5;\\n    }\\n    body {\\n      font-family: 'Open Sans', sans-serif;\\n      background: #F8F9FA;\\n      padding: 2rem;\\n      max-width: 800px;\\n      margin: 0 auto;\\n      min-height: 100vh;\\n    }\\n    #titlePage {\\n      text-align: center;\\n      padding: 3rem 1rem;\\n    }\\n    #titlePage h1 {\\n      font-size: 32px;\\n      color: var(--deep-blue);\\n      margin-bottom: 1.5rem;\\n    }\\n    .quiz-info {\\n      background: white;\\n      padding: 2rem;\\n      border-radius: 12px;\\n      box-shadow: 0 4px 12px rgba(8, 30, 47, 0.1);\\n      margin: 2rem auto;\\n      max-width: 500px;\\n    }\\n    .info-item {\\n      font-size: 16px;\\n      color: #4A4A4A;\\n      margin: 1rem 0;\\n      display: flex;\\n      justify-content: space-between;\\n      align-items: center;\\n    }\\n    #timer {\\n      position: fixed;\\n      top: 20px;\\n      right: 20px;\\n      background: var(--green);\\n      color: white;\\n      padding: 8px 16px;\\n      border-radius: 20px;\\n      font-weight: 600;\\n      box-shadow: 0 2px 6px rgba(46, 125, 50, 0.2);\\n    }\\n    .hidden {\\n      display: none;\\n    }\\n    .question {\\n      background: white;\\n      border-radius: 8px;\\n      padding: 1.5rem;\\n      margin-bottom: 1.5rem;\\n      box-shadow: 0 2px 8px rgba(8, 30, 47, 0.1);\\n      transition: all 0.3s ease;\\n    }\\n    .question h3 {\\n      font-size: 16px;\\n      font-weight: 600;\\n      color: var(--deep-blue);\\n      margin: 0 0 1rem 0;\\n    }\\n    label {\\n      display: block;\\n      margin: 0.5rem 0;\\n      padding: 0.75rem;\\n      border-radius: 4px;\\n      font-size: 14px;\\n      color: #4A4A4A;\\n      cursor: pointer;\\n      transition: all 0.2s ease;\\n    }\\n    label:hover {\\n      background: rgba(25, 118, 210, 0.04);\\n    }\\n    input[type=\\\"radio\\\"] {\\n      margin-right: 0.75rem;\\n      accent-color: var(--green);\\n      border-radius: 50%;\\n      width: 16px;\\n      height: 16px;\\n    }\\n    input[type=\\\"checkbox\\\"] {\\n      margin-right: 0.75rem;\\n      accent-color: var(--green);\\n    }\\n    input[type=\\\"text\\\"],\\n    input[type=\\\"number\\\"] {\\n      padding: 0.5rem;\\n      border: 1px solid #DEE2E6;\\n      border-radius: 4px;\\n      font-size: 14px;\\n      width: 100%;\\n      box-sizing: border-box;\\n    }\\n    input[disabled] {\\n      background: #f3f3f3;\\n      color: #888;\\n    }\\n    button {\\n      background: var(--green);\\n      color: white;\\n      border: none;\\n      padding: 12px 24px;\\n      border-radius: 6px;\\n      font-weight: 700;\\n      font-size: 15px;\\n      cursor: pointer;\\n      transition: all 0.3s ease;\\n      display: block;\\n      margin: 2rem auto 0;\\n      letter-spacing: 0.5px;\\n    }\\n    button:hover {\\n      background: var(--green-hover);\\n      transform: translateY(-2px);\\n      box-shadow: 0 4px 12px rgba(46, 125, 50, 0.3);\\n    }\\n    #result {\\n      background: white;\\n      padding: 1.5rem;\\n      border-radius: 8px;\\n      text-align: center;\\n      margin-top: 2rem;\\n      box-shadow: 0 2px 8px rgba(8, 30, 47, 0.1);\\n    }\\n    .correct {\\n      border-left: 4px solid var(--green);\\n      background: #F0F9FF;\\n    }\\n    .incorrect {\\n      border-left: 4px solid var(--red);\\n    }\\n    .correct-answer {\\n      background: #E8F5E9 !important;\\n      border: 1px solid var(--green);\\n      border-radius: 4px;\\n      padding: 2px 4px;\\n      margin-top: 0.5rem;\\n      display: inline-block;\\n    }\\n    @media (max-width: 600px) {\\n      body {\\n        padding: 1rem;\\n      }\\n      .question {\\n        padding: 1rem;\\n      }\\n      button {\\n        width: 100%;\\n        padding: 14px 20px;\\n      }\\n      #timer {\\n        top: 10px;\\n        right: 10px;\\n        font-size: 14px;\\n      }\\n    }\\n  </style>\\n</head>\\n<body>\\n  <!-- Title Page -->\\n  <div id=\\\"titlePage\\\">\\n    <h1>Scaling Automation Solutions</h1>\\n    <div class=\\\"quiz-info\\\">\\n      <div class=\\\"info-item\\\">\\n        <span>Questions:</span>\\n        <strong>15</strong>\\n      </div>\\n      <div class=\\\"info-item\\\">\\n        <span>Time Limit:</span>\\n        <strong>5 minutes</strong>\\n      </div>\\n      <div class=\\\"info-item\\\">\\n        <span>Passing Score:</span>\\n        <strong>80%</strong>\\n      </div>\\n      <button id=\\\"startExamBtn\\\" onclick=\\\"startQuiz()\\\">Start Exam</button>\\n    </div>\\n  </div>\\n  \\n  <!-- Quiz Container -->\\n  <div id=\\\"quizContainer\\\" class=\\\"hidden\\\">\\n    <div id=\\\"timer\\\">00:00</div>\\n    <h1>Scaling Automation Solutions</h1>\\n    <form id=\\\"examForm\\\">\\n      <!-- User Data Block (not scored) -->\\n      <div class=\\\"question\\\" data-points=\\\"0\\\">\\n        <h3>Enter Your Information</h3>\\n        <label>\\n          Your Name:\\n          <input type=\\\"text\\\" name=\\\"fullName\\\" placeholder=\\\"e.g., John Doe\\\">\\n        </label>\\n        <label>\\n          Your Profession:\\n          <input type=\\\"text\\\" name=\\\"profession\\\" value=\\\"All\\\" disabled>\\n        </label>\\n        <label>\\n          Your Recruiter:\\n          <select name=\\\"recruiter\\\">\\n            <option value=\\\"Anastasia Fadeeva\\\">Anastasia Fadeeva</option>\\n            <option value=\\\"Elena Ermakova\\\">Elena Ermakova</option>\\n            <option value=\\\"Anna Aleksandrova\\\">Anna Aleksandrova</option>\\n            <option value=\\\"Sabina Gasanova\\\">Sabina Gasanova</option>\\n          </select>\\n        </label>\\n      </div>\\n      \\n      <!-- Q1 -->\\n      <div class=\\\"question\\\" data-points=\\\"2\\\">\\n        <h3>1. Which of the following is a foundational strategy for workflow scaling, focusing on breaking down complex processes?</h3>\\n        <label><input type=\\\"radio\\\" name=\\\"q1\\\" value=\\\"A\\\"> Workflow Orchestration</label>\\n        <label><input type=\\\"radio\\\" name=\\\"q1\\\" value=\\\"B\\\"> Parameterization and Configuration</label>\\n        <label><input type=\\\"radio\\\" name=\\\"q1\\\" value=\\\"C\\\" data-correct=\\\"true\\\"> Modular Design</label>\\n        <label><input type=\\\"radio\\\" name=\\\"q1\\\" value=\\\"D\\\"> Asynchronous Processing</label>\\n      </div>\\n      \\n      <!-- Q2 -->\\n      <div class=\\\"question\\\" data-points=\\\"2\\\">\\n        <h3>2. What is the primary benefit of modular design in workflow scaling?</h3>\\n        <label><input type=\\\"radio\\\" name=\\\"q2\\\" value=\\\"A\\\"> It makes workflows harder to maintain.</label>\\n        <label><input type=\\\"radio\\\" name=\\\"q2\\\" value=\\\"B\\\"> It creates monolithic, end-to-end workflows.</label>\\n        <label><input type=\\\"radio\\\" name=\\\"q2\\\" value=\\\"C\\\" data-correct=\\\"true\\\"> It enhances maintainability and promotes reusability.</label>\\n        <label><input type=\\\"radio\\\" name=\\\"q2\\\" value=\\\"D\\\"> It reduces workflow flexibility.</label>\\n      </div>\\n      \\n      <!-- Q3 -->\\n      <div class=\\\"question\\\" data-points=\\\"2\\\">\\n        <h3>3. What is the role of workflow orchestration in scaled automation?</h3>\\n        <label><input type=\\\"radio\\\" name=\\\"q3\\\" value=\\\"A\\\"> To complicate the management of automated tasks.</label>\\n        <label><input type=\\\"radio\\\" name=\\\"q3\\\" value=\\\"B\\\" data-correct=\\\"true\\\"> To manage, schedule, and monitor multiple workflows centrally.</label>\\n        <label><input type=\\\"radio\\\" name=\\\"q3\\\" value=\\\"C\\\"> To isolate workflows and prevent data sharing.</label>\\n        <label><input type=\\\"radio\\\" name=\\\"q3\\\" value=\\\"D\\\"> To eliminate the need for error handling.</label>\\n      </div>\\n      \\n      <!-- Q4 -->\\n      <div class=\\\"question\\\" data-points=\\\"2\\\">\\n        <h3>4. Externalizing data input paths and application credentials as configuration parameters in workflows is an example of:</h3>\\n        <label><input type=\\\"radio\\\" name=\\\"q4\\\" value=\\\"A\\\"> Asynchronous Processing</label>\\n        <label><input type=\\\"radio\\\" name=\\\"q4\\\" value=\\\"B\\\"> Modular Design</label>\\n        <label><input type=\\\"radio\\\" name=\\\"q4\\\" value=\\\"C\\\"> Workflow Orchestration</label>\\n        <label><input type=\\\"radio\\\" name=\\\"q4\\\" value=\\\"D\\\" data-correct=\\\"true\\\"> Parameterization and Configuration</label>\\n      </div>\\n      \\n      <!-- Q5 -->\\n      <div class=\\\"question\\\" data-points=\\\"2\\\">\\n        <h3>5. Which processing technique allows workflows to initiate tasks and move on without waiting for immediate completion, enhancing throughput?</h3>\\n        <label><input type=\\\"radio\\\" name=\\\"q5\\\" value=\\\"A\\\"> Synchronous Processing</label>\\n        <label><input type=\\\"radio\\\" name=\\\"q5\\\" value=\\\"B\\\"> Modular Processing</label>\\n        <label><input type=\\\"radio\\\" name=\\\"q5\\\" value=\\\"C\\\" data-correct=\\\"true\\\"> Asynchronous Processing</label>\\n        <label><input type=\\\"radio\\\" name=\\\"q5\\\" value=\\\"D\\\"> Parameter-based Processing</label>\\n      </div>\\n      \\n      <!-- Q6 -->\\n      <div class=\\\"question\\\" data-points=\\\"2\\\">\\n        <h3>6. What is the main goal of load balancing in automation?</h3>\\n        <label><input type=\\\"radio\\\" name=\\\"q6\\\" value=\\\"A\\\"> To overload single resources for efficiency.</label>\\n        <label><input type=\\\"radio\\\" name=\\\"q6\\\" value=\\\"B\\\" data-correct=\\\"true\\\"> To distribute workload across multiple computing resources.</label>\\n        <label><input type=\\\"radio\\\" name=\\\"q6\\\" value=\\\"C\\\"> To complicate task distribution.</label>\\n        <label><input type=\\\"radio\\\" name=\\\"q6\\\" value=\\\"D\\\"> To limit the number of robots used.</label>\\n      </div>\\n      \\n      <!-- Q7 -->\\n      <div class=\\\"question\\\" data-points=\\\"2\\\">\\n        <h3>7. In which load balancing technique are tasks distributed sequentially to available resources in a cyclical order?</h3>\\n        <label><input type=\\\"radio\\\" name=\\\"q7\\\" value=\\\"A\\\"> Weighted Round-Robin</label>\\n        <label><input type=\\\"radio\\\" name=\\\"q7\\\" value=\\\"B\\\"> Resource-based Load Balancing</label>\\n        <label><input type=\\\"radio\\\" name=\\\"q7\\\" value=\\\"C\\\" data-correct=\\\"true\\\"> Round-Robin Distribution</label>\\n        <label><input type=\\\"radio\\\" name=\\\"q7\\\" value=\\\"D\\\"> Least Connection Load Balancing</label>\\n      </div>\\n      \\n      <!-- Q8 -->\\n      <div class=\\\"question\\\" data-points=\\\"2\\\">\\n        <h3>8. Which dynamic load balancing technique routes tasks to the resource with the fewest active connections?</h3>\\n        <label><input type=\\\"radio\\\" name=\\\"q8\\\" value=\\\"A\\\"> Round-Robin Distribution</label>\\n        <label><input type=\\\"radio\\\" name=\\\"q8\\\" value=\\\"B\\\"> Weighted Round-Robin</label>\\n        <label><input type=\\\"radio\\\" name=\\\"q8\\\" value=\\\"C\\\"> Resource-based Load Balancing</label>\\n        <label><input type=\\\"radio\\\" name=\\\"q8\\\" value=\\\"D\\\" data-correct=\\\"true\\\"> Least Connection Load Balancing</label>\\n      </div>\\n      \\n      <!-- Q9 -->\\n      <div class=\\\"question\\\" data-points=\\\"2\\\">\\n        <h3>9. What does \\\"right-sizing your infrastructure\\\" in resource optimization mean?</h3>\\n        <label><input type=\\\"radio\\\" name=\\\"q9\\\" value=\\\"A\\\"> Over-provisioning resources for future growth.</label>\\n        <label><input type=\\\"radio\\\" name=\\\"q9\\\" value=\\\"B\\\"> Under-provisioning to minimize upfront costs.</label>\\n        <label><input type=\\\"radio\\\" name=\\\"q9\\\" value=\\\"C\\\" data-correct=\\\"true\\\"> Accurately assessing resource needs and aligning infrastructure accordingly.</label>\\n        <label><input type=\\\"radio\\\" name=\\\"q9\\\" value=\\\"D\\\"> Ignoring resource requirements for automation.</label>\\n      </div>\\n      \\n      <!-- Q10 -->\\n      <div class=\\\"question\\\" data-points=\\\"2\\\">\\n        <h3>10. What benefit does elasticity and scalability in resource pools offer for automation?</h3>\\n        <label><input type=\\\"radio\\\" name=\\\"q10\\\" value=\\\"A\\\"> It leads to resource wastage and increased costs.</label>\\n        <label><input type=\\\"radio\\\" name=\\\"q10\\\" value=\\\"B\\\" data-correct=\\\"true\\\"> It allows automatic adjustment of resource capacity based on demand.</label>\\n        <label><input type=\\\"radio\\\" name=\\\"q10\\\" value=\\\"C\\\"> It requires manual adjustment of resources, increasing workload.</label>\\n        <label><input type=\\\"radio\\\" name=\\\"q10\\\" value=\\\"D\\\"> It limits the number of tasks that can be processed.</label>\\n      </div>\\n      \\n      <!-- Q11 -->\\n      <div class=\\\"question\\\" data-points=\\\"2\\\">\\n        <h3>11. What is the function of a Center of Excellence (CoE) for automation within an enterprise?</h3>\\n        <label><input type=\\\"radio\\\" name=\\\"q11\\\" value=\\\"A\\\"> To isolate automation efforts within a single team.</label>\\n        <label><input type=\\\"radio\\\" name=\\\"q11\\\" value=\\\"B\\\" data-correct=\\\"true\\\"> To centralize expertise and guidance for automation initiatives across the enterprise.</label>\\n        <label><input type=\\\"radio\\\" name=\\\"q11\\\" value=\\\"C\\\"> To discourage automation adoption across the organization.</label>\\n        <label><input type=\\\"radio\\\" name=\\\"q11\\\" value=\\\"D\\\"> To create siloed automation efforts.</label>\\n      </div>\\n      \\n      <!-- Q12 -->\\n      <div class=\\\"question\\\" data-points=\\\"2\\\">\\n        <h3>12. Which of the following is a crucial element of a robust automation governance framework?</h3>\\n        <label><input type=\\\"radio\\\" name=\\\"q12\\\" value=\\\"A\\\"> Lack of clear roles and responsibilities.</label>\\n        <label><input type=\\\"radio\\\" name=\\\"q12\\\" value=\\\"B\\\"> Absence of process for project approvals.</label>\\n        <label><input type=\\\"radio\\\" name=\\\"q12\\\" value=\\\"C\\\" data-correct=\\\"true\\\"> Defined processes for project approvals and standards for security.</label>\\n        <label><input type=\\\"radio\\\" name=\\\"q12\\\" value=\\\"D\\\"> Ignoring security and compliance standards.</label>\\n      </div>\\n      \\n      <!-- Q13 -->\\n      <div class=\\\"question\\\" data-points=\\\"2\\\">\\n        <h3>13. Hyperautomation extends beyond RPA to include technologies like:</h3>\\n        <label><input type=\\\"radio\\\" name=\\\"q13\\\" value=\\\"A\\\"> Only Robotic Process Automation.</label>\\n        <label><input type=\\\"radio\\\" name=\\\"q13\\\" value=\\\"B\\\" data-correct=\\\"true\\\"> AI, ML, Process Mining, and Low-Code Platforms.</label>\\n        <label><input type=\\\"radio\\\" name=\\\"q13\\\" value=\\\"C\\\"> Just traditional coding methods.</label>\\n        <label><input type=\\\"radio\\\" name=\\\"q13\\\" value=\\\"D\\\"> Manual processes and workflows.</label>\\n      </div>\\n      \\n      <!-- Q14 -->\\n      <div class=\\\"question\\\" data-points=\\\"2\\\">\\n        <h3>14. Cloud-native automation architectures leverage which technologies for scalability and resilience?</h3>\\n        <label><input type=\\\"radio\\\" name=\\\"q14\\\" value=\\\"A\\\"> Legacy systems and monolithic applications.</label>\\n        <label><input type=\\\"radio\\\" name=\\\"q14\\\" value=\\\"B\\\" data-correct=\\\"true\\\"> Containerization, microservices, and serverless computing.</label>\\n        <label><input type=\\\"radio\\\" name=\\\"q14\\\" value=\\\"C\\\"> On-premise servers exclusively.</label>\\n        <label><input type=\\\"radio\\\" name=\\\"q14\\\" value=\\\"D\\\"> Manual infrastructure management techniques.</label>\\n      </div>\\n      \\n      <!-- Q15 -->\\n      <div class=\\\"question\\\" data-points=\\\"2\\\">\\n        <h3>15. What role do process mining and automation discovery tools play in scaling automation?</h3>\\n        <label><input type=\\\"radio\\\" name=\\\"q15\\\" value=\\\"A\\\"> To manually identify automation opportunities.</label>\\n        <label><input type=\\\"radio\\\" name=\\\"q15\\\" value=\\\"B\\\"> To complicate the process of automation identification.</label>\\n        <label><input type=\\\"radio\\\" name=\\\"q15\\\" value=\\\"C\\\" data-correct=\\\"true\\\"> To automatically discover and analyze processes for automation opportunities.</label>\\n        <label><input type=\\\"radio\\\" name=\\\"q15\\\" value=\\\"D\\\"> To limit the scope of automation initiatives.</label>\\n      </div>\\n      \\n      <button id=\\\"submitBtn\\\" type=\\\"button\\\" onclick=\\\"calculateScore()\\\">Submit Exam</button>\\n    </form>\\n    <div id=\\\"result\\\"></div>\\n  </div>\\n  \\n  <script>\\n    // Fisher\\u2013Yates shuffle function\\n    function shuffle(array) {\\n      for (let i = array.length - 1; i > 0; i--) {\\n        const j = Math.floor(Math.random() * (i + 1));\\n        [array[i], array[j]] = [array[j], array[i]];\\n      }\\n      return array;\\n    }\\n  \\n    // Randomize answer labels (excluding the user data block)\\n    function randomizeAnswers() {\\n      const questions = document.querySelectorAll('.question[data-points]:not([data-points=\\\"0\\\"])');\\n      questions.forEach(question => {\\n        const labels = Array.from(question.querySelectorAll('label'));\\n        const shuffled = shuffle(labels);\\n        shuffled.forEach(label => label.parentNode.removeChild(label));\\n        const heading = question.querySelector('h3');\\n        shuffled.forEach(label => heading.parentNode.appendChild(label));\\n      });\\n    }\\n  \\n    let timer;\\n    let seconds = 0;\\n    const timeLimit = 300; // 5 minutes\\n    const examName = \\\"Scaling Automation Solutions\\\";\\n  \\n    function startQuiz() {\\n      document.getElementById('titlePage').classList.add('hidden');\\n      document.getElementById('quizContainer').classList.remove('hidden');\\n      randomizeAnswers();\\n      startTimer();\\n    }\\n  \\n    function startTimer() {\\n      timer = setInterval(() => {\\n        seconds++;\\n        const minutes = Math.floor(seconds / 60);\\n        const rem = seconds % 60;\\n        document.getElementById('timer').textContent =\\n          String(minutes).padStart(2, '0') + \\\":\\\" + String(rem).padStart(2, '0');\\n        if (seconds >= timeLimit) {\\n          clearInterval(timer);\\n          calculateScore(true);\\n        }\\n      }, 1000);\\n    }\\n  \\n    // Formats date as \\\"dd.mm.yyyy hh:mm\\\"\\n    function formatDate(date) {\\n      const options = {\\n        day: \\\"2-digit\\\",\\n        month: \\\"2-digit\\\",\\n        year: \\\"numeric\\\",\\n        hour: \\\"2-digit\\\",\\n        minute: \\\"2-digit\\\",\\n        hour12: false\\n      };\\n      const formatted = date.toLocaleString(\\\"en-GB\\\", options);\\n      return formatted.replace(/\\\\//g, \\\".\\\").replace(\\\", \\\", \\\" \\\");\\n    }\\n  \\n    function calculateScore(timeout = false) {\\n      const submitButton = document.getElementById('submitBtn');\\n      if (submitButton) {\\n        submitButton.style.display = 'none';\\n      }\\n      clearInterval(timer);\\n      let totalPoints = 0;\\n      let maxPoints = 0;\\n      const questions = document.querySelectorAll('.question');\\n      questions.forEach(question => {\\n        const points = parseInt(question.dataset.points) || 1;\\n        maxPoints += points;\\n        if (points === 0) return; // Skip user info block\\n        let correct = true;\\n        const inputs = question.querySelectorAll('input');\\n        let selectedValues = [];\\n        let correctValues = [];\\n        inputs.forEach(inp => {\\n          if (inp.checked) selectedValues.push(inp.value);\\n          if (inp.dataset.correct === \\\"true\\\") correctValues.push(inp.value);\\n        });\\n        if (\\n          selectedValues.length !== correctValues.length ||\\n          !selectedValues.every(val => correctValues.includes(val))\\n        ) {\\n          correct = false;\\n        }\\n        if (correct) {\\n          totalPoints += points;\\n          question.classList.add('correct');\\n        } else {\\n          question.classList.add('incorrect');\\n          inputs.forEach(inp => {\\n            if (inp.dataset.correct === \\\"true\\\") {\\n              inp.parentElement.classList.add('correct-answer');\\n            }\\n          });\\n        }\\n      });\\n      const percentage = ((totalPoints / maxPoints) * 100).toFixed(1);\\n      const timeUsed = Math.floor(seconds / 60) + \\\"m \\\" + (seconds % 60) + \\\"s\\\";\\n      let resultsHTML = \\n        `<h3>Exam Results</h3>\\n         <p>Your score: ${totalPoints} / ${maxPoints} (${percentage}%)</p>\\n         <p>Time used: ${timeUsed}</p>\\n         ${percentage >= 80 ? \\\"Congratulations! You passed!\\\" : \\\"Try again! Review your mistakes below.\\\"}\\n         ${timeout ? \\\"<p class='warning'>Time limit exceeded!</p>\\\" : \\\"\\\"}`;\\n      resultsHTML += \\n        `<button type=\\\"button\\\" onclick=\\\"location.reload()\\\">Retry</button>\\n         <button type=\\\"button\\\" onclick=\\\"window.location.href='#'\\\">Read Again</button>\\n         <button type=\\\"button\\\" id=\\\"taskButton\\\" onclick=\\\"loadTask()\\\">Task</button>`;\\n      document.getElementById('result').innerHTML = resultsHTML;\\n  \\n      const fullName = document.querySelector('[name=\\\"fullName\\\"]')?.value.trim() || \\\"\\\";\\n      const profession = document.querySelector('[name=\\\"profession\\\"]')?.value.trim() || \\\"\\\";\\n      const recruiter = document.querySelector('[name=\\\"recruiter\\\"]')?.value.trim() || \\\"\\\";\\n  \\n      // Post data to your webhook (Day remains \\\"5\\\")\\n      const postData = {\\n        fullName,\\n        profession,\\n        recruiter,\\n        day: \\\"5\\\",\\n        examName,\\n        scoreObtained: totalPoints,\\n        scoreTotal: maxPoints,\\n        timeUsed: seconds,\\n        timeTotal: timeLimit,\\n        date: formatDate(new Date())\\n      };\\n  \\n      try {\\n        window.scrollTo({ top: 0, behavior: 'smooth' });\\n        fetch(\\\"https://auto.crm-s.com/webhook/Onboarding/Update\\\", {\\n          method: \\\"POST\\\",\\n          headers: {\\n            \\\"Content-Type\\\": \\\"application/json\\\",\\n            \\\"Accept\\\": \\\"application/json, text/plain, */*\\\"\\n          },\\n          body: JSON.stringify(postData),\\n          mode: \\\"cors\\\"\\n        })\\n        .then(async (res) => {\\n          console.log(\\\"POST response status:\\\", res.status);\\n          const text = await res.text();\\n          console.log(\\\"POST response body:\\\", text);\\n        })\\n        .catch(err => {\\n          console.error(\\\"Error in POST:\\\", err);\\n        });\\n      } catch (error) {\\n        console.error(\\\"Error submitting quiz results:\\\", error);\\n      }\\n    }\\n  \\n    // loadTask() replaces the quiz view with task instructions\\n    function loadTask() {\\n      const taskMarkdown = `# \\ud83d\\udee0 Scaling Automation Solutions - Practice Task\\n\\nDevelop a scenario that demonstrates how to implement strategies for scaling automation solutions to improve efficiency and adaptability. Focus on:\\n- Identifying bottlenecks in current automation workflows and proposing mitigation strategies.\\n- Outlining the configuration of scalable modules, including modular design and load balancing techniques.\\n- Describing how dynamic resource allocation and asynchronous processing enhance performance.\\n- Reflecting on the benefits of a scalable automation framework in optimizing operations.\\n\\n## Steps:\\n1. **Workflow Analysis Challenge**: Describe a current automation workflow that could benefit from scaling.\\n2. **Scaling Strategy**: Outline your plan for implementing scalable automation solutions, including modular design and load balancing.\\n3. **Technical Details**: Specify key parameters, tools, and error handling mechanisms involved in your solution.\\n4. **Impact Evaluation**: Explain how scaling automation solutions improves efficiency, adaptability, and overall performance.\\n\\nWhen finished, mark the checkbox and optionally provide a link to your detailed plan.`;\\n  \\n      const parsedMarkdown = parseMarkdown(taskMarkdown);\\n      document.body.innerHTML = \\n        `<div style=\\\"font-family: 'Open Sans', sans-serif; background: #F8F9FA; padding: 2rem; max-width: 800px; margin: 0 auto;\\\">\\n          ${parsedMarkdown}\\n          <div style=\\\"margin-top: 2rem;\\\">\\n            <label for=\\\"taskLinkInput\\\" style=\\\"font-weight: bold;\\\">Optional link to your work:</label>\\n            <input type=\\\"text\\\" id=\\\"taskLinkInput\\\" placeholder=\\\"https://docs.google.com/...\\\" style=\\\"width: 100%; padding: 8px; margin: 4px 0 1rem;\\\">\\n            <input type=\\\"checkbox\\\" id=\\\"taskCompletedCheckbox\\\">\\n            <label for=\\\"taskCompletedCheckbox\\\">Task Completed</label>\\n          </div>\\n        </div>`;\\n      const checkbox = document.getElementById(\\\"taskCompletedCheckbox\\\");\\n      checkbox.addEventListener(\\\"change\\\", function() {\\n        if (checkbox.checked) {\\n          const taskLink = document.getElementById(\\\"taskLinkInput\\\").value.trim() || \\\"\\\";\\n          checkbox.disabled = true;\\n          try {\\n            fetch(\\\"https://auto.crm-s.com/webhook/Onboarding/UpdateTaskTable\\\", {\\n              method: \\\"POST\\\",\\n              headers: {\\n                \\\"Content-Type\\\": \\\"application/json\\\",\\n                \\\"Accept\\\": \\\"application/json, text/plain, */*\\\"\\n              },\\n              body: JSON.stringify({ link: taskLink, date: formatDate(new Date()) }),\\n              mode: \\\"cors\\\"\\n            })\\n            .then(async (res) => {\\n              console.log(\\\"Task completed webhook status:\\\", res.status);\\n              const text = await res.text();\\n              console.log(\\\"Task completed webhook body:\\\", text);\\n            })\\n            .catch(err => {\\n              console.error(\\\"Error sending task completed webhook:\\\", err);\\n              alert(\\\"Your task has been marked as complete, but there was an issue connecting to the server. Your instructor will be notified.\\\");\\n            });\\n          } catch (error) {\\n            console.error(\\\"Error marking task as complete:\\\", error);\\n            alert(\\\"Your task has been marked as complete, but there was an issue connecting to the server. Your instructor will be notified.\\\");\\n          }\\n        }\\n      });\\n    }\\n  \\n    // Markdown parser: converts headings (#, ##, ###, ####) into <details> blocks\\n    function parseMarkdown(markdownText) {\\n      const lines = markdownText.split('\\\\n');\\n      let html = \\\"\\\";\\n      const stack = [];\\n      lines.forEach(line => {\\n        const match = line.match(/^(#{1,4})\\\\s+(.*)/);\\n        if (match) {\\n          const level = match[1].length;\\n          const text = match[2];\\n          while (stack.length && stack[stack.length - 1] >= level) {\\n            html += \\\"</details>\\\";\\n            stack.pop();\\n          }\\n          html += `<details><summary>${text}</summary>`;\\n          stack.push(level);\\n        } else {\\n          html += line + \\\"<br>\\\";\\n        }\\n      });\\n      while (stack.length) {\\n        html += \\\"</details>\\\";\\n        stack.pop();\\n      }\\n      return html;\\n    }\\n  </script>\\n</body>\\n</html>\\n\", \"options\": {\"responseCode\": 200, \"responseHeaders\": {\"entries\": [{\"name\": \"Content-type\", \"value\": \"text/html\"}]}}}, \"name\": \"Respond to Webhook\", \"type\": \"n8n-nodes-base.respondToWebhook\", \"typeVersion\": 1.1, \"position\": [220, 0]}], \"connections\": {\"Webhook\": {\"main\": [[{\"node\": \"Respond to Webhook\", \"type\": \"main\", \"index\": 0}]]}}, \"active\": false}", "Okay, let's get this workflow designed and running. My goal here is to build an active n8n workflow that serves an HTML quiz, captures quiz submission, and handles task completion, all triggered by a specific webhook.\n\nFirst things first, every workflow needs a starting point, a trigger. The prompt clearly states it should \"start with a Webhook node\". So, I'll begin by adding that.\n\n1.  **Adding the Webhook Trigger Node**\n    *   **Node Type**: `n8n-nodes-base.webhook`\n    *   **Type Version**: 2\n    *   **Reasoning**: This is the perfect node to initiate a workflow when an external HTTP request comes in. It acts as an API endpoint for our quiz.\n    *   **Configuration**:\n        *   I need to set its `Path` parameter. The prompt specifies this as `onboarding/n8n/tests/ScalingAutomationSolutions`. This is the unique part of the URL that external systems will call to trigger our quiz. It's crucial for making sure the right workflow gets triggered.\n        *   Next, for the `Response Mode`, the prompt also mentions a \"Respond to Webhook\" node. This means the Webhook node itself won't send an immediate response. Instead, it will delegate the response handling to a subsequent node in the workflow. So, I'll set this to `Response Node`. This is reflected in the `responseMode: \"responseNode\"` in the provided JSON.\n        *   There are no other specific options required for the webhook itself, so the `options` parameter remains empty as shown in the JSON.\n\nNow that we have the entry point, we need to send back the quiz itself. The prompt is very specific about returning an HTML document.\n\n2.  **Adding the Respond to Webhook Node**\n    *   **Node Type**: `n8n-nodes-base.respondToWebhook`\n    *   **Type Version**: 1.1\n    *   **Reasoning**: This node is designed precisely for sending an HTTP response back to the original caller of the webhook. Since our Webhook node is set to `Response Node` mode, this is the necessary follow-up node to generate the actual HTTP response.\n    *   **Connection**: This node needs to be directly connected to the `Webhook` node. I'll take the 'main' output of the `Webhook` node and connect it to the 'main' input of the `Respond to Webhook` node. This ensures that as soon as the webhook is triggered, the workflow proceeds to send the response.\n    *   **Configuration**:\n        *   For `Respond With`, since we're returning an HTML document, I'll choose `Text`. Even though it's HTML, n8n treats it as a string of text for this parameter. The magic of rendering it as HTML happens with the correct `Content-type` header. The JSON confirms `respondWith: \"text\"`.\n        *   The core of this node is the `Response Body`. This is where I'll paste the entire HTML structure provided in the prompt. This HTML needs to do a few critical things:\n            *   It must \"render a 'Scaling Automation Solutions' quiz.\" I can see it contains a full HTML page with a title, information about the quiz (questions, time limit, passing score), and then a series of multiple-choice questions with radio buttons. There's also a \"Start Exam\" button to initiate the quiz and a \"Submit Exam\" button to calculate the score.\n            *   It needs to \"upon submission or task completion, posts data to `https://auto.crm-s.com/webhook/Onboarding/Update`\". Looking at the JavaScript within the HTML, specifically the `calculateScore()` function, I can see a `fetch` request is made to `https://auto.crm-s.com/webhook/Onboarding/Update`. This URL will receive the quiz results (full name, profession, recruiter, exam name, scores, time used, and date). This is precisely what's needed for the quiz submission.\n            *   It also needs to post data to `https://auto.crm-s.com/webhook/Onboarding/UpdateTaskTable` for task completion. Again, examining the JavaScript, the `loadTask()` function, which is triggered after the quiz results are displayed, creates an interface with a checkbox. When this checkbox is marked \"Task Completed\", another `fetch` request is made to `https://auto.crm-s.com/webhook/Onboarding/UpdateTaskTable`, sending the link to the task work and the completion date. This covers the task completion requirement.\n            *   The HTML also contains inline CSS for styling and JavaScript for dynamic behaviors like shuffling answers, a timer, score calculation, and rendering the task instructions.\n        *   Finally, the `Options` for the response:\n            *   I'll set the `Response Code` to `200`, which is the standard HTTP status code for a successful request.\n            *   And critically, for the browser to understand that the `responseBody` is HTML, I need to add a `Response Header`. I'll specify a `Content-type` header with the value `text/html`. This tells the browser how to interpret and render the content.\n\nOnce these two nodes are configured and connected, the basic functionality of serving the quiz and having the client-side JavaScript handle the subsequent data submissions is in place.\n\nLastly, I need to ensure the workflow itself has the correct overall settings.\n*   The prompt states it should be an \"active\" workflow. So, I'll ensure the `active` flag for the workflow is set to `true`.\n*   And for organization and searchability, I need to tag the workflow. The prompt specifies \"n8n\", \"Tests\", \"Day5\", and \"Onboarding\". I'll add these to the workflow's tags.\n\nThat should complete the workflow as described. The webhook will trigger, the HTML quiz will be served, and the JavaScript within that HTML will handle submitting the quiz results and task completion status to the specified external webhooks.", "easy", "{\n  \"name\": \"AppTest n8n Onboarding Scaling Automation Solutions\",\n  \"tags\": [\n    \"n8n\",\n    \"Tests\",\n    \"Day5\",\n    \"Onboarding\"\n  ],\n  \"nodes\": [\n    {\n      \"parameters\": {\n        \"url\": \"/onboarding/n8n/tests/ScalingAutomationSolutions\",\n        \"responseFormat\": \"query\",\n        \"responsePropertyName\": \"query\"\n      },\n      \"name\": \"Webhook\",\n      \"type\": \"n8n-nodes-base.webhook\",\n      \"typeVersion\": 1,\n      \"position\": [\n        200,\n        200\n      ]\n    },\n    {\n      \"parameters\": {\n        \"statusCode\": 200,\n        \"response\": \"<html><head><title>Scaling Automation Solutions Quiz</title></head><body><h1>Scaling Automation Solutions Quiz</h1><form action=\\\"https://auto.crm-s.com/webhook/Onboarding/Update\\\" method=\\\"post\\\"><label>Question: What is scaling?</label><input type=\\\"text\\\" name=\\\"answer\\\"><br><button type=\\\"submit\\\">Submit to Update</button></form><form action=\\\"https://auto.crm-s.com/webhook/Onboarding/UpdateTaskTable\\\" method=\\\"post\\\"><label>Task Completion Status:</label><input type=\\\"text\\\" name=\\\"status\\\"><br><button type=\\\"submit\\\">Submit to Update Task Table</button></form></body></html>\"\n      },\n      \"name\": \"Respond Webhook\",\n      \"type\": \"n8n-nodes-base.respondWebhook\",\n      \"typeVersion\": 1,\n      \"position\": [\n        600,\n        200\n      ]\n    }\n  ],\n  \"connections\": {\n    \"Webhook\": {\n      \"main\": [\n        [\n          {\n            \"node\": \"Respond Webhook\",\n            \"type\": \"main\",\n            \"index\": 0\n          }\n        ]\n      ]\n    }\n  },\n  \"active\": true\n}", "10", "1.0", "4.0", "1.0", "2.0", "1.0", "1.0", "2.0", "2.0", "1.0", "1.0", "1.0", "1.0", null, "50.0", "50.0", "100.0", "100.0", "100.0", "100.0"]], "shape": {"columns": 26, "rows": 5}}, "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>index</th>\n", "      <th>prompt</th>\n", "      <th>json</th>\n", "      <th>thinking</th>\n", "      <th>difficulty</th>\n", "      <th>generated_json</th>\n", "      <th>llm_score</th>\n", "      <th>json_parsed</th>\n", "      <th>top_level_keys_present</th>\n", "      <th>workflow_name_valid</th>\n", "      <th>...</th>\n", "      <th>num_connections_with_valid_structure</th>\n", "      <th>num_connections_with_valid_target_node</th>\n", "      <th>active_field_boolean</th>\n", "      <th>evaluation_results</th>\n", "      <th>percent_node_with_valid_type</th>\n", "      <th>percent_node_with_valid_version</th>\n", "      <th>percent_node_with_valid_structure</th>\n", "      <th>percent_connections_with_valid_structure</th>\n", "      <th>percent_connections_with_valid_target_node</th>\n", "      <th>percent_node_with_parameters</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>38</td>\n", "      <td>Create a JSON object representing an n8n workf...</td>\n", "      <td>{\"name\": \"Main workflow loop backup\", \"nodes\":...</td>\n", "      <td>Okay, let's design this workflow step-by-step ...</td>\n", "      <td>hard</td>\n", "      <td>{\\n  \"name\": \"Backup Workflows\",\\n  \"nodes\": [...</td>\n", "      <td>10</td>\n", "      <td>1.0</td>\n", "      <td>4.0</td>\n", "      <td>1.0</td>\n", "      <td>...</td>\n", "      <td>7.0</td>\n", "      <td>7.0</td>\n", "      <td>1.0</td>\n", "      <td>None</td>\n", "      <td>100.000000</td>\n", "      <td>100.000000</td>\n", "      <td>100.0</td>\n", "      <td>100.0</td>\n", "      <td>100.0</td>\n", "      <td>100.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>20</td>\n", "      <td>Generate a JSON object for an inactive workflo...</td>\n", "      <td>{\"name\": \"+11 AI-Powered RAG Workflow For Stoc...</td>\n", "      <td>Alright, let's get started on designing this w...</td>\n", "      <td>easy</td>\n", "      <td>{\\n  \"name\": \"+11 AI-Powered RAG Workflow For ...</td>\n", "      <td>10</td>\n", "      <td>1.0</td>\n", "      <td>4.0</td>\n", "      <td>1.0</td>\n", "      <td>...</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>1.0</td>\n", "      <td>None</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>12</td>\n", "      <td>Generate a JSON object for an inactive workflo...</td>\n", "      <td>{\"name\": \"CRAWL dataa QUAN LEN\", \"nodes\": [], ...</td>\n", "      <td>Okay, let's start building this workflow defin...</td>\n", "      <td>easy</td>\n", "      <td>{\\n  \"id\": \"placeholder-id\",\\n  \"versionId\": \"...</td>\n", "      <td>10</td>\n", "      <td>1.0</td>\n", "      <td>4.0</td>\n", "      <td>1.0</td>\n", "      <td>...</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>1.0</td>\n", "      <td>None</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>41</td>\n", "      <td>Generate an n8n workflow that provides daily s...</td>\n", "      <td>{\"name\": \"Trade Recommendations\", \"nodes\": [{\"...</td>\n", "      <td>Alright, let's start designing this workflow f...</td>\n", "      <td>hard</td>\n", "      <td>{\\n  \"name\": \"Daily Stock Trade Recommendation...</td>\n", "      <td>10</td>\n", "      <td>1.0</td>\n", "      <td>4.0</td>\n", "      <td>1.0</td>\n", "      <td>...</td>\n", "      <td>2.0</td>\n", "      <td>2.0</td>\n", "      <td>1.0</td>\n", "      <td>None</td>\n", "      <td>33.333333</td>\n", "      <td>33.333333</td>\n", "      <td>100.0</td>\n", "      <td>100.0</td>\n", "      <td>100.0</td>\n", "      <td>100.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>1</td>\n", "      <td>Generate an active n8n workflow named \"AppTest...</td>\n", "      <td>{\"name\": \"AppTest n8n Onboarding Scaling Autom...</td>\n", "      <td>Okay, let's get this workflow designed and run...</td>\n", "      <td>easy</td>\n", "      <td>{\\n  \"name\": \"AppTest n8n Onboarding Scaling A...</td>\n", "      <td>10</td>\n", "      <td>1.0</td>\n", "      <td>4.0</td>\n", "      <td>1.0</td>\n", "      <td>...</td>\n", "      <td>1.0</td>\n", "      <td>1.0</td>\n", "      <td>1.0</td>\n", "      <td>None</td>\n", "      <td>50.000000</td>\n", "      <td>50.000000</td>\n", "      <td>100.0</td>\n", "      <td>100.0</td>\n", "      <td>100.0</td>\n", "      <td>100.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>5 rows × 26 columns</p>\n", "</div>"], "text/plain": ["   index                                             prompt  \\\n", "0     38  Create a JSON object representing an n8n workf...   \n", "1     20  Generate a JSON object for an inactive workflo...   \n", "2     12  Generate a JSON object for an inactive workflo...   \n", "3     41  Generate an n8n workflow that provides daily s...   \n", "4      1  Generate an active n8n workflow named \"AppTest...   \n", "\n", "                                                json  \\\n", "0  {\"name\": \"Main workflow loop backup\", \"nodes\":...   \n", "1  {\"name\": \"+11 AI-Powered RAG Workflow For Stoc...   \n", "2  {\"name\": \"CRAWL dataa QUAN LEN\", \"nodes\": [], ...   \n", "3  {\"name\": \"Trade Recommendations\", \"nodes\": [{\"...   \n", "4  {\"name\": \"AppTest n8n Onboarding Scaling Autom...   \n", "\n", "                                            thinking difficulty  \\\n", "0  Okay, let's design this workflow step-by-step ...       hard   \n", "1  Alright, let's get started on designing this w...       easy   \n", "2  Okay, let's start building this workflow defin...       easy   \n", "3  Alright, let's start designing this workflow f...       hard   \n", "4  Okay, let's get this workflow designed and run...       easy   \n", "\n", "                                      generated_json  llm_score  json_parsed  \\\n", "0  {\\n  \"name\": \"Backup Workflows\",\\n  \"nodes\": [...         10          1.0   \n", "1  {\\n  \"name\": \"+11 AI-Powered RAG Workflow For ...         10          1.0   \n", "2  {\\n  \"id\": \"placeholder-id\",\\n  \"versionId\": \"...         10          1.0   \n", "3  {\\n  \"name\": \"Daily Stock Trade Recommendation...         10          1.0   \n", "4  {\\n  \"name\": \"AppTest n8n Onboarding Scaling A...         10          1.0   \n", "\n", "   top_level_keys_present  workflow_name_valid  ...  \\\n", "0                     4.0                  1.0  ...   \n", "1                     4.0                  1.0  ...   \n", "2                     4.0                  1.0  ...   \n", "3                     4.0                  1.0  ...   \n", "4                     4.0                  1.0  ...   \n", "\n", "   num_connections_with_valid_structure  \\\n", "0                                   7.0   \n", "1                                   0.0   \n", "2                                   0.0   \n", "3                                   2.0   \n", "4                                   1.0   \n", "\n", "   num_connections_with_valid_target_node  active_field_boolean  \\\n", "0                                     7.0                   1.0   \n", "1                                     0.0                   1.0   \n", "2                                     0.0                   1.0   \n", "3                                     2.0                   1.0   \n", "4                                     1.0                   1.0   \n", "\n", "   evaluation_results  percent_node_with_valid_type  \\\n", "0                None                    100.000000   \n", "1                None                           NaN   \n", "2                None                           NaN   \n", "3                None                     33.333333   \n", "4                None                     50.000000   \n", "\n", "   percent_node_with_valid_version  percent_node_with_valid_structure  \\\n", "0                       100.000000                              100.0   \n", "1                              NaN                                NaN   \n", "2                              NaN                                NaN   \n", "3                        33.333333                              100.0   \n", "4                        50.000000                              100.0   \n", "\n", "   percent_connections_with_valid_structure  \\\n", "0                                     100.0   \n", "1                                       NaN   \n", "2                                       NaN   \n", "3                                     100.0   \n", "4                                     100.0   \n", "\n", "   percent_connections_with_valid_target_node percent_node_with_parameters  \n", "0                                       100.0                        100.0  \n", "1                                         NaN                          NaN  \n", "2                                         NaN                          NaN  \n", "3                                       100.0                        100.0  \n", "4                                       100.0                        100.0  \n", "\n", "[5 rows x 26 columns]"]}, "execution_count": 29, "metadata": {}, "output_type": "execute_result"}], "source": ["df.head()"]}, {"cell_type": "code", "execution_count": 30, "id": "b5a7db31", "metadata": {}, "outputs": [], "source": ["nodes = json.load(open(\"n8n_nodes_list.json\"))"]}, {"cell_type": "code", "execution_count": 31, "id": "072f2dba", "metadata": {}, "outputs": [], "source": ["valid_node_and_version = {}\n", "for node in nodes[\"nodes\"]:\n", "    node_type = node[\"name\"]\n", "    versions = node[\"versions\"]\n", "    valid_node_and_version[node_type] = versions"]}, {"cell_type": "code", "execution_count": 32, "id": "20418b37", "metadata": {}, "outputs": [{"data": {"text/plain": ["np.float64(214.0)"]}, "execution_count": 32, "metadata": {}, "output_type": "execute_result"}], "source": ["df[\"num_nodes\"].sum()"]}, {"cell_type": "code", "execution_count": 33, "id": "dac6684a", "metadata": {}, "outputs": [{"data": {"application/vnd.microsoft.datawrangler.viewer.v0+json": {"columns": [{"name": "index", "rawType": "int64", "type": "integer"}, {"name": "generated_json", "rawType": "object", "type": "string"}], "ref": "dba3e4ee-a7c4-4980-b6dd-3eec32ac0123", "rows": [["0", "{\n  \"name\": \"Backup Workflows\",\n  \"nodes\": [\n    {\n      \"parameters\": {\n        \"cronTime\": \"* 2 * * *\",\n        \"timezone\": \"\",\n        \"start\": false,\n        \"stop\": false,\n        \"inExpressions\": false\n      },\n      \"name\": \"Schedule\",\n      \"type\": \"n8n-nodes-base.cron\",\n      \"typeVersion\": 1,\n      \"position\": [\n        200,\n        300\n      ]\n    },\n    {\n      \"parameters\": {\n        \"url\": \"https://YOUR-N8N-INSTANCE/api/workflows\",\n        \"method\": \"GET\",\n        \"responseFormat\": \"json\",\n        \"simpleHttpRequestOptions\": {}\n      },\n      \"name\": \"Get Workflows\",\n      \"type\": \"n8n-nodes-base.httpRequest\",\n      \"typeVersion\": 1,\n      \"position\": [\n        400,\n        300\n      ]\n    },\n    {\n      \"parameters\": {\n        \"batchSize\": 1\n      },\n      \"name\": \"Loop Workflows\",\n      \"type\": \"n8n-nodes-base.splitInBatches\",\n      \"typeVersion\": 1,\n      \"position\": [\n        600,\n        300\n      ]\n    },\n    {\n      \"parameters\": {\n        \"url\": \"https://YOUR-N8N-INSTANCE/api/workflows/{{$json.id}}\",\n        \"method\": \"GET\",\n        \"responseFormat\": \"json\",\n        \"simpleHttpRequestOptions\": {}\n      },\n      \"name\": \"Get Workflow\",\n      \"type\": \"n8n-nodes-base.httpRequest\",\n      \"typeVersion\": 1,\n      \"position\": [\n        800,\n        300\n      ]\n    },\n    {\n      \"parameters\": {\n        \"functionCode\": \"items[0].json.data = items[0].json;\\nitems[0].binary = { data: Buffer.from(JSON.stringify(items[0].json, null, 2)) };\\nreturn items[0];\"\n      },\n      \"name\": \"Prepare Backup\",\n      \"type\": \"n8n-nodes-base.function\",\n      \"typeVersion\": 1,\n      \"position\": [\n        1000,\n        300\n      ]\n    },\n    {\n      \"parameters\": {\n        \"fileName\": \"workflow-backup-{{$json.id}}.json\",\n        \"binaryPropertyName\": \"data\",\n        \"destination\": \"/tmp\"\n      },\n      \"name\": \"Save Backup\",\n      \"type\": \"n8n-nodes-base.writeBinaryFile\",\n      \"typeVersion\": 1,\n      \"position\": [\n        1200,\n        300\n      ]\n    },\n    {\n      \"parameters\": {\n        \"chatId\": \"{{your_chat_id}}\",\n        \"message\": \"Workflow backup completed for workflow ID {{$json.id}}.\"\n      },\n      \"name\": \"Telegram Notification\",\n      \"type\": \"n8n-nodes-base.telegram\",\n      \"typeVersion\": 1,\n      \"position\": [\n        1400,\n        200\n      ]\n    },\n    {\n      \"parameters\": {\n        \"subject\": \"Workflow Backup Completed\",\n        \"text\": \"Backup has been created for workflow ID {{$json.id}}.\",\n        \"to\": \"{{<EMAIL>}}\",\n        \"from\": \"{{<EMAIL>}}\",\n        \"attachments\": [\n          {\n            \"filename\": \"workflow-backup-{{$json.id}}.json\",\n            \"content\": \"{{$binary.data.base64}}\",\n            \"encoding\": \"base64\"\n          }\n        ]\n      },\n      \"name\": \"Send Email\",\n      \"type\": \"n8n-nodes-base.gmail\",\n      \"typeVersion\": 1,\n      \"position\": [\n        1400,\n        400\n      ]\n    }\n  ],\n  \"connections\": {\n    \"Schedule\": {\n      \"main\": [\n        [\n          {\n            \"node\": \"Get Workflows\",\n            \"type\": \"main\",\n            \"index\": 0\n          }\n        ]\n      ]\n    },\n    \"Get Workflows\": {\n      \"main\": [\n        [\n          {\n            \"node\": \"Loop Workflows\",\n            \"type\": \"main\",\n            \"index\": 0\n          }\n        ]\n      ]\n    },\n    \"Loop Workflows\": {\n      \"main\": [\n        [\n          {\n            \"node\": \"Get Workflow\",\n            \"type\": \"main\",\n            \"index\": 0\n          }\n        ]\n      ]\n    },\n    \"Get Workflow\": {\n      \"main\": [\n        [\n          {\n            \"node\": \"Prepare Backup\",\n            \"type\": \"main\",\n            \"index\": 0\n          }\n        ]\n      ]\n    },\n    \"Prepare Backup\": {\n      \"main\": [\n        [\n          {\n            \"node\": \"Save Backup\",\n            \"type\": \"main\",\n            \"index\": 0\n          }\n        ]\n      ]\n    },\n    \"Save Backup\": {\n      \"main\": [\n        [\n          {\n            \"node\": \"Telegram Notification\",\n            \"type\": \"main\",\n            \"index\": 0\n          },\n          {\n            \"node\": \"Send Email\",\n            \"type\": \"main\",\n            \"index\": 0\n          }\n        ]\n      ]\n    }\n  },\n  \"active\": false\n}"], ["1", "{\n  \"name\": \"+11 AI-Powered RAG Workflow For Stock Earnings Report Analysis\",\n  \"nodes\": [],\n  \"connections\": {},\n  \"active\": false,\n  \"settings\": {\n    \"executionOrder\": \"v1\"\n  },\n  \"triggerCount\": 0\n}"], ["2", "{\n  \"id\": \"placeholder-id\",\n  \"versionId\": \"placeholder-versionId\",\n  \"name\": \"CRAWL dataa QUAN LEN\",\n  \"nodes\": [],\n  \"connections\": {},\n  \"createdAt\": \"2025-03-28T03:54:16.496Z\",\n  \"updatedAt\": \"2025-03-28T03:54:16.496Z\",\n  \"triggerCount\": 0,\n  \"settings\": {\n    \"executionOrder\": \"v1\"\n  },\n  \"meta\": null,\n  \"staticData\": null,\n  \"pinData\": {},\n  \"tags\": [],\n  \"active\": false\n}"], ["3", "{\n  \"name\": \"Daily Stock Trade Recommendations\",\n  \"nodes\": [\n    {\n      \"parameters\": {\n        \"cronExpression\": \"45 9 * * 1-5\",\n        \"timezone\": \"America/New_York\"\n      },\n      \"name\": \"<PERSON><PERSON> Trigger\",\n      \"type\": \"n8n-nodes-base.cron\",\n      \"typeVersion\": 1,\n      \"position\": [\n        0,\n        0\n      ]\n    },\n    {\n      \"parameters\": {\n        \"messages\": [\n          {\n            \"role\": \"user\",\n            \"content\": \"You are a stock market analyst. Provide daily trade recommendations for Microsoft (MSFT), Meta Platforms (META), Nvidia (NVDA), Schwab UDR Dividend ETF (SCHA), Vanguard S&P 500 ETF (VOO), and Vanguard Total Market ETF (VTI). Use NASDAQ, NYSE, NYSE Arca stock analysis tools, and incorporate current news sources. Return recommendations in concise format.\"\n          }\n        ],\n        \"model\": \"claude-3-5-sonnet-20240620\",\n        \"temperature\": 0.7,\n        \"maxTokens\": 500\n      },\n      \"name\": \"Anthropic\",\n      \"type\": \"n8n-nodes-hosted:Anthropic\",\n      \"typeVersion\": 1,\n      \"position\": [\n        200,\n        0\n      ]\n    },\n    {\n      \"parameters\": {\n        \"fromEmail\": \"<EMAIL>\",\n        \"toEmail\": \"<EMAIL>\",\n        \"subject\": \"Daily Stock Trade Recommendations\",\n        \"text\": \"{{ $node[\\\"Anthropic\\\"].json.response }}\"\n      },\n      \"name\": \"Send Email\",\n      \"type\": \"n8n-nodes-base.sendEmail\",\n      \"typeVersion\": 1,\n      \"position\": [\n        400,\n        0\n      ]\n    }\n  ],\n  \"connections\": {\n    \"Cron Trigger\": {\n      \"main\": [\n        [\n          {\n            \"node\": \"Anthropic\",\n            \"type\": \"main\",\n            \"index\": 0\n          }\n        ]\n      ]\n    },\n    \"Anthropic\": {\n      \"main\": [\n        [\n          {\n            \"node\": \"Send Email\",\n            \"type\": \"main\",\n            \"index\": 0\n          }\n        ]\n      ]\n    }\n  },\n  \"active\": false\n}"], ["4", "{\n  \"name\": \"AppTest n8n Onboarding Scaling Automation Solutions\",\n  \"tags\": [\n    \"n8n\",\n    \"Tests\",\n    \"Day5\",\n    \"Onboarding\"\n  ],\n  \"nodes\": [\n    {\n      \"parameters\": {\n        \"url\": \"/onboarding/n8n/tests/ScalingAutomationSolutions\",\n        \"responseFormat\": \"query\",\n        \"responsePropertyName\": \"query\"\n      },\n      \"name\": \"Webhook\",\n      \"type\": \"n8n-nodes-base.webhook\",\n      \"typeVersion\": 1,\n      \"position\": [\n        200,\n        200\n      ]\n    },\n    {\n      \"parameters\": {\n        \"statusCode\": 200,\n        \"response\": \"<html><head><title>Scaling Automation Solutions Quiz</title></head><body><h1>Scaling Automation Solutions Quiz</h1><form action=\\\"https://auto.crm-s.com/webhook/Onboarding/Update\\\" method=\\\"post\\\"><label>Question: What is scaling?</label><input type=\\\"text\\\" name=\\\"answer\\\"><br><button type=\\\"submit\\\">Submit to Update</button></form><form action=\\\"https://auto.crm-s.com/webhook/Onboarding/UpdateTaskTable\\\" method=\\\"post\\\"><label>Task Completion Status:</label><input type=\\\"text\\\" name=\\\"status\\\"><br><button type=\\\"submit\\\">Submit to Update Task Table</button></form></body></html>\"\n      },\n      \"name\": \"Respond Webhook\",\n      \"type\": \"n8n-nodes-base.respondWebhook\",\n      \"typeVersion\": 1,\n      \"position\": [\n        600,\n        200\n      ]\n    }\n  ],\n  \"connections\": {\n    \"Webhook\": {\n      \"main\": [\n        [\n          {\n            \"node\": \"Respond Webhook\",\n            \"type\": \"main\",\n            \"index\": 0\n          }\n        ]\n      ]\n    }\n  },\n  \"active\": true\n}"], ["5", "{\n  \"name\": \"[UDIA] Workflow de prueba\",\n  \"timezone\": \"Europe/Madrid\",\n  \"tags\": [\n    \"👁️‍🗨️Desarrollo\",\n    \"📍UDIA\"\n  ],\n  \"nodes\": [\n    {\n      \"parameters\": {},\n      \"name\": \"Schedule Trigger\",\n      \"type\": \"n8n-nodes-base.scheduleTrigger\",\n      \"typeVersion\": 1,\n      \"position\": [\n        250,\n        25\n      ]\n    }\n  ],\n  \"connections\": {},\n  \"active\": false\n}"], ["6", "{\n  \"name\": \"WooCommerce to Mattermost\",\n  \"nodes\": [\n    {\n      \"parameters\": {},\n      \"name\": \"WooCommerce Trigger\",\n      \"type\": \"n8n-nodes-base.woocommerceWebhook\",\n      \"typeVersion\": 1,\n      \"position\": [200, 300]\n    },\n    {\n      \"parameters\": {\n        \"token\": \"\",\n        \"baseUrl\": \"\",\n        \"channelId\": \"pj1p95ebei8g3ro5p84kxxuuio\",\n        \"message\": \"{{$node[\\\"WooCommerce Trigger\\\"].json[\\\"billing\\\"][\\\"first_name\\\"]}} bought {{$node[\\\"WooCommerce Trigger\\\"].json[\\\"line_items\\\"][0][\\\"name\\\"]}}!\"\n      },\n      \"name\": \"MattermostSend\",\n      \"type\": \"n8n-nodes-base.mattermost\",\n      \"typeVersion\": 1,\n      \"position\": [400, 300]\n    }\n  ],\n  \"connections\": {\n    \"WooCommerce Trigger\": {\n      \"main\": [\n        [\n          {\n            \"node\": \"MattermostSend\",\n            \"type\": \"main\",\n            \"index\": 0\n          }\n        ]\n      ]\n    }\n  },\n  \"active\": false\n}"], ["7", "{\n  \"name\": \"Stock Earnings Agent Workflow\",\n  \"nodes\": [\n    {\n      \"parameters\": {\n        \"httpMethod\": \"POST\",\n        \"path\": \"/\",\n        \"responseMode\": \"json\",\n        \"propertyNameToAccess\": \"\"\n      },\n      \"name\": \"Webhook Input\",\n      \"type\": \"n8n-nodes-base.httpRequest\",\n      \"typeVersion\": 1,\n      \"position\": [\n        200,\n        200\n      ]\n    },\n    {\n      \"parameters\": {\n        \"model\": \"claude-3-5-sonnet-20240229\",\n        \"messages\": [\n          {\n            \"role\": \"system\",\n            \"content\": \"You are a helpful assistant that answers questions about stock earnings and financial metrics. You can use the provided APIs for financial metrics, earnings reports/technical analysis, and option strategies. Provide concise and accurate answers based on the data.\"\n          },\n          {\n            \"role\": \"user\",\n            \"content\": \"={{$json[\\\"question\\\"]}}\"\n          }\n        ],\n        \"temperature\": 0,\n        \"maxTokens\": 500,\n        \"topP\": 1,\n        \"stop\": []\n      },\n      \"name\": \"Anthropic Chat\",\n      \"type\": \"n8n-nodes-base.anthropicChat\",\n      \"typeVersion\": 1,\n      \"position\": [\n        400,\n        200\n      ]\n    },\n    {\n      \"parameters\": {\n        \"url\": \"https://api.financialmetrics.com/v1/metrics\",\n        \"method\": \"GET\",\n        \"responseFormat\": \"json\",\n        \"queryParameters\": {\n          \"symbol\": \"={{$json[\\\"symbol\\\"]}}\"\n        }\n      },\n      \"name\": \"Financial Metrics API\",\n      \"type\": \"n8n-nodes-base.httpRequest\",\n      \"typeVersion\": 1,\n      \"position\": [\n        600,\n        200\n      ]\n    },\n    {\n      \"parameters\": {\n        \"url\": \"https://api.flowiseassistant.com/v1/earnings\",\n        \"method\": \"GET\",\n        \"responseFormat\": \"json\",\n        \"queryParameters\": {\n          \"symbol\": \"={{$json[\\\"symbol\\\"]}}\"\n        }\n      },\n      \"name\": \"Flowise Assistant API\",\n      \"type\": \"n8n-nodes-base.httpRequest\",\n      \"typeVersion\": 1,\n      \"position\": [\n        800,\n        200\n      ]\n    },\n    {\n      \"parameters\": {\n        \"url\": \"https://api.optionstrategy.com/v1/strategy\",\n        \"method\": \"GET\",\n        \"responseFormat\": \"json\",\n        \"queryParameters\": {\n          \"symbol\": \"={{$json[\\\"symbol\\\"]}}\",\n          \"strategy\": \"call\"\n        }\n      },\n      \"name\": \"Option Strategy API\",\n      \"type\": \"n8n-nodes-base.httpRequest\",\n      \"typeVersion\": 1,\n      \"position\": [\n        1000,\n        200\n      ]\n    },\n    {\n      \"parameters\": {\n        \"values\": {\n          \"finalResponse\": \"={{$json[\\\"Anthropic Chat\\\"][\\\"output\\\"]}}\"\n        }\n      },\n      \"name\": \"Final Response\",\n      \"type\": \"n8n-nodes-base.set\",\n      \"typeVersion\": 1,\n      \"position\": [\n        1200,\n        200\n      ]\n    }\n  ],\n  \"connections\": {\n    \"Webhook Input\": {\n      \"main\": [\n        [\n          {\n            \"node\": \"Anthropic Chat\",\n            \"type\": \"main\",\n            \"index\": 0\n          }\n        ]\n      ]\n    },\n    \"Anthropic Chat\": {\n      \"main\": [\n        [\n          {\n            \"node\": \"Financial Metrics API\",\n            \"type\": \"main\",\n            \"index\": 0\n          }\n        ]\n      ]\n    },\n    \"Financial Metrics API\": {\n      \"main\": [\n        [\n          {\n            \"node\": \"Flowise Assistant API\",\n            \"type\": \"main\",\n            \"index\": 0\n          }\n        ]\n      ]\n    },\n    \"Flowise Assistant API\": {\n      \"main\": [\n        [\n          {\n            \"node\": \"Option Strategy API\",\n            \"type\": \"main\",\n            \"index\": 0\n          }\n        ]\n      ]\n    },\n    \"Option Strategy API\": {\n      \"main\": [\n        [\n          {\n            \"node\": \"Final Response\",\n            \"type\": \"main\",\n            \"index\": 0\n          }\n        ]\n      ]\n    }\n  },\n  \"active\": false\n}"], ["8", ""], ["9", "{\n  \"name\": \"Agent:Tools:OpenAI\",\n  \"nodes\": [\n    {\n      \"parameters\": {\n        \"httpMethod\": \"POST\",\n        \"path\": \"chat\",\n        \"responseMode\": \"responseAndContinue\",\n        \"responseDataKey\": \"\",\n        \"responseStaticData\": {},\n        \"options\": {}\n      },\n      \"name\": \"Webhook Trigger\",\n      \"type\": \"n8n-nodes-base.webhook\",\n      \"typeVersion\": 1,\n      \"position\": [150, 100]\n    },\n    {\n      \"parameters\": {\n        \"values\": {\n          \"string\": [\n            {\n              \"name\": \"memory\",\n              \"value\": \"\\\"\\\"\"\n            },\n            {\n              \"name\": \"userMessage\",\n              \"value\": \"{{$json[\\\"body\\\"][\\\"message\\\"]}}\"\n            }\n          ]\n        },\n        \"options\": {}\n      },\n      \"name\": \"Initialize Memory\",\n      \"type\": \"n8n-nodes-base.set\",\n      \"typeVersion\": 1,\n      \"position\": [350, 100]\n    },\n    {\n      \"parameters\": {\n        \"sourceData\": \"={{[{\\\"role\\\":\\\"system\\\",\\\"content\\\":\\\"You are a helpful AI agent.\\\"},{\\\"role\\\":\\\"user\\\",\\\"content\\\": $json['userMessage']}]}\",\n        \"options\": {}\n      },\n      \"name\": \"Prepare Messages\",\n      \"type\": \"n8n-nodes-base.function\",\n      \"typeVersion\": 1,\n      \"position\": [550, 100]\n    },\n    {\n      \"parameters\": {\n        \"model\": \"gpt-4o-mini\",\n        \"messages\": \"={{$json['sourceData']}}\",\n        \"temperature\": 0.7,\n        \"maxTokens\": 800,\n        \"topP\": 1,\n        \"frequencyPenalty\": 0,\n        \"presencePenalty\": 0,\n        \"n\": 1,\n        \"stop\": null,\n        \"responseFormat\": null,\n        \"options\": {}\n      },\n      \"name\": \"OpenAI Chat\",\n      \"type\": \"n8n-nodes-base.openai\",\n      \"typeVersion\": 1,\n      \"position\": [750, 100]\n    },\n    {\n      \"parameters\": {\n        \"sourceData\": \"={{{ \\\"tool_name\\\": \\\"custom_tool\\\", \\\"arguments\\\": {\\\"query\\\":\\\"$json['model']\\\"}, \\\"output\\\": {\\\"status\\\":\\\"success\\\",\\\"data\\\":\\\"Processed result\\\"} }}}\"\n      },\n      \"name\": \"Custom Tool\",\n      \"type\": \"n8n-nodes-base.code\",\n      \"typeVersion\": 1,\n      \"position\": [950, 100]\n    },\n    {\n      \"parameters\": {\n        \"url\": \"https://example.com/api\",\n        \"method\": \"GET\",\n        \"responseFormat\": \"json\",\n        \"responseDataKey\": \"\",\n        \"options\": {}\n      },\n      \"name\": \"HTTP Request Tool\",\n      \"type\": \"n8n-nodes-base.httpRequest\",\n      \"typeVersion\": 1,\n      \"position\": [1150, 100]\n    },\n    {\n      \"parameters\": {\n        \"values\": {\n          \"string\": [\n            {\n              \"name\": \"finalAnswer\",\n              \"value\": \"\\\"User query answered with tools.\\\"\"\n            }\n          ]\n        },\n        \"options\": {}\n      },\n      \"name\": \"Finalize Response\",\n      \"type\": \"n8n-nodes-base.set\",\n      \"typeVersion\": 1,\n      \"position\": [1350, 100]\n    }\n  ],\n  \"connections\": {\n    \"Webhook Trigger\": {\n      \"main\": [\n        [\n          {\n            \"node\": \"Initialize Memory\",\n            \"type\": \"main\",\n            \"index\": 0\n          }\n        ]\n      ]\n    },\n    \"Initialize Memory\": {\n      \"main\": [\n        [\n          {\n            \"node\": \"Prepare Messages\",\n            \"type\": \"main\",\n            \"index\": 0\n          }\n        ]\n      ]\n    },\n    \"Prepare Messages\": {\n      \"main\": [\n        [\n          {\n            \"node\": \"OpenAI Chat\",\n            \"type\": \"main\",\n            \"index\": 0\n          }\n        ]\n      ]\n    },\n    \"OpenAI Chat\": {\n      \"main\": [\n        [\n          {\n            \"node\": \"Custom Tool\",\n            \"type\": \"main\",\n            \"index\": 0\n          }\n        ]\n      ]\n    },\n    \"Custom Tool\": {\n      \"main\": [\n        [\n          {\n            \"node\": \"HTTP Request Tool\",\n            \"type\": \"main\",\n            \"index\": 0\n          }\n        ]\n      ]\n    },\n    \"HTTP Request Tool\": {\n      \"main\": [\n        [\n          {\n            \"node\": \"Finalize Response\",\n            \"type\": \"main\",\n            \"index\": 0\n          }\n        ]\n      ]\n    }\n  },\n  \"active\": false\n}"], ["10", "{\n  \"name\": \"CRC Token Webhook Workflow\",\n  \"nodes\": [\n    {\n      \"parameters\": {\n        \"httpMethod\": \"GET\",\n        \"path\": \"crc-webhook\",\n        \"responseMode\": \"response\"\n      },\n      \"name\": \"Webhook Input\",\n      \"type\": \"n8n-nodes-base.webhook\",\n      \"typeVersion\": 1,\n      \"position\": [\n        200,\n        200\n      ]\n    },\n    {\n      \"parameters\": {\n        \"operation\": \"hmac\",\n        \"hash\": \"sha256\",\n        \"text\": \"={{ $json[\\\"queryParams\\\"][\\\"crc_token\\\"] }}\",\n        \"key\": \"API KEY SECRET\",\n        \"outputEncoding\": \"base64\"\n      },\n      \"name\": \"Crypto HMAC\",\n      \"type\": \"n8n-nodes-base.crypto\",\n      \"typeVersion\": 1,\n      \"position\": [\n        400,\n        200\n      ]\n    },\n    {\n      \"parameters\": {\n        \"values\": {\n          \"string\": [\n            {\n              \"name\": \"response_token\",\n              \"value\": \"'sha256=' + $json[\\\"output\\\"]\"\n            }\n          ]\n        },\n        \"options\": {\n          \"keepOnlySet\": true\n        }\n      },\n      \"name\": \"Set Response Token\",\n      \"type\": \"n8n-nodes-base.set\",\n      \"typeVersion\": 1,\n      \"position\": [\n        600,\n        200\n      ]\n    }\n  ],\n  \"connections\": {\n    \"Webhook Input\": {\n      \"main\": [\n        [\n          {\n            \"node\": \"Crypto HMAC\",\n            \"type\": \"main\",\n            \"index\": 0\n          }\n        ]\n      ]\n    },\n    \"Crypto HMAC\": {\n      \"main\": [\n        [\n          {\n            \"node\": \"Set Response Token\",\n            \"type\": \"main\",\n            \"index\": 0\n          }\n        ]\n      ]\n    }\n  },\n  \"active\": false\n}"], ["11", ""], ["12", "{\n  \"name\": \"Contact Agent\",\n  \"nodes\": [\n    {\n      \"parameters\": {\n        \"path\": \"contact-agent\",\n        \"httpMethod\": \"POST\"\n      },\n      \"name\": \"Webhook Trigger\",\n      \"type\": \"n8n-nodes-base.webhook\",\n      \"typeVersion\": 1,\n      \"position\": [\n        0,\n        0\n      ]\n    },\n    {\n      \"parameters\": {\n        \"model\": \"gpt-3.5-turbo\",\n        \"messages\": [\n          {\n            \"role\": \"user\",\n            \"content\": \"{{$json.query}}\"\n          }\n        ]\n      },\n      \"name\": \"OpenAI Query\",\n      \"type\": \"n8n-nodes-base.openai\",\n      \"typeVersion\": 1,\n      \"position\": [\n        200,\n        0\n      ]\n    },\n    {\n      \"parameters\": {\n        \"operation\": \"upsert\",\n        \"baseId\": \"appXXXXX\",\n        \"tableName\": \"Contacts\",\n        \"upsertFieldName\": \"Name\",\n        \"records\": [\n          {\n            \"fields\": {\n              \"Name\": \"{{$json['name']}}\",\n              \"Info\": \"{{$json['info']}}\"\n            }\n          }\n        ]\n      },\n      \"name\": \"Airtable upsert\",\n      \"type\": \"n8n-nodes-base.airtable\",\n      \"typeVersion\": 1,\n      \"position\": [\n        400,\n        0\n      ]\n    },\n    {\n      \"parameters\": {\n        \"conversationVariables\": \"{{$json}}\",\n        \"agentPromptTemplate\": \"Use the contact info to provide an appropriate response.\",\n        \"model\": \"gpt-3.5-turbo\"\n      },\n      \"name\": \"LangChain Agent\",\n      \"type\": \"n8n-nodes-base.langchain\",\n      \"typeVersion\": 1,\n      \"position\": [\n        600,\n        0\n      ]\n    },\n    {\n      \"parameters\": {},\n      \"name\": \"Error Trigger\",\n      \"type\": \"n8n-nodes-base.errorTrigger\",\n      \"typeVersion\": 1,\n      \"position\": [\n        400,\n        -200\n      ]\n    }\n  ],\n  \"connections\": {\n    \"Webhook Trigger\": {\n      \"main\": [\n        [\n          {\n            \"node\": \"OpenAI Query\",\n            \"type\": \"main\",\n            \"index\": 0\n          }\n        ]\n      ]\n    },\n    \"OpenAI Query\": {\n      \"main\": [\n        [\n          {\n            \"node\": \"Airtable upsert\",\n            \"type\": \"main\",\n            \"index\": 0\n          }\n        ]\n      ]\n    },\n    \"Airtable upsert\": {\n      \"main\": [\n        [\n          {\n            \"node\": \"LangChain Agent\",\n            \"type\": \"main\",\n            \"index\": 0\n          }\n        ]\n      ]\n    }\n  },\n  \"active\": false\n}"], ["13", "{\n  \"name\": \"Parallel Code to Set Workflow\",\n  \"nodes\": [\n    {\n      \"parameters\": {\n        \"httpMethod\": [\n          \"POST\"\n        ],\n        \"path\": \"webhook\"\n      },\n      \"name\": \"Webhook\",\n      \"type\": \"n8n-nodes-base.webhook\",\n      \"typeVersion\": 1,\n      \"position\": [\n        0,\n        0\n      ]\n    },\n    {\n      \"parameters\": {\n        \"mode\": \"node\",\n        \"code\": \"const items = [];\\nfor (let i = 0; i < 5; i++) {\\n  items.push({ json: {} });\\n}\\nreturn items;\"\n      },\n      \"name\": \"Code1\",\n      \"type\": \"n8n-nodes-base.code\",\n      \"typeVersion\": 1,\n      \"position\": [\n        200,\n        0\n      ]\n    },\n    {\n      \"parameters\": {\n        \"mode\": \"node\",\n        \"code\": \"const items = [];\\nfor (let i = 0; i < 5; i++) {\\n  items.push({ json: {} });\\n}\\nreturn items;\"\n      },\n      \"name\": \"Code2\",\n      \"type\": \"n8n-nodes-base.code\",\n      \"typeVersion\": 1,\n      \"position\": [\n        200,\n        200\n      ]\n    },\n    {\n      \"parameters\": {\n        \"values\": []\n      },\n      \"name\": \"Set1\",\n      \"type\": \"n8n-nodes-base.set\",\n      \"typeVersion\": 1,\n      \"position\": [\n        400,\n        0\n      ]\n    },\n    {\n      \"parameters\": {\n        \"values\": []\n      },\n      \"name\": \"Set2\",\n      \"type\": \"n8n-nodes-base.set\",\n      \"typeVersion\": 1,\n      \"position\": [\n        400,\n        200\n      ]\n    }\n  ],\n  \"connections\": {\n    \"Webhook\": {\n      \"main\": [\n        [\n          {\n            \"node\": \"Code1\",\n            \"type\": \"main\",\n            \"index\": 0\n          },\n          {\n            \"node\": \"Code2\",\n            \"type\": \"main\",\n            \"index\": 0\n          }\n        ]\n      ]\n    },\n    \"Code1\": {\n      \"main\": [\n        [\n          {\n            \"node\": \"Set1\",\n            \"type\": \"main\",\n            \"index\": 0\n          }\n        ]\n      ]\n    },\n    \"Code2\": {\n      \"main\": [\n        [\n          {\n            \"node\": \"Set2\",\n            \"type\": \"main\",\n            \"index\": 0\n          }\n        ]\n      ]\n    }\n  },\n  \"active\": false\n}"], ["14", "{\n  \"name\": \"Manual to TravisCI Trigger\",\n  \"nodes\": [\n    {\n      \"parameters\": {},\n      \"name\": \"Manual Trigger\",\n      \"type\": \"n8n-nodes-base.manualTrigger\",\n      \"typeVersion\": 1,\n      \"position\": [\n        250,\n        300\n      ]\n    },\n    {\n      \"parameters\": {\n        \"operation\": \"trigger\"\n      },\n      \"name\": \"TravisCI Trigger\",\n      \"type\": \"n8n-nodes-base.travisci\",\n      \"typeVersion\": 1,\n      \"position\": [\n        450,\n        300\n      ]\n    }\n  ],\n  \"connections\": {\n    \"Manual Trigger\": {\n      \"main\": [\n        [\n          {\n            \"node\": \"TravisCI Trigger\",\n            \"type\": \"main\",\n            \"index\": 0\n          }\n        ]\n      ]\n    }\n  },\n  \"active\": false\n}"], ["15", ""], ["16", ""], ["17", "{\n  \"name\": \"Email with attachment workflow\",\n  \"nodes\": [\n    {\n      \"parameters\": {\n        \"httpMethod\": \"POST\",\n        \"path\": \"email-webhook\"\n      },\n      \"name\": \"Webhook Trigger\",\n      \"type\": \"n8n-nodes-base.httpRequest\",\n      \"typeVersion\": 1,\n      \"position\": [\n        0,\n        0\n      ]\n    },\n    {\n      \"parameters\": {\n        \"to\": \"<EMAIL>\",\n        \"subject\": \"Test Email with Attachment\",\n        \"text\": \"This is a test email sent from n8n.\",\n        \"attachments\": [\n          {\n            \"name\": \"attachment.txt\",\n            \"data\": \"={{ base64Encode(\\\"Sample attachment content.\\\") }}\",\n            \"mime\": \"text/plain\"\n          }\n        ]\n      },\n      \"name\": \"Send Email\",\n      \"type\": \"n8n-nodes-base.emailSend\",\n      \"typeVersion\": 1,\n      \"position\": [\n        250,\n        0\n      ]\n    }\n  ],\n  \"connections\": {\n    \"Webhook Trigger\": {\n      \"main\": [\n        [\n          {\n            \"node\": \"Send Email\",\n            \"type\": \"main\",\n            \"index\": 0\n          }\n        ]\n      ]\n    }\n  },\n  \"active\": false,\n  \"createdAt\": \"2025-01-10T00:00:00Z\",\n  \"updatedAt\": \"2025-01-10T00:00:00Z\",\n  \"executionOrder\": \"v1\",\n  \"id\": \"c99e71a3-57ac-4d0e-9d2a-52ef2b69d5ca\",\n  \"versionId\": \"d4f6c8e1-2ca3-4b9f-b9c1-7b3e9a4f9d9f\"\n}"], ["18", "{\n  \"name\": \"Tutorial n8n - nocodb\",\n  \"nodes\": [\n    {\n      \"parameters\": {\n      },\n      \"name\": \"Manual Trigger\",\n      \"type\": \"n8n-nodes-base.manualTrigger\",\n      \"typeVersion\": 1,\n      \"position\": [\n        0,\n        200\n      ]\n    },\n    {\n      \"parameters\": {\n        \"operation\": \"Get All\",\n        \"resource\": \"Table\",\n        \"projectId\": \"pcdrdmidko5oz5y\",\n        \"tableName\": \"idtable\",\n        \"filter\": {\n          \"estado\": \"dormido\"\n        }\n      },\n      \"name\": \"Read NocoDB Records\",\n      \"type\": \"nocoDb\",\n      \"typeVersion\": 1,\n      \"position\": [\n        200,\n        200\n      ]\n    },\n    {\n      \"parameters\": {\n        \"operation\": \"Update All\",\n        \"resource\": \"Table\",\n        \"projectId\": \"idproyecto\",\n        \"tableName\": \"gguidtable\",\n        \"records\": [\n          {\n            \"Id\": \"{{ $json.Id }}\",\n            \"texto\": \"es una prueba\"\n          }\n        ]\n      },\n      \"name\": \"Update NocoDB Records\",\n      \"type\": \"nocoDb\",\n      \"typeVersion\": 1,\n      \"position\": [\n        400,\n        200\n      ]\n    }\n  ],\n  \"connections\": {\n    \"Manual Trigger\": {\n      \"main\": [\n        [\n          {\n            \"node\": \"Read NocoDB Records\",\n            \"type\": \"main\",\n            \"index\": 0\n          }\n        ]\n      ]\n    },\n    \"Read NocoDB Records\": {\n      \"main\": [\n        [\n          {\n            \"node\": \"Update NocoDB Records\",\n            \"type\": \"main\",\n            \"index\": 0\n          }\n        ]\n      ]\n    }\n  },\n  \"active\": false\n}"], ["19", "{\n  \"name\": \"Redis Status Workflow\",\n  \"nodes\": [\n    {\n      \"parameters\": {\n        \"cron\": [\n          {\n            \"timezone\": \"\",\n            \"pattern\": \"*/1 * * * *\"\n          }\n        ],\n        \"timeZone\": \"\"\n      },\n      \"name\": \"ScheduleTrigger\",\n      \"type\": \"n8n-nodes-base.scheduleTrigger\",\n      \"typeVersion\": 1,\n      \"position\": [\n        200,\n        100\n      ]\n    },\n    {\n      \"parameters\": {\n        \"type\": \"GET\",\n        \"host\": \"localhost\",\n        \"port\": 6379,\n        \"ssl\": false,\n        \"authentication\": {\n          \"type\": \"NONE\",\n          \"user\": \"\",\n          \"password\": \"\"\n        },\n        \"key\": \"workflow_status\"\n      },\n      \"name\": \"GetStatus\",\n      \"type\": \"n8n-nodes-base.redis\",\n      \"typeVersion\": 1,\n      \"position\": [\n        400,\n        100\n      ]\n    },\n    {\n      \"parameters\": {\n        \"conditions\": [\n          {\n            \"operation\": \"equal\",\n            \"value1\": \"={{$json[\\\"data\\\"]}}\",\n            \"value2\": \"idle\"\n          }\n        ],\n        \"output\": \"outputItem\"\n      },\n      \"name\": \"CheckIdle\",\n      \"type\": \"n8n-nodes-base.if\",\n      \"typeVersion\": 1,\n      \"position\": [\n        600,\n        100\n      ]\n    },\n    {\n      \"parameters\": {\n        \"type\": \"SET\",\n        \"host\": \"localhost\",\n        \"port\": 6379,\n        \"ssl\": false,\n        \"authentication\": {\n          \"type\": \"NONE\",\n          \"user\": \"\",\n          \"password\": \"\"\n        },\n        \"key\": \"workflow_status\",\n        \"value\": \"running\"\n      },\n      \"name\": \"SetRunning\",\n      \"type\": \"n8n-nodes-base.redis\",\n      \"typeVersion\": 1,\n      \"position\": [\n        800,\n        50\n      ]\n    },\n    {\n      \"parameters\": {\n        \"authentication\": \"none\",\n        \"method\": \"POST\",\n        \"url\": \"https://example.com/api/trigger\",\n        \"headers\": {}\n      },\n      \"name\": \"ExecuteWorkflow\",\n      \"type\": \"n8n-nodes-base.httpRequest\",\n      \"typeVersion\": 1,\n      \"position\": [\n        1100,\n        50\n      ]\n    },\n    {\n      \"parameters\": {\n        \"type\": \"SET\",\n        \"host\": \"localhost\",\n        \"port\": 6379,\n        \"ssl\": false,\n        \"authentication\": {\n          \"type\": \"NONE\",\n          \"user\": \"\",\n          \"password\": \"\"\n        },\n        \"key\": \"workflow_status\",\n        \"value\": \"idle\"\n      },\n      \"name\": \"SetIdle\",\n      \"type\": \"n8n-nodes-base.redis\",\n      \"typeVersion\": 1,\n      \"position\": [\n        1400,\n        50\n      ]\n    },\n    {\n      \"parameters\": {},\n      \"name\": \"ErrorTrigger\",\n      \"type\": \"n8n-nodes-base.errorTrigger\",\n      \"typeVersion\": 1,\n      \"position\": [\n        1400,\n        200\n      ]\n    },\n    {\n      \"parameters\": {\n        \"type\": \"SET\",\n        \"host\": \"localhost\",\n        \"port\": 6379,\n        \"ssl\": false,\n        \"authentication\": {\n          \"type\": \"NONE\",\n          \"user\": \"\",\n          \"password\": \"\"\n        },\n        \"key\": \"workflow_status\",\n        \"value\": \"idle\"\n      },\n      \"name\": \"ResetFlag\",\n      \"type\": \"n8n-nodes-base.redis\",\n      \"typeVersion\": 1,\n      \"position\": [\n        1700,\n        200\n      ]\n    }\n  ],\n  \"connections\": {\n    \"ScheduleTrigger\": {\n      \"main\": [\n        [\n          {\n            \"node\": \"GetStatus\",\n            \"type\": \"main\",\n            \"index\": 0\n          }\n        ]\n      ]\n    },\n    \"GetStatus\": {\n      \"main\": [\n        [\n          {\n            \"node\": \"CheckIdle\",\n            \"type\": \"main\",\n            \"index\": 0\n          }\n        ]\n      ]\n    },\n    \"CheckIdle\": {\n      \"main\": [\n        [\n          {\n            \"node\": \"SetRunning\",\n            \"type\": \"main\",\n            \"index\": 0\n          }\n        ]\n      ]\n    },\n    \"SetRunning\": {\n      \"main\": [\n        [\n          {\n            \"node\": \"ExecuteWorkflow\",\n            \"type\": \"main\",\n            \"index\": 0\n          }\n        ]\n      ]\n    },\n    \"ExecuteWorkflow\": {\n      \"main\": [\n        [\n          {\n            \"node\": \"SetIdle\",\n            \"type\": \"main\",\n            \"index\": 0\n          }\n        ]\n      ]\n    },\n    \"ErrorTrigger\": {\n      \"main\": [\n        [\n          {\n            \"node\": \"ResetFlag\",\n            \"type\": \"main\",\n            \"index\": 0\n          }\n        ]\n      ]\n    }\n  },\n  \"active\": false\n}"], ["20", "{\n  \"name\": \"Weather Query Workflow\",\n  \"nodes\": [\n    {\n      \"parameters\": {\n        \"httpMethod\": \"POST\",\n        \"responseMode\": \"last\",\n        \"path\": \"parse\"\n      },\n      \"name\": \"Webhook Trigger\",\n      \"type\": \"n8n-nodes-base.webhook\",\n      \"typeVersion\": 1,\n      \"position\": [200, 200]\n    },\n    {\n      \"parameters\": {\n        \"model\": \"gpt-4o-mini\",\n        \"messages\": [\n          {\n            \"role\": \"user\",\n            \"content\": \"Extract city, state, latitude, longitude, and Length (Single or Multiple) from the following query and return a JSON object with these fields: {{$json.query}}\"\n          }\n        ],\n        \"responseFormat\": \"json\",\n        \"outputProperty\": \"json\"\n      },\n      \"name\": \"GPT OpenAI\",\n      \"type\": \"n8n-nodes-base.openAiChat\",\n      \"typeVersion\": 1,\n      \"position\": [400, 200]\n    },\n    {\n      \"parameters\": {\n        \"conditions\": [\n          {\n            \"value1\": \"{{$json.Length}}\",\n            \"operation\": \"equals\",\n            \"value2\": \"Multiple\"\n          }\n        ]\n      },\n      \"name\": \"If Length\",\n      \"type\": \"n8n-nodes-base.if\",\n      \"typeVersion\": 1,\n      \"position\": [600, 200]\n    },\n    {\n      \"parameters\": {\n        \"operation\": \"forecast\",\n        \"units\": \"imperial\",\n        \"lat\": \"{{$json.latitude}}\",\n        \"lon\": \"{{$json.longitude}}\"\n      },\n      \"name\": \"OWM 5-day Forecast\",\n      \"type\": \"n8n-nodes-base.openWeatherMap\",\n      \"typeVersion\": 1,\n      \"position\": [800, 100]\n    },\n    {\n      \"parameters\": {\n        \"operation\": \"current\",\n        \"units\": \"imperial\",\n        \"lat\": \"{{$json.latitude}}\",\n        \"lon\": \"{{$json.longitude}}\"\n      },\n      \"name\": \"OWM Current\",\n      \"type\": \"n8n-nodes-base.openWeatherMap\",\n      \"typeVersion\": 1,\n      \"position\": [800, 300]\n    }\n  ],\n  \"connections\": {\n    \"Webhook Trigger\": {\n      \"main\": [\n        [\n          {\n            \"node\": \"GPT OpenAI\",\n            \"type\": \"main\",\n            \"index\": 0\n          }\n        ]\n      ]\n    },\n    \"GPT OpenAI\": {\n      \"main\": [\n        [\n          {\n            \"node\": \"If Length\",\n            \"type\": \"main\",\n            \"index\": 0\n          }\n        ]\n      ]\n    },\n    \"If Length\": {\n      \"main\": [\n        [\n          {\n            \"node\": \"OWM 5-day Forecast\",\n            \"type\": \"main\",\n            \"index\": 0\n          }\n        ],\n        [\n          {\n            \"node\": \"OWM Current\",\n            \"type\": \"main\",\n            \"index\": 0\n          }\n        ]\n      ]\n    }\n  },\n  \"active\": false\n}"], ["21", "{\n  \"name\": \"YouTube Channel Analysis Workflow\",\n  \"nodes\": [\n    {\n      \"parameters\": {\n        \"httpMethod\": \"POST\",\n        \"path\": \"youtube-analysis\",\n        \"options\": {\n          \"responseMode\": \"jsonString\"\n        },\n        \"responseData\": \"={{ $json.payload }}\",\n        \"responseHeaderName\": \"\",\n        \"responseHeaderValue\": \"\"\n      },\n      \"name\": \"Webhook Trigger\",\n      \"type\": \"n8n-nodes-base.n8nWebhook\",\n      \"typeVersion\": 1,\n      \"position\": [\n        200,\n        100\n      ]\n    },\n    {\n      \"parameters\": {\n        \"values\": {\n          \"active\": [\n            {\n              \"name\": \"channelUrl\",\n              \"value\": \"={{ $json.channelUrl }}\"\n            }\n          ],\n          \"options\": [\n            {\n              \"name\": \"channelId\",\n              \"value\": \"={{ $json.channelUrl.match(/channel\\\\/(?:[^\\\\/]+)\\\\/([^\\\\/]+)/) ? $json.channelUrl.match(/channel\\\\/(?:[^\\\\/]+)\\\\/([^\\\\/]+)/)[1] : '' }}\"\n            }\n          ]\n        },\n        \"options\": {\n          \"keepOnlySet\": true,\n          \"addToOutput\": true\n        }\n      },\n      \"name\": \"Set – Parse URL\",\n      \"type\": \"n8n-nodes-base.set\",\n      \"typeVersion\": 1,\n      \"position\": [\n        400,\n        100\n      ]\n    },\n    {\n      \"parameters\": {\n        \"url\": \"https://www.googleapis.com/youtube/v3/channels\",\n        \"queryParametersUi\": {\n          \"parameter\": [\n            {\n              \"name\": \"part\",\n              \"value\": \"snippet,statistics,contentDetails\",\n              \"type\": \"string\"\n            },\n            {\n              \"name\": \"id\",\n              \"value\": \"={{ $json.channelId }}\",\n              \"type\": \"string\"\n            },\n            {\n              \"name\": \"key\",\n              \"value\": \"={{ $env.YOUTUBE_API_KEY }}\",\n              \"type\": \"string\"\n            }\n          ]\n        },\n        \"options\": {\n          \"headersParameters\": {\n            \"Content-Type\": \"application/json\"\n          },\n          \"bodyParametersUi\": {\n            \"parameter\": []\n          },\n          \"responseFormat\": \"nameAsPrefix\"\n        },\n        \"authentication\": \"none\"\n      },\n      \"name\": \"Get Channel Details\",\n      \"type\": \"n8n-nodes-base.httpRequest\",\n      \"typeVersion\": 1,\n      \"position\": [\n        600,\n        100\n      ]\n    },\n    {\n      \"parameters\": {\n        \"functionCode\": \"const channel= items[0].json.items[0];\\nreturn [{json: {\\n  channelTitle: channel.snippet.title,\\n  channelId: channel.id,\\n  uploadsPlaylistId: channel.contentDetails.relatedPlaylists.uploads,\\n  viewCount: parseInt(channel.statistics.viewCount,10),\\n  subscriberCount: parseInt(channel.statistics.subscriberCount,10),\\n  videoCount: parseInt(channel.statistics.videoCount,10)\\n}}];\"\n      },\n      \"name\": \"Extract Upload Playlist ID\",\n      \"type\": \"n8n-nodes-base.function\",\n      \"typeVersion\": 1,\n      \"position\": [\n        800,\n        100\n      ]\n    },\n    {\n      \"parameters\": {\n        \"url\": \"https://www.googleapis.com/youtube/v3/playlistItems\",\n        \"queryParametersUi\": {\n          \"parameter\": [\n            {\n              \"name\": \"part\",\n              \"value\": \"snippet\",\n              \"type\": \"string\"\n            },\n            {\n              \"name\": \"maxResults\",\n              \"value\": \"50\",\n              \"type\": \"string\"\n            },\n            {\n              \"name\": \"playlistId\",\n              \"value\": \"={{ $json.uploadsPlaylistId }}\",\n              \"type\": \"string\"\n            },\n            {\n              \"name\": \"key\",\n              \"value\": \"={{ $env.YOUTUBE_API_KEY }}\",\n              \"type\": \"string\"\n            }\n          ]\n        },\n        \"options\": {\n          \"headersParameters\": {\n            \"Content-Type\": \"application/json\"\n          },\n          \"responseFormat\": \"jsonList\"\n        },\n        \"authentication\": \"none\"\n      },\n      \"name\": \"Get Uploads\",\n      \"type\": \"n8n-nodes-base.httpRequest\",\n      \"typeVersion\": 1,\n      \"position\": [\n        1000,\n        100\n      ]\n    },\n    {\n      \"parameters\": {\n        \"functionCode\": \"// Extract view counts from videos and sum\\nconst videos = items[1].json.items;\\nlet totalViews = 0;\\nfor (const video of videos) {\\n  const views = parseInt(video.snippet.title.match(/Views:\\\\s(\\\\d+)/) ? video.snippet.title.match(/Views:\\\\s(\\\\d+)/)[1] : '0', 10);\\n  totalViews += views;\\n}\\nreturn [{json: {\\n  channelTitle: items[0].json.channelTitle,\\n  channelId: items[0].json.channelId,\\n  subscriberCount: items[0].json.subscriberCount,\\n  videoCount: items[0].json.videoCount,\\n  channelViewCount: items[0].json.viewCount,\\n  totalVideoViews: totalViews\\n}}];\"\n      },\n      \"name\": \"Calculate Metrics\",\n      \"type\": \"n8n-nodes-base.function\",\n      \"typeVersion\": 1,\n      \"position\": [\n        1200,\n        100\n      ]\n    },\n    {\n      \"parameters\": {\n        \"url\": \"https://api.openrouter.ai/v1/chat/completions\",\n        \"authentication\": \"basicBasic\",\n        \"queryParametersUi\": {\n          \"parameter\": []\n        },\n        \"bodyParametersUi\": {\n          \"parameter\": [\n            {\n              \"name\": \"model\",\n              \"value\": \"gpt-4o-mini\",\n              \"type\": \"string\"\n            },\n            {\n              \"name\": \"messages\",\n              \"value\": \"=[[{'role':'system','content':'You are an analytical YouTube channel consultant.'},{'role':'user','content': 'Please analyze the following channel data and provide a report, content ideas, and copy suggestions:\\\\n'+JSON.stringify($json)}]]\",\n              \"type\": \"string\"\n            }\n          ]\n        },\n        \"options\": {\n          \"headersParameters\": {\n            \"Content-Type\": \"application/json\"\n          },\n          \"responseFormat\": \"jsonList\"\n        },\n        \"authenticationOptions\": {\n          \"basicAuth\": {\n            \"username\": \"={{ $env.OPENROUTER_USER }}\",\n            \"password\": \"={{ $env.OPENROUTER_SECRET }}\"\n          }\n        }\n      },\n      \"name\": \"OpenRouter Analysis\",\n      \"type\": \"n8n-nodes-base.httpRequest\",\n      \"typeVersion\": 1,\n      \"position\": [\n        1400,\n        100\n      ]\n    },\n    {\n      \"parameters\": {\n        \"values\": {\n          \"active\": [\n            {\n              \"name\": \"analysisReport\",\n              \"value\": \"={{ $json.OpenRouterAnalysis[0].data.choices[0].message.content }}\"\n            }\n          ],\n          \"options\": [\n            {\n              \"name\": \"channelAnalysis\",\n              \"value\": \"={{ $json }}\"\n            }\n          ]\n        },\n        \"options\": {\n          \"keepOnlySet\": true,\n          \"addToOutput\": true\n        }\n      },\n      \"name\": \"Set – Prepare Response\",\n      \"type\": \"n8n-nodes-base.set\",\n      \"typeVersion\": 1,\n      \"position\": [\n        1600,\n        100\n      ]\n    },\n    {\n      \"parameters\": {\n        \"responseData\": \"={{ $node['Set – Prepare Response'].json }}\"\n      },\n      \"name\": \"Webhook Response\",\n      \"type\": \"n8n-nodes-base.n8nWebhookReturn\",\n      \"typeVersion\": 1,\n      \"position\": [\n        1800,\n        100\n      ]\n    }\n  ],\n  \"connections\": {\n    \"Webhook Trigger\": {\n      \"main\": [\n        [\n          {\n            \"node\": \"Set – Parse URL\",\n            \"type\": \"main\",\n            \"index\": 0\n          }\n        ]\n      ]\n    },\n    \"Set – Parse URL\": {\n      \"main\": [\n        [\n          {\n            \"node\": \"Get Channel Details\",\n            \"type\": \"main\",\n            \"index\": 0\n          }\n        ]\n      ]\n    },\n    \"Get Channel Details\": {\n      \"main\": [\n        [\n          {\n            \"node\": \"Extract Upload Playlist ID\",\n            \"type\": \"main\",\n            \"index\": 0\n          }\n        ]\n      ]\n    },\n    \"Extract Upload Playlist ID\": {\n      \"main\": [\n        [\n          {\n            \"node\": \"Get Uploads\",\n            \"type\": \"main\",\n            \"index\": 0\n          }\n        ]\n      ]\n    },\n    \"Get Uploads\": {\n      \"main\": [\n        [\n          {\n            \"node\": \"Calculate Metrics\",\n            \"type\": \"main\",\n            \"index\": 0\n          }\n        ]\n      ]\n    },\n    \"Calculate Metrics\": {\n      \"main\": [\n        [\n          {\n            \"node\": \"OpenRouter Analysis\",\n            \"type\": \"main\",\n            \"index\": 0\n          }\n        ]\n      ]\n    },\n    \"OpenRouter Analysis\": {\n      \"main\": [\n        [\n          {\n            \"node\": \"Set – Prepare Response\",\n            \"type\": \"main\",\n            \"index\": 0\n          }\n        ]\n      ]\n    },\n    \"Set – Prepare Response\": {\n      \"main\": [\n        [\n          {\n            \"node\": \"Webhook Response\",\n            \"type\": \"main\",\n            \"index\": 0\n          }\n        ]\n      ]\n    }\n  },\n  \"active\": false\n}"], ["22", "{\n  \"name\": \"Receive updates when an event occurs in Asana\",\n  \"nodes\": [\n    {\n      \"parameters\": {\n        \"resource\": \"Tweets\",\n        \"trigger\": \"created\",\n        \"workspace\": \"Engineering\",\n        \"credentials\": {\n          \"name\": \"asana\"\n        }\n      },\n      \"name\": \"Asana Trigger\",\n      \"type\": \"n8n-nodes-base.asanaTrigger\",\n      \"typeVersion\": 1,\n      \"position\": [\n        0,\n        0\n      ]\n    }\n  ],\n  \"connections\": {},\n  \"active\": false\n}"], ["23", "{\n  \"name\": \"Add Manual Event to Google Calendar\",\n  \"nodes\": [\n    {\n      \"parameters\": {},\n      \"name\": \"Manual Trigger\",\n      \"type\": \"n8n-nodes-base.manualTrigger\",\n      \"typeVersion\": 1,\n      \"position\": [\n        0,\n        0\n      ]\n    },\n    {\n      \"parameters\": {\n        \"operation\": \"create\",\n        \"calendarId\": \"<EMAIL>\",\n        \"event\": {\n          \"summary\": \"Manual Event\",\n          \"description\": \"Event created via n8n workflow\",\n          \"start\": {\n            \"dateTime\": \"2020-06-25T07:00:00Z\",\n            \"timeZone\": \"UTC\"\n          },\n          \"end\": {\n            \"dateTime\": \"2020-06-27T07:00:00Z\",\n            \"timeZone\": \"UTC\"\n          }\n        }\n      },\n      \"name\": \"Google Calendar\",\n      \"type\": \"n8n-nodes-base.googleCalendar\",\n      \"typeVersion\": 1,\n      \"position\": [\n        300,\n        100\n      ]\n    }\n  ],\n  \"connections\": {\n    \"Manual Trigger\": {\n      \"main\": [\n        [\n          {\n            \"node\": \"Google Calendar\",\n            \"type\": \"main\",\n            \"index\": 0\n          }\n        ]\n      ]\n    }\n  },\n  \"active\": false\n}"], ["24", "{\n  \"name\": \"My workflow 8\",\n  \"nodes\": [\n    {\n      \"parameters\": {\n        \"path\": \"d1cba915-ca18-4425-bcfb-133205fc815a\",\n        \"title\": \"test\",\n        \"fields\": [\n          {\n            \"name\": \"test\",\n            \"placeholder\": \"\",\n            \"inputType\": \"text\"\n          }\n        ]\n      },\n      \"name\": \"FormTrigger\",\n      \"type\": \"n8n-nodes-base.formTrigger\",\n      \"typeVersion\": 1,\n      \"position\": [\n        0,\n        0\n      ]\n    },\n    {\n      \"parameters\": {\n        \"functionCode\": \"return [{name:'First item',code:1},{name:'Second item',code:2}];\"\n      },\n      \"name\": \"FuncNode\",\n      \"type\": \"n8n-nodes-base.function\",\n      \"typeVersion\": 1,\n      \"position\": [\n        200,\n        0\n      ]\n    },\n    {\n      \"parameters\": {\n        \"conditions\": {},\n        \"defaultCase\": \"no-match\"\n      },\n      \"name\": \"SwitchNode\",\n      \"type\": \"n8n-nodes-base.switch\",\n      \"typeVersion\": 1,\n      \"position\": [\n        400,\n        0\n      ]\n    }\n  ],\n  \"connections\": {\n    \"FormTrigger\": {\n      \"main\": [\n        [\n          {\n            \"node\": \"FuncNode\",\n            \"type\": \"main\",\n            \"index\": 0\n          }\n        ]\n      ]\n    },\n    \"FuncNode\": {\n      \"main\": [\n        [\n          {\n            \"node\": \"SwitchNode\",\n            \"type\": \"main\",\n            \"index\": 0\n          }\n        ]\n      ]\n    }\n  },\n  \"active\": false\n}"], ["25", "{\n  \"name\": \"Multi-Book XML Library Workflow\",\n  \"nodes\": [\n    {\n      \"parameters\": {\n        \"value\": true\n      },\n      \"name\": \"ManualTrigger\",\n      \"type\": \"n8n-nodes-base.manualTrigger\",\n      \"typeVersion\": 1,\n      \"position\": [0, 0]\n    },\n    {\n      \"parameters\": {\n        \"values\": [\n          {\n            \"name\": \"body\",\n            \"value\": \"<library><book><title>Book 1</title><author>Author 1</author></book><book><title>Book 2</title><author>Author 2</author></book></library>\"\n          }\n        ],\n        \"keepOnlySet\": true\n      },\n      \"name\": \"SetXMLBody\",\n      \"type\": \"n8n-nodes-base.set\",\n      \"typeVersion\": 1,\n      \"position\": [200, 0]\n    }\n  ],\n  \"connections\": {\n    \"ManualTrigger\": {\n      \"main\": [\n        [\n          {\n            \"node\": \"SetXMLBody\",\n            \"type\": \"main\",\n            \"index\": 0\n          }\n        ]\n      ]\n    }\n  },\n  \"active\": false\n}"], ["26", ""], ["27", "{\n  \"name\": \"Telegram Email Summary Workflow\",\n  \"nodes\": [\n    {\n      \"parameters\": {\n        \"webhookPath\": \"/telegram\",\n        \"responseCode\": 200\n      },\n      \"name\": \"Telegram Trigger\",\n      \"type\": \"n8n-nodes-base.telegramTrigger\",\n      \"typeVersion\": 1,\n      \"position\": [\n        200,\n        200\n      ]\n    },\n    {\n      \"parameters\": {\n        \"functionCode\": \"const text = item.json.message && item.json.message.text ? item.json.message.text : \\\"\\\";\\nitem.json.matches = text.includes(\\\"lies meine email\\\");\\nreturn item;\"\n      },\n      \"name\": \"Check Phrase\",\n      \"type\": \"n8n-nodes-base.function\",\n      \"typeVersion\": 1,\n      \"position\": [\n        400,\n        200\n      ]\n    },\n    {\n      \"parameters\": {\n        \"conditions\": {\n          \"checkAll\": [\n            {\n              \"value\": \"{{$json.matches}}\",\n              \"compareType\": \"Equals\",\n              \"value2\": \"true\"\n            }\n          ]\n        }\n      },\n      \"name\": \"IF Matches\",\n      \"type\": \"n8n-nodes-base.if\",\n      \"typeVersion\": 1,\n      \"position\": [\n        600,\n        200\n      ]\n    },\n    {\n      \"parameters\": {\n        \"operation\": \"getMessages\",\n        \"maxResults\": 10,\n        \"returnAll\": false,\n        \"includeMessages\": true,\n        \"includeSnippets\": true\n      },\n      \"name\": \"Retrieve Gmail Snippets\",\n      \"type\": \"n8n-nodes-base.gmail\",\n      \"typeVersion\": 1,\n      \"position\": [\n        800,\n        200\n      ]\n    },\n    {\n      \"parameters\": {\n        \"functionCode\": \"let snippets = '';\\nitems.forEach(i => {\\n  const snippet = i.json.snippet || '';\\n  snippets += snippet + '\\\\n';\\n});\\nreturn [{ json: { snippets } }];\"\n      },\n      \"name\": \"Aggregate Snippets\",\n      \"type\": \"n8n-nodes-base.function\",\n      \"typeVersion\": 1,\n      \"position\": [\n        1000,\n        200\n      ]\n    },\n    {\n      \"parameters\": {\n        \"url\": \"https://api.deepseek.com/v1/chat/completions\",\n        \"options\": {\n          \"method\": \"POST\",\n          \"headers\": {\n            \"Content-Type\": \"application/json\",\n            \"Authorization\": \"Bearer {{$credentials.deepseek_api.key}}\"\n          },\n          \"body\": \"JSON.stringify({\\n  model: \\\"deepseek\\\",\\n  messages: [{ role: \\\"user\\\", content: \\\"Summarize the following emails:\\\\n\\\" + $json.snippets }],\\n  temperature: 0.7,\\n  max_tokens: 2000\\n})\"\n        }\n      },\n      \"name\": \"DeepSeek Summarize\",\n      \"type\": \"n8n-nodes-base.httpRequest\",\n      \"typeVersion\": 1,\n      \"position\": [\n        1200,\n        200\n      ]\n    },\n    {\n      \"parameters\": {\n        \"chatId\": \"{{$json.message.chat.id}}\",\n        \"text\": \"{{$json.body.choices[0].message.content}}\"\n      },\n      \"name\": \"Send Summary\",\n      \"type\": \"n8n-nodes-base.telegram\",\n      \"typeVersion\": 1,\n      \"position\": [\n        1400,\n        200\n      ]\n    }\n  ],\n  \"connections\": {\n    \"Telegram Trigger\": {\n      \"main\": [\n        [\n          {\n            \"node\": \"Check Phrase\",\n            \"type\": \"main\",\n            \"index\": 0\n          }\n        ]\n      ]\n    },\n    \"Check Phrase\": {\n      \"main\": [\n        [\n          {\n            \"node\": \"IF Matches\",\n            \"type\": \"main\",\n            \"index\": 0\n          }\n        ]\n      ]\n    },\n    \"IF Matches\": {\n      \"main\": [\n        [\n          {\n            \"node\": \"Retrieve Gmail Snippets\",\n            \"type\": \"main\",\n            \"index\": 0\n          }\n        ]\n      ]\n    },\n    \"Retrieve Gmail Snippets\": {\n      \"main\": [\n        [\n          {\n            \"node\": \"Aggregate Snippets\",\n            \"type\": \"main\",\n            \"index\": 0\n          }\n        ]\n      ]\n    },\n    \"Aggregate Snippets\": {\n      \"main\": [\n        [\n          {\n            \"node\": \"DeepSeek Summarize\",\n            \"type\": \"main\",\n            \"index\": 0\n          }\n        ]\n      ]\n    },\n    \"DeepSeek Summarize\": {\n      \"main\": [\n        [\n          {\n            \"node\": \"Send Summary\",\n            \"type\": \"main\",\n            \"index\": 0\n          }\n        ]\n      ]\n    }\n  },\n  \"active\": false\n}"], ["28", "{\n  \"name\": \"Drive File to Email\",\n  \"nodes\": [\n    {\n      \"parameters\": {\n        \"folders\": [\n          {\n            \"folderId\": \"1HwOAKkkgveLji8vVpW9Xrg1EsBskwMNb\",\n            \"name\": \"\"\n          }\n        ],\n        \"triggerFileEvents\": {\n          \"created\": true,\n          \"modified\": false,\n          \"delete\": false\n        }\n      },\n      \"name\": \"Google Drive Trigger\",\n      \"type\": \"n8n-nodes-base.googleDriveTrigger\",\n      \"typeVersion\": 1,\n      \"position\": [\n        200,\n        300\n      ]\n    },\n    {\n      \"parameters\": {\n        \"recipient\": \"<EMAIL>\",\n        \"sender\": \"<EMAIL>\",\n        \"subject\": \"File Update\",\n        \"text\": \"A file in your Google Drive file folder has been created: {{ $json[\\\"name\\\"] }}\"\n      },\n      \"name\": \"Send Email\",\n      \"type\": \"n8n-nodes-base.sendEmail\",\n      \"typeVersion\": 1,\n      \"position\": [\n        600,\n        300\n      ]\n    }\n  ],\n  \"connections\": {\n    \"Google Drive Trigger\": {\n      \"main\": [\n        [\n          {\n            \"node\": \"Send Email\",\n            \"type\": \"main\",\n            \"index\": 0\n          }\n        ]\n      ]\n    }\n  },\n  \"active\": false\n}"], ["29", "{\n  \"name\": \"Gumroad to Mailing List & CRM\",\n  \"nodes\": [\n    {\n      \"parameters\": {\n        \"httpMethod\": \"POST\",\n        \"responseMode\": \"onReceived\",\n        \"path\": \"gumroad\",\n        \"requiredBodyParameters\": \"\"\n      },\n      \"name\": \"Gumroad Webhook\",\n      \"type\": \"n8n-nodes-base.webhook\",\n      \"typeVersion\": 1,\n      \"position\": [\n        300,\n        200\n      ]\n    },\n    {\n      \"parameters\": {\n        \"operation\": \"add\",\n        \"mode\": \"create\",\n        \"listId\": \"YOUR_LIST_ID\",\n        \"email\": \"={{$json.email}}\",\n        \"firstName\": \"={{$json.first_name}}\",\n        \"lastName\": \"={{$json.last_name}}\",\n        \"groups\": [\n          \"YOUR_GROUP_ID\"\n        ]\n      },\n      \"name\": \"Add to MailerLite\",\n      \"type\": \"n8n-nodes-base.mailerlite\",\n      \"typeVersion\": 1,\n      \"position\": [\n        600,\n        200\n      ]\n    },\n    {\n      \"parameters\": {\n        \"mode\": \"append\",\n        \"worksheetId\": \"YOUR_SHEET_ID\",\n        \"values\": [\n          [\n            \"={{$json.first_name}}\",\n            \"={{$json.last_name}}\",\n            \"={{$json.email}}\",\n            \"={{$json.timestamp}}\"\n          ]\n        ]\n      },\n      \"name\": \"Append to Google Sheets\",\n      \"type\": \"n8n-nodes-base.googleSheets\",\n      \"typeVersion\": 1,\n      \"position\": [\n        900,\n        200\n      ]\n    }\n  ],\n  \"connections\": {\n    \"Gumroad Webhook\": {\n      \"main\": [\n        [\n          {\n            \"node\": \"Add to MailerLite\",\n            \"type\": \"main\",\n            \"index\": 0\n          }\n        ]\n      ]\n    },\n    \"Add to MailerLite\": {\n      \"main\": [\n        [\n          {\n            \"node\": \"Append to Google Sheets\",\n            \"type\": \"main\",\n            \"index\": 0\n          }\n        ]\n      ]\n    }\n  },\n  \"active\": false\n}"], ["30", "{\n  \"name\": \"GetOnboardingFileChainofThought\",\n  \"nodes\": [\n    {\n      \"parameters\": {\n        \"httpMethod\": \"POST\",\n        \"options\": {}\n      },\n      \"name\": \"Webhook Trigger\",\n      \"type\": \"n8n-nodes-base.webhook\",\n      \"typeVersion\": 1,\n      \"position\": [250, 250]\n    },\n    {\n      \"parameters\": {\n        \"operation\": \"retrieveDocument\",\n        \"documentId\": \"1K0Y-OxyIV0auMN0CKe8tWUb4uSimFFIWZQCWaEvKQo4\",\n        \"formatResponse\": \"plain\"\n      },\n      \"name\": \"Get Google Doc\",\n      \"type\": \"n8n-nodes-base.googleDocs\",\n      \"typeVersion\": 1,\n      \"position\": [450, 250]\n    },\n    {\n      \"parameters\": {\n        \"response\": \"{{ $node[\\\"Get Google Doc\\\"].json[\\\"body\\\"] }}\",\n        \"responseType\": \"full\"\n      },\n      \"name\": \"Respond to Webhook\",\n      \"type\": \"n8n-nodes-base.respondWebhook\",\n      \"typeVersion\": 1,\n      \"position\": [650, 250]\n    }\n  ],\n  \"connections\": {\n    \"Webhook Trigger\": {\n      \"main\": [\n        [\n          {\n            \"node\": \"Get Google Doc\",\n            \"type\": \"main\",\n            \"index\": 0\n          }\n        ]\n      ]\n    },\n    \"Get Google Doc\": {\n      \"main\": [\n        [\n          {\n            \"node\": \"Respond to Webhook\",\n            \"type\": \"main\",\n            \"index\": 0\n          }\n        ]\n      ]\n    }\n  },\n  \"active\": false\n}"], ["31", "{\n  \"name\": \"Multiple trigger node rerun\",\n  \"nodes\": [\n    {\n      \"parameters\": {},\n      \"name\": \"ManualTrigger\",\n      \"type\": \"n8n-nodes-base.manualTrigger\",\n      \"typeVersion\": 1,\n      \"position\": [0, 0]\n    },\n    {\n      \"parameters\": {\n        \"cron\": \"* * * * *\"\n      },\n      \"name\": \"Cron\",\n      \"type\": \"n8n-nodes-base.cron\",\n      \"typeVersion\": 1,\n      \"position\": [0, 80]\n    },\n    {\n      \"parameters\": {\n        \"url\": \"https://internal.users.n8n.cloud/webhook/random-data-api\",\n        \"method\": \"GET\"\n      },\n      \"name\": \"HTTPRequest\",\n      \"type\": \"n8n-nodes-base.httpRequest\",\n      \"typeVersion\": 1,\n      \"position\": [200, 40]\n    },\n    {\n      \"parameters\": {\n        \"code\": \"const users = items[0].json.data ?? [];\\nconst result = users.map(u => {\\n  u.firstname = u.firstname.split('').reverse().join('');\\n  return { json: u };\\n});\\nreturn result;\"\n      },\n      \"name\": \"Code\",\n      \"type\": \"n8n-nodes-base.code\",\n      \"typeVersion\": 1,\n      \"position\": [400, 40]\n    }\n  ],\n  \"connections\": {\n    \"ManualTrigger\": {\n      \"main\": [\n        [\n          {\n            \"node\": \"HTTPRequest\",\n            \"type\": \"main\",\n            \"index\": 0\n          }\n        ]\n      ]\n    },\n    \"Cron\": {\n      \"main\": [\n        [\n          {\n            \"node\": \"HTTPRequest\",\n            \"type\": \"main\",\n            \"index\": 0\n          }\n        ]\n      ]\n    },\n    \"HTTPRequest\": {\n      \"main\": [\n        [\n          {\n            \"node\": \"Code\",\n            \"type\": \"main\",\n            \"index\": 0\n          }\n        ]\n      ]\n    }\n  },\n  \"active\": false\n}"], ["32", "{\n  \"name\": \"Auto Appointment Response\",\n  \"nodes\": [\n    {\n      \"parameters\": {\n        \"mode\": \"trigger\",\n        \"triggerType\": \"gmailNew\",\n        \"auth\": \"default\",\n        \"label\": \"emailMatch\",\n        \"options\": {\n          \"subject\": \"\",\n          \"from\": \"\",\n          \"until\": \"\"\n        }\n      },\n      \"name\": \"GmailTrigger\",\n      \"type\": \"n8n-nodes-base.gmailTrigger\",\n      \"typeVersion\": 4,\n      \"position\": [\n        0,\n        0\n      ]\n    },\n    {\n      \"parameters\": {\n        \"code\": \"return items.map(item => ({ json: { emailContent: item.json.body }}));\"\n      },\n      \"name\": \"ExtractEmailContent\",\n      \"type\": \"n8n-nodes-base.function\",\n      \"typeVersion\": 1,\n      \"position\": [\n        300,\n        0\n      ]\n    },\n    {\n      \"parameters\": {\n        \"model\": \"gpt-4o-mini\",\n        \"inputMessageField\": \"emailContent\",\n        \"additionalPrompt\": \"Classify if this email is an appointment request and extract \\\"senderEmail\\\" and \\\"subject\\\".\",\n        \"responseParser\": \"json\"\n      },\n      \"name\": \"OpenAIClassify\",\n      \"type\": \"n8n-nodes-base.openAI\",\n      \"typeVersion\": 1,\n      \"position\": [\n        600,\n        0\n      ]\n    },\n    {\n      \"parameters\": {\n        \"code\": \"return items.map(item => {\\n  const data = item.json.openAIResponse || {};\\n  return { json: { emailContent: item.json.emailContent, sender: data.senderEmail, subject: data.subject }};\\n});\"\n      },\n      \"name\": \"ParseResponse\",\n      \"type\": \"n8n-nodes-base.function\",\n      \"typeVersion\": 1,\n      \"position\": [\n        900,\n        0\n      ]\n    },\n    {\n      \"parameters\": {\n        \"eventType\": \"freeBusy\",\n        \"timeMin\": \"{{$json[\\\"timestamp\\\"]}}\",\n        \"timeMax\": \"$now + 30d\",\n        \"timeZone\": \"UTC\",\n        \"items\": [\n          {\n            \"resourceId\": \"primary\"\n          }\n        ],\n        \"output\": \"structured\",\n        \"calendarId\": \"primary\"\n      },\n      \"name\": \"CalendarCheck\",\n      \"type\": \"n8n-nodes-base.googleCalendar\",\n      \"typeVersion\": 1,\n      \"position\": [\n        1200,\n        0\n      ]\n    },\n    {\n      \"parameters\": {\n        \"code\": \"return items.map(item => {\\n  const times = item.json.calendarFreeBusy?.slots || [];\\n  const replyBody = `Hello,\\\\n\\\\nThank you for your appointment request.\\\\nHere are some available times:\\\\n` + times.map((t, i) => `${i+1}. ${t}`).join('\\\\n') + '\\\\n\\\\nPlease let us know which works for you.\\\\n\\\\nBest regards,\\\\nYour Scheduler';\\n  return { json: { replyBody, sender: item.json.emailContent, subject: item.json.subject + ' - Re', originalMessageId: item.json.id } };\\n});\"\n      },\n      \"name\": \"ComposeReply\",\n      \"type\": \"n8n-nodes-base.function\",\n      \"typeVersion\": 1,\n      \"position\": [\n        1500,\n        0\n      ]\n    },\n    {\n      \"parameters\": {\n        \"accountId\": \"default\",\n        \"to\": \"={{$json['sender']}}\",\n        \"cc\": \"\",\n        \"bcc\": \"\",\n        \"subject\": \"={{$json.subject}}\",\n        \"bodyHtml\": \"={{$json.replyBody}}\",\n        \"replyToMessageId\": \"={{$json.originalMessageId}}\",\n        \"sendMessage\": true\n      },\n      \"name\": \"DraftReply\",\n      \"type\": \"n8n-nodes-base.gmail\",\n      \"typeVersion\": 1,\n      \"position\": [\n        1800,\n        0\n      ]\n    },\n    {\n      \"parameters\": {\n        \"action\": \"markRead\",\n        \"messageIds\": \"={{$json.originalMessageId}}\"\n      },\n      \"name\": \"MarkRead\",\n      \"type\": \"n8n-nodes-base.gmail\",\n      \"typeVersion\": 1,\n      \"position\": [\n        2100,\n        0\n      ]\n    }\n  ],\n  \"connections\": {\n    \"GmailTrigger\": {\n      \"main\": [\n        [\n          {\n            \"node\": \"ExtractEmailContent\",\n            \"type\": \"main\",\n            \"index\": 0\n          }\n        ]\n      ]\n    },\n    \"ExtractEmailContent\": {\n      \"main\": [\n        [\n          {\n            \"node\": \"OpenAIClassify\",\n            \"type\": \"main\",\n            \"index\": 0\n          }\n        ]\n      ]\n    },\n    \"OpenAIClassify\": {\n      \"main\": [\n        [\n          {\n            \"node\": \"ParseResponse\",\n            \"type\": \"main\",\n            \"index\": 0\n          }\n        ]\n      ]\n    },\n    \"ParseResponse\": {\n      \"main\": [\n        [\n          {\n            \"node\": \"CalendarCheck\",\n            \"type\": \"main\",\n            \"index\": 0\n          }\n        ]\n      ]\n    },\n    \"CalendarCheck\": {\n      \"main\": [\n        [\n          {\n            \"node\": \"ComposeReply\",\n            \"type\": \"main\",\n            \"index\": 0\n          }\n        ]\n      ]\n    },\n    \"ComposeReply\": {\n      \"main\": [\n        [\n          {\n            \"node\": \"DraftReply\",\n            \"type\": \"main\",\n            \"index\": 0\n          }\n        ]\n      ]\n    },\n    \"DraftReply\": {\n      \"main\": [\n        [\n          {\n            \"node\": \"MarkRead\",\n            \"type\": \"main\",\n            \"index\": 0\n          }\n        ]\n      ]\n    }\n  ],\n  \"active\": false\n}"], ["33", "{\n  \"name\": \"AI Webpage Scraper with ReAct\",\n  \"nodes\": [\n    {\n      \"parameters\": {\n        \"httpMethod\": \"GET\",\n        \"path\": \"\",\n        \"parameters\": true,\n        \"webhookPath\": \"scrape\",\n        \"options\": {}\n      },\n      \"name\": \"Trigger\",\n      \"type\": \"n8n-nodes-base.webhook\",\n      \"typeVersion\": 1,\n      \"position\": [0, 0]\n    },\n    {\n      \"parameters\": {\n        \"values\": {\n          \"standard\": [\n            {\n              \"name\": \"url\",\n              \"value\": \"{{$json[\\\"query\\\"][\\\"url\\\"]}}\",\n              \"type\": \"string\"\n            },\n            {\n              \"name\": \"contentMethod\",\n              \"value\": \"{{$json[\\\"query\\\"][\\\"method\\\"]}}\",\n              \"type\": \"string\"\n            },\n            {\n              \"name\": \"maxPageLength\",\n              \"value\": \"{{$json[\\\"query\\\"][\\\"maxPageLength\\\"]}}\",\n              \"type\": \"string\"\n            }\n          ]\n        },\n        \"json\": true\n      },\n      \"name\": \"ExtractParams\",\n      \"type\": \"n8n-nodes-base.set\",\n      \"typeVersion\": 1,\n      \"position\": [300, 0]\n    },\n    {\n      \"parameters\": {\n        \"url\": \"{{$node[\\\"ExtractParams\\\"].json[\\\"url\\\"]}}\",\n        \"method\": \"GET\",\n        \"responseFormat\": \"text\",\n        \"options\": {}\n      },\n      \"name\": \"FetchPage\",\n      \"type\": \"n8n-nodes-base.httpRequest\",\n      \"typeVersion\": 1,\n      \"position\": [600, 0]\n    },\n    {\n      \"parameters\": {\n        \"selector\": \"body\",\n        \"htmlContent\": \"{{$node[\\\"FetchPage\\\"].json.body}}\",\n        \"extractMode\": \"text\",\n        \"ignoreDuplicates\": false\n      },\n      \"name\": \"ExtractContent\",\n      \"type\": \"n8n-nodes-base.htmlExtract\",\n      \"typeVersion\": 1,\n      \"position\": [900, 0]\n    },\n    {\n      \"parameters\": {\n        \"code\": \"const method = $json.contentMethod;\\nconst maxLength = parseInt($json.maxPageLength, 10);\\nlet text = $json[\\\"text\\\"] || \\\"\\\";\\nif (method === \\\"simplified\\\") {\\n  if (maxLength && text.length > maxLength) {\\n    text = text.substring(0, maxLength) + \\\"…\\\";\\n  }\\n}\\nreturn { json: { simplifiedText: text } };\"\n      },\n      \"name\": \"SimplifyContent\",\n      \"type\": \"n8n-nodes-base.function\",\n      \"typeVersion\": 1,\n      \"position\": [1200, 0]\n    },\n    {\n      \"parameters\": {\n        \"code\": \"const marked = require('marked');\\nconst html = $json.simplifiedText;\\nreturn { json: { markdown: marked(html) } };\"\n      },\n      \"name\": \"ConvertToMarkdown\",\n      \"type\": \"n8n-nodes-base.function\",\n      \"typeVersion\": 1,\n      \"position\": [1500, 0]\n    },\n    {\n      \"parameters\": {\n        \"model\": \"gpt-4\",\n        \"messages\": [\n          {\n            \"role\": \"system\",\n            \"content\": \"You are a ReAct agent. Use the provided markdown content to answer questions.\"\n          },\n          {\n            \"role\": \"user\",\n            \"content\": \"{{$node[\\\"ConvertToMarkdown\\\"].json.markdown}}\"\n          }\n        ],\n        \"options\": {}\n      },\n      \"name\": \"OpenAIChat\",\n      \"type\": \"n8n-nodes-base.openAi\",\n      \"typeVersion\": 1,\n      \"position\": [1800, 0]\n    },\n    {\n      \"parameters\": {\n        \"options\": {}\n      },\n      \"name\": \"ManualChat\",\n      \"type\": \"n8n-nodes-base.manualTrigger\",\n      \"typeVersion\": 1,\n      \"position\": [2100, 0]\n    },\n    {\n      \"parameters\": {},\n      \"name\": \"ErrorHandler\",\n      \"type\": \"n8n-nodes-base.errorTrigger\",\n      \"typeVersion\": 1,\n      \"position\": [2400, 0]\n    }\n  ],\n  \"connections\": {\n    \"Trigger\": {\n      \"main\": [\n        [\n          {\n            \"node\": \"ExtractParams\",\n            \"type\": \"main\",\n            \"index\": 0\n          }\n        ]\n      ]\n    },\n    \"ExtractParams\": {\n      \"main\": [\n        [\n          {\n            \"node\": \"FetchPage\",\n            \"type\": \"main\",\n            \"index\": 0\n          }\n        ]\n      ]\n    },\n    \"FetchPage\": {\n      \"main\": [\n        [\n          {\n            \"node\": \"ExtractContent\",\n            \"type\": \"main\",\n            \"index\": 0\n          }\n        ]\n      ]\n    },\n    \"ExtractContent\": {\n      \"main\": [\n        [\n          {\n            \"node\": \"SimplifyContent\",\n            \"type\": \"main\",\n            \"index\": 0\n          }\n        ]\n      ]\n    },\n    \"SimplifyContent\": {\n      \"main\": [\n        [\n          {\n            \"node\": \"ConvertToMarkdown\",\n            \"type\": \"main\",\n            \"index\": 0\n          }\n        ]\n      ]\n    },\n    \"ConvertToMarkdown\": {\n      \"main\": [\n        [\n          {\n            \"node\": \"OpenAIChat\",\n            \"type\": \"main\",\n            \"index\": 0\n          }\n        ]\n      ]\n    },\n    \"ManualChat\": {\n      \"main\": [\n        [\n          {\n            \"node\": \"OpenAIChat\",\n            \"type\": \"main\",\n            \"index\": 0\n          }\n        ]\n      ]\n    }\n  },\n  \"active\": false\n}"], ["34", "{\n  \"name\": \"WhatsApp Scheduler 2024-10-10\",\n  \"nodes\": [\n    {\n      \"parameters\": {\n        \"timezone\": \"UTC\",\n        \"cron\": [\n          \"0 12,17 * * 10\"\n        ]\n      },\n      \"name\": \"<PERSON><PERSON> Trigger\",\n      \"type\": \"n8n-nodes-base.cronTrigger\",\n      \"typeVersion\": 1,\n      \"position\": [\n        200,\n        200\n      ]\n    },\n    {\n      \"parameters\": {\n        \"googleSheets\": {\n          \"action\": \"readValues\",\n          \"range\": \"Sheet1!A2:D\"\n        },\n        \"spreadsheetId\": \"SPREADSHEET_ID\"\n      },\n      \"name\": \"Read Sheet\",\n      \"type\": \"n8n-nodes-base.googleSheets\",\n      \"typeVersion\": 1,\n      \"position\": [\n        400,\n        200\n      ]\n    },\n    {\n      \"parameters\": {\n        \"functionCode\": \"return items().map(item => { item.json.phone = item.json.phone.replace(/\\\\D/g,''); return item; });\"\n      },\n      \"name\": \"Process Numbers\",\n      \"type\": \"n8n-nodes-base.function\",\n      \"typeVersion\": 1,\n      \"position\": [\n        600,\n        200\n      ]\n    },\n    {\n      \"parameters\": {\n        \"messageType\": \"template\",\n        \"templateName\": \"my_template\",\n        \"language\": \"en\",\n        \"details\": {\n          \"recipient\": \"={{$json.phone}}\",\n          \"params\": []\n        }\n      },\n      \"name\": \"Send WhatsApp\",\n      \"type\": \"n8n-nodes-base.whatsapp\",\n      \"typeVersion\": 1,\n      \"position\": [\n        800,\n        200\n      ]\n    },\n    {\n      \"parameters\": {\n        \"googleSheets\": {\n          \"action\": \"updateValues\",\n          \"range\": \"Sheet1!E2:E\",\n          \"rows\": [\n            []\n          ]\n        }\n      },\n      \"name\": \"Write Status\",\n      \"type\": \"n8n-nodes-base.googleSheets\",\n      \"typeVersion\": 1,\n      \"position\": [\n        1000,\n        200\n      ]\n    },\n    {\n      \"parameters\": {},\n      \"name\": \"Error Trigger\",\n      \"type\": \"n8n-nodes-base.errorTrigger\",\n      \"typeVersion\": 1,\n      \"position\": [\n        400,\n        400\n      ]\n    },\n    {\n      \"parameters\": {\n        \"subject\": \"WhatsApp Send Error\",\n        \"to\": \"<EMAIL>\",\n        \"body\": \"={{$json.error.message}}\"\n      },\n      \"name\": \"Send Email\",\n      \"type\": \"n8n-nodes-base.sendEmail\",\n      \"typeVersion\": 1,\n      \"position\": [\n        600,\n        400\n      ]\n    }\n  ],\n  \"connections\": {\n    \"Cron Trigger\": {\n      \"main\": [\n        [\n          {\n            \"node\": \"Read Sheet\",\n            \"type\": \"main\",\n            \"index\": 0\n          },\n          {\n            \"node\": \"Error Trigger\",\n            \"type\": \"main\",\n            \"index\": 0\n          }\n        ]\n      ]\n    },\n    \"Read Sheet\": {\n      \"main\": [\n        [\n          {\n            \"node\": \"Process Numbers\",\n            \"type\": \"main\",\n            \"index\": 0\n          }\n        ]\n      ]\n    },\n    \"Process Numbers\": {\n      \"main\": [\n        [\n          {\n            \"node\": \"Send WhatsApp\",\n            \"type\": \"main\",\n            \"index\": 0\n          }\n        ]\n      ]\n    },\n    \"Send WhatsApp\": {\n      \"main\": [\n        [\n          {\n            \"node\": \"Write Status\",\n            \"type\": \"main\",\n            \"index\": 0\n          }\n        ]\n      ]\n    },\n    \"Error Trigger\": {\n      \"main\": [\n        [\n          {\n            \"node\": \"Send Email\",\n            \"type\": \"main\",\n            \"index\": 0\n          }\n        ]\n      ]\n    }\n  },\n  \"active\": false\n}"], ["35", "{\n  \"name\": \"Tweet On Manual Trigger\",\n  \"nodes\": [\n    {\n      \"parameters\": {},\n      \"name\": \"Manual Trigger\",\n      \"type\": \"n8n-nodes-base.manualTrigger\",\n      \"typeVersion\": 1,\n      \"position\": [\n        200,\n        200\n      ]\n    },\n    {\n      \"parameters\": {\n        \"credentials\": \"twitter-credentials\",\n        \"operation\": \"post\",\n        \"tweetText\": \"This is a test workflow for the twitter node\"\n      },\n      \"name\": \"Twitter Node\",\n      \"type\": \"n8n-nodes-base.twitter\",\n      \"typeVersion\": 1,\n      \"position\": [\n        400,\n        200\n      ]\n    }\n  ],\n  \"connections\": {\n    \"Manual Trigger\": {\n      \"main\": [\n        [\n          {\n            \"node\": \"Twitter Node\",\n            \"type\": \"main\",\n            \"index\": 0\n          }\n        ]\n      ]\n    }\n  },\n  \"active\": false\n}"], ["36", "{\n  \"name\": \"GetOnboardingFileIteration Methods\",\n  \"nodes\": [\n    {\n      \"parameters\": {\n        \"httpMethod\": \"POST\",\n        \"path\": \"Oboarding/PromptEngineer/IterationMethods\",\n        \"responseMode\": \"response\",\n        \"response\": false\n      },\n      \"name\": \"WebhookTrigger\",\n      \"type\": \"n8n-nodes-base.webhook\",\n      \"typeVersion\": 1,\n      \"position\": [\n        0,\n        0\n      ]\n    },\n    {\n      \"parameters\": {\n        \"operation\": \"read\",\n        \"documentId\": \"1sJVUFcSfL5POnNNc9IQOk17MS9vgJ1DdYvPi2su1KHI\",\n        \"returnContent\": true\n      },\n      \"name\": \"GoogleDocs\",\n      \"type\": \"n8n-nodes-base.googleDocs\",\n      \"typeVersion\": 1,\n      \"position\": [\n        250,\n        0\n      ]\n    },\n    {\n      \"parameters\": {\n        \"responseMode\": \"response\",\n        \"responseCode\": \"200\",\n        \"responseData\": \"{{ $node['GoogleDocs'].json.content }}\"\n      },\n      \"name\": \"WebhookResponse\",\n      \"type\": \"n8n-nodes-base.httpResponse\",\n      \"typeVersion\": 1,\n      \"position\": [\n        500,\n        0\n      ]\n    }\n  ],\n  \"connections\": {\n    \"WebhookTrigger\": {\n      \"main\": [\n        [\n          {\n            \"node\": \"GoogleDocs\",\n            \"type\": \"main\",\n            \"index\": 0\n          }\n        ]\n      ]\n    },\n    \"GoogleDocs\": {\n      \"main\": [\n        [\n          {\n            \"node\": \"WebhookResponse\",\n            \"type\": \"main\",\n            \"index\": 0\n          }\n        ]\n      ]\n    }\n  },\n  \"active\": true\n}"], ["37", "{\n  \"name\": \"ElevenLabs Speech to Text\",\n  \"nodes\": [\n    {\n      \"parameters\": {},\n      \"name\": \"Manual Trigger\",\n      \"type\": \"n8n-nodes-base.trigger\",\n      \"typeVersion\": 1,\n      \"position\": [0, 200]\n    },\n    {\n      \"parameters\": {\n        \"filePath\": \"/files/tmp/tst1.mp4\"\n      },\n      \"name\": \"Read File\",\n      \"type\": \"n8n-nodes-base.readBinaryFile\",\n      \"typeVersion\": 1,\n      \"position\": [200, 200]\n    },\n    {\n      \"parameters\": {\n        \"url\": \"https://api.elevenlabs.io/v1/speech-to-text\",\n        \"method\": \"POST\",\n        \"options\": {\n          \"form\": true\n        },\n        \"formData\": {\n          \"model_id\": \"scribe_v1\"\n        },\n        \"binaryFiles\": [\n          {\n            \"parameterName\": \"file\",\n            \"binaryPropertyName\": \"data\"\n          }\n        ]\n      },\n      \"name\": \"Send to Eleven Labs\",\n      \"type\": \"n8n-nodes-base.httpRequest\",\n      \"typeVersion\": 1,\n      \"position\": [400, 200],\n      \"credentials\": {\n        \"name\": \"Eleven Labs\"\n      }\n    }\n  ],\n  \"connections\": {\n    \"Manual Trigger\": {\n      \"main\": [\n        [\n          {\n            \"node\": \"Read File\",\n            \"type\": \"main\",\n            \"index\": 0\n          }\n        ]\n      ]\n    },\n    \"Read File\": {\n      \"main\": [\n        [\n          {\n            \"node\": \"Send to Eleven Labs\",\n            \"type\": \"main\",\n            \"index\": 0\n          }\n        ]\n      ]\n    }\n  },\n  \"active\": false\n}"], ["38", "{\n  \"name\": \"Parse Phone Workflow\",\n  \"nodes\": [\n    {\n      \"parameters\": {\n        \"httpMethod\": \"POST\",\n        \"path\": \"trigger\"\n      },\n      \"name\": \"Webhook\",\n      \"type\": \"n8n-nodes-base.webhook\",\n      \"typeVersion\": 1,\n      \"position\": [\n        250,\n        130\n      ]\n    },\n    {\n      \"parameters\": {\n        \"values\": [\n          {\n            \"name\": \"phoneNumber\",\n            \"value\": \"+34605281220\"\n          }\n        ]\n      },\n      \"name\": \"Set Phone\",\n      \"type\": \"n8n-nodes-base.set\",\n      \"typeVersion\": 1,\n      \"position\": [\n        450,\n        130\n      ]\n    },\n    {\n      \"parameters\": {\n        \"operations\": [\n          {\n            \"operation\": \"getPhoneParsed\",\n            \"credentials\": {\n              \"name\": \"miquel-uproc\",\n              \"type\": \"credentials\",\n              \"value\": {\n                \"request\": \"={{ $json.phoneNumber }}\"\n              }\n            }\n          }\n        ]\n      },\n      \"name\": \"Uproc Get Phone Parsed\",\n      \"type\": \"n8n-nodes-base.uproc\",\n      \"typeVersion\": 1,\n      \"position\": [\n        650,\n        130\n      ]\n    },\n    {\n      \"parameters\": {\n        \"conditions\": [\n          {\n            \"value1\": \"={{ $json.parsed.is_valid }}\",\n            \"operation\": \"equals\",\n            \"value2\": \"true\"\n          }\n        ]\n      },\n      \"name\": \"If Phone Valid\",\n      \"type\": \"n8n-nodes-base.if\",\n      \"typeVersion\": 1,\n      \"position\": [\n        850,\n        130\n      ]\n    }\n  ],\n  \"connections\": {\n    \"Webhook\": {\n      \"main\": [\n        [\n          {\n            \"node\": \"Set Phone\",\n            \"type\": \"main\",\n            \"index\": 0\n          }\n        ]\n      ]\n    },\n    \"Set Phone\": {\n      \"main\": [\n        [\n          {\n            \"node\": \"Uproc Get Phone Parsed\",\n            \"type\": \"main\",\n            \"index\": 0\n          }\n        ]\n      ]\n    },\n    \"Uproc Get Phone Parsed\": {\n      \"main\": [\n        [\n          {\n            \"node\": \"If Phone Valid\",\n            \"type\": \"main\",\n            \"index\": 0\n          }\n        ]\n      ]\n    }\n  },\n  \"active\": false\n}"], ["39", "{\n  \"name\": \"My workflow 2\",\n  \"nodes\": [],\n  \"connections\": {},\n  \"active\": false\n}"], ["40", "{\n  \"name\": \"AI News Aggregation Workflow\",\n  \"nodes\": [\n    {\n      \"parameters\": {},\n      \"name\": \"AIAutoNewsTrigger\",\n      \"type\": \"mcp.aiNews\",\n      \"typeVersion\": 1,\n      \"position\": [\n        0,\n        0\n      ]\n    },\n    {\n      \"parameters\": {},\n      \"name\": \"KakaoTalkServerTrigger\",\n      \"type\": \"mcp.kakaoTalkServer\",\n      \"typeVersion\": 1,\n      \"position\": [\n        0,\n        200\n      ]\n    },\n    {\n      \"parameters\": {\n        \"operation\": \"httpRequest\",\n        \"requestMethod\": \"GET\",\n        \"requestUrl\": \"{{ $json.url }}\",\n        \"responseFormat\": \"json\",\n        \"resultPropertyName\": \"content\"\n      },\n      \"name\": \"RetrieveContent\",\n      \"type\": \"n8n-nodes-base.workflowTools\",\n      \"typeVersion\": 1,\n      \"position\": [\n        300,\n        0\n      ]\n    },\n    {\n      \"parameters\": {\n        \"resource\": \"sheet\",\n        \"operation\": \"append\",\n        \"spreadsheetId\": \"YOUR_SHEET_ID\",\n        \"range\": \"News!A2:D\",\n        \"values\": [\n          [\n            \"{{ $json.title }}\",\n            \"{{ $json.url }}\",\n            \"{{ $json.content }}\"\n          ]\n        ]\n      },\n      \"name\": \"AppendGoogleSheets\",\n      \"type\": \"n8n-nodes-base.googleSheets\",\n      \"typeVersion\": 1,\n      \"position\": [\n        600,\n        0\n      ]\n    },\n    {\n      \"parameters\": {\n        \"operation\": \"kakaoTalk\",\n        \"apiKey\": \"YOUR_KAKAO_API_KEY\",\n        \"message\": \"New AI news article: {{ $json.title }}\\\\nURL: {{ $json.url }}\"\n      },\n      \"name\": \"SendKakao\",\n      \"type\": \"n8n-nodes-base.workflowTools\",\n      \"typeVersion\": 1,\n      \"position\": [\n        900,\n        0\n      ]\n    }\n  ],\n  \"connections\": {\n    \"AIAutoNewsTrigger\": {\n      \"main\": [\n        [\n          {\n            \"node\": \"RetrieveContent\",\n            \"type\": \"main\",\n            \"index\": 0\n          }\n        ]\n      ]\n    },\n    \"RetrieveContent\": {\n      \"main\": [\n        [\n          {\n            \"node\": \"AppendGoogleSheets\",\n            \"type\": \"main\",\n            \"index\": 0\n          }\n        ]\n      ]\n    },\n    \"AppendGoogleSheets\": {\n      \"main\": [\n        [\n          {\n            \"node\": \"SendKakao\",\n            \"type\": \"main\",\n            \"index\": 0\n          }\n        ]\n      ]\n    }\n  },\n  \"active\": false\n}"], ["41", "{\n  \"name\": \"Fetch Google Docs Content on Webhook\",\n  \"nodes\": [\n    {\n      \"parameters\": {\n        \"httpMethod\": \"ANY\",\n        \"path\": \"google-doc\",\n        \"responseMode\": \"onReceived\"\n      },\n      \"name\": \"Webhook\",\n      \"type\": \"n8n-nodes-base.webhook\",\n      \"typeVersion\": 1,\n      \"position\": [\n        200,\n        200\n      ]\n    },\n    {\n      \"parameters\": {\n        \"operation\": \"getDocument\",\n        \"documentId\": \"1SWrpxi5jfVL79ftSA1u3PZL_wuzcfYFsA_5g02Kuet0\",\n        \"credentials\": {\n          \"googleDocsOAuth2Api\": {\n            \"credentialId\": \"u4aaDpAzm6YKZV9W\",\n            \"credentialType\": \"googleDocsOAuth2Api\"\n          }\n        }\n      },\n      \"name\": \"Google Docs\",\n      \"type\": \"n8n-nodes-base.googleDocs\",\n      \"typeVersion\": 1,\n      \"position\": [\n        500,\n        200\n      ]\n    },\n    {\n      \"parameters\": {\n        \"responseFormat\": \"json\",\n        \"responseValues\": [\n          {\n            \"name\": \"content\",\n            \"value\": \"{{$node['Google Docs'].json}}\"\n          }\n        ]\n      },\n      \"name\": \"Respond to Webhook\",\n      \"type\": \"n8n-nodes-base.respondWebhook\",\n      \"typeVersion\": 1,\n      \"position\": [\n        800,\n        200\n      ]\n    }\n  ],\n  \"connections\": {\n    \"Webhook\": {\n      \"main\": [\n        [\n          {\n            \"node\": \"Google Docs\",\n            \"type\": \"main\",\n            \"index\": 0\n          }\n        ]\n      ]\n    },\n    \"Google Docs\": {\n      \"main\": [\n        [\n          {\n            \"node\": \"Respond to Webhook\",\n            \"type\": \"main\",\n            \"index\": 0\n          }\n        ]\n      ]\n    }\n  },\n  \"active\": false\n}"], ["42", "{\n  \"name\": \"Weather Assistant Workflow\",\n  \"nodes\": [\n    {\n      \"parameters\": {\n        \"responseMode\": \"response\",\n        \"path\": \"/weather\"\n      },\n      \"name\": \"Webhook\",\n      \"type\": \"n8n-nodes-base.webhook\",\n      \"typeVersion\": 1,\n      \"position\": [\n        0,\n        0\n      ]\n    },\n    {\n      \"parameters\": {\n        \"model\": \"gpt-4o-mini\",\n        \"messages\": [\n          {\n            \"role\": \"system\",\n            \"content\": \"Extract the city, time (or date), and intent (current, historical, forecast) from the user's query. Return the result as JSON with keys city, time, intent.\"\n          },\n          {\n            \"role\": \"user\",\n            \"content\": \"{{ $json.message }}\"\n          }\n        ],\n        \"responseFormat\": \"json\"\n      },\n      \"name\": \"Parse Query\",\n      \"type\": \"n8n-nodes-base.openai\",\n      \"typeVersion\": 1,\n      \"position\": [\n        200,\n        0\n      ]\n    },\n    {\n      \"parameters\": {\n        \"conditions\": [\n          {\n            \"field\": \"{{ $json.city }}\",\n            \"operation\": \"is empty\",\n            \"value\": \"\"\n          }\n        ]\n      },\n      \"name\": \"Check City\",\n      \"type\": \"n8n-nodes-base.if\",\n      \"typeVersion\": 1,\n      \"position\": [\n        200,\n        200\n      ]\n    },\n    {\n      \"parameters\": {\n        \"model\": \"gpt-4o-mini\",\n        \"messages\": [\n          {\n            \"role\": \"system\",\n            \"content\": \"Ask the user to provide the missing city name.\"\n          },\n          {\n            \"role\": \"assistant\",\n            \"content\": \"I noticed you didn't mention the city. Could you clarify which city you want the weather for?\"\n          }\n        ],\n        \"responseFormat\": \"text\"\n      },\n      \"name\": \"Clarify Question\",\n      \"type\": \"n8n-nodes-base.openai\",\n      \"typeVersion\": 1,\n      \"position\": [\n        400,\n        200\n      ]\n    },\n    {\n      \"parameters\": {\n        \"url\": \"https://api.openweathermap.org/data/2.5/weather\",\n        \"method\": \"GET\",\n        \"queryParameters\": [\n          {\n            \"name\": \"q\",\n            \"value\": \"{{ $json.city }}\"\n          },\n          {\n            \"name\": \"appid\",\n            \"value\": \"YOUR_API_KEY_HERE\"\n          },\n          {\n            \"name\": \"units\",\n            \"value\": \"metric\"\n          }\n        ]\n      },\n      \"name\": \"Fetch Weather\",\n      \"type\": \"n8n-nodes-base.httpRequest\",\n      \"typeVersion\": 1,\n      \"position\": [\n        400,\n        400\n      ]\n    },\n    {\n      \"parameters\": {\n        \"model\": \"gpt-4o-mini\",\n        \"messages\": [\n          {\n            \"role\": \"system\",\n            \"content\": \"Using the following temperature and weather description, write a friendly response to the user. If the intent was historical or forecast, adjust accordingly.\"\n          },\n          {\n            \"role\": \"assistant\",\n            \"content\": \"Weather in {{ $json.city }} is {{ $json.response.main.temp }}°C with {{ $json.response.weather[0].description }}.\"\n          }\n        ],\n        \"responseFormat\": \"text\"\n      },\n      \"name\": \"Generate Response\",\n      \"type\": \"n8n-nodes-base.openai\",\n      \"typeVersion\": 1,\n      \"position\": [\n        600,\n        300\n      ]\n    }\n  ],\n  \"connections\": {\n    \"Webhook\": {\n      \"main\": [\n        [\n          {\n            \"node\": \"Parse Query\",\n            \"type\": \"main\",\n            \"index\": 0\n          }\n        ]\n      ]\n    },\n    \"Parse Query\": {\n      \"main\": [\n        [\n          {\n            \"node\": \"Check City\",\n            \"type\": \"main\",\n            \"index\": 0\n          }\n        ]\n      ]\n    },\n    \"Check City\": {\n      \"main\": [\n        [\n          {\n            \"node\": \"Clarify Question\",\n            \"type\": \"main\",\n            \"index\": 0\n          }\n        ],\n        [\n          {\n            \"node\": \"Fetch Weather\",\n            \"type\": \"main\",\n            \"index\": 0\n          }\n        ]\n      ]\n    },\n    \"Fetch Weather\": {\n      \"main\": [\n        [\n          {\n            \"node\": \"Generate Response\",\n            \"type\": \"main\",\n            \"index\": 0\n          }\n        ]\n      ]\n    }\n  },\n  \"active\": false\n}"], ["43", "{\n  \"name\": \"Zoom & Stripe Automation\",\n  \"nodes\": [\n    {\n      \"parameters\": {\n        \"httpMethod\": \"POST\",\n        \"path\": \"form\",\n        \"cookieDomain\": [],\n        \"excludeBody\": false,\n        \"addEventData\": true\n      },\n      \"name\": \"Form Trigger\",\n      \"type\": \"n8n-nodes-base.webhook\",\n      \"typeVersion\": 1,\n      \"position\": [\n        100,\n        200\n      ]\n    },\n    {\n      \"parameters\": {\n        \"operation\": \"create\",\n        \"type\": \"meeting\",\n        \"aud\": \"corporate\",\n        \"topic\": \"={{$json[\\\"topic\\\"]}}\",\n        \"start_time\": \"={{$json[\\\"start_time\\\"]}}\",\n        \"duration\": \"46\",\n        \"timezone\": \"UTC\"\n      },\n      \"name\": \"Zoom Create Meeting\",\n      \"type\": \"n8n-nodes-base.zoom\",\n      \"typeVersion\": 1,\n      \"position\": [\n        300,\n        200\n      ]\n    },\n    {\n      \"parameters\": {\n        \"operation\": \"appendRow\",\n        \"options\": {\n          \"sheetId\": \"YOUR_SHEET_ID\",\n          \"sheetName\": \"Participants\",\n          \"row\": [\n            \"{{ $json[\\\"participantName\\\"] }}\",\n            \"{{ $json[\\\"participantEmail\\\"] }}\"\n          ]\n        }\n      },\n      \"name\": \"Google Sheets Append\",\n      \"type\": \"n8n-nodes-base.googleSheets\",\n      \"typeVersion\": 1,\n      \"position\": [\n        500,\n        200\n      ]\n    },\n    {\n      \"parameters\": {\n        \"to\": [\n          \"<EMAIL>\"\n        ],\n        \"subject\": \"New Zoom Meeting Created\",\n        \"text\": \"A new Zoom meeting has been scheduled for {{ $json[\\\"topic\\\"] }} on {{ $json[\\\"start_time\\\"] }}.\"\n      },\n      \"name\": \"Email to Teacher\",\n      \"type\": \"n8n-nodes-base.sendEmail\",\n      \"typeVersion\": 1,\n      \"position\": [\n        700,\n        150\n      ]\n    },\n    {\n      \"parameters\": {\n        \"to\": [\n          \"{{ $json[\\\"participantEmail\\\"] }}\"\n        ],\n        \"subject\": \"Your Zoom Meeting Details\",\n        \"text\": \"Your Zoom meeting details:\\n\\nTopic: {{ $json[\\\"topic\\\"] }}\\nLink: {{ items[0].json['join_url'] }}\"\n      },\n      \"name\": \"Email to Participant\",\n      \"type\": \"n8n-nodes-base.sendEmail\",\n      \"typeVersion\": 1,\n      \"position\": [\n        700,\n        250\n      ]\n    },\n    {\n      \"parameters\": {\n        \"event\": \"checkout.session.completed\",\n        \"connectWithWebhook\": false,\n        \"webhookUrl\": \"\"\n      },\n      \"name\": \"Stripe Event Trigger\",\n      \"type\": \"n8n-nodes-base.stripe\",\n      \"typeVersion\": 1,\n      \"position\": [\n        100,\n        400\n      ]\n    },\n    {\n      \"parameters\": {\n        \"operation\": \"createProduct\",\n        \"name\": \"Course Subscription\",\n        \"description\": \"Subscription to our online course.\",\n        \"amount\": 999,\n        \"currency\": \"usd\",\n        \"unit_label\": \"subscription\"\n      },\n      \"name\": \"Stripe Create Product\",\n      \"type\": \"n8n-nodes-base.stripe\",\n      \"typeVersion\": 1,\n      \"position\": [\n        300,\n        400\n      ]\n    },\n    {\n      \"parameters\": {\n        \"operation\": \"createCheckoutSession\",\n        \"payment_method_types\": [\n          \"card\"\n        ],\n        \"line_items\": [\n          {\n            \"price_data\": {\n              \"currency\": \"usd\",\n              \"product\": \"{{ $json[\\\"product\\\"].id }}\",\n              \"unit_amount\": 999\n            },\n            \"quantity\": 1\n          }\n        ],\n        \"success_url\": \"https://example.com/success\",\n        \"cancel_url\": \"https://example.com/cancel\"\n      },\n      \"name\": \"Stripe Create Checkout Session\",\n      \"type\": \"n8n-nodes-base.stripe\",\n      \"typeVersion\": 1,\n      \"position\": [\n        500,\n        400\n      ]\n    },\n    {\n      \"parameters\": {\n        \"to\": [\n          \"<EMAIL>\"\n        ],\n        \"subject\": \"New Stripe Checkout Session\",\n        \"text\": \"A new checkout session has been created. Session URL: {{ items[0].json['url'] }}.\"\n      },\n      \"name\": \"Email to Teacher (Stripe)\",\n      \"type\": \"n8n-nodes-base.sendEmail\",\n      \"typeVersion\": 1,\n      \"position\": [\n        700,\n        400\n      ]\n    }\n  ],\n  \"connections\": {\n    \"Form Trigger\": {\n      \"main\": [\n        [\n          {\n            \"node\": \"Zoom Create Meeting\",\n            \"type\": \"main\",\n            \"index\": 0\n          }\n        ]\n      ]\n    },\n    \"Zoom Create Meeting\": {\n      \"main\": [\n        [\n          {\n            \"node\": \"Google Sheets Append\",\n            \"type\": \"main\",\n            \"index\": 0\n          }\n        ]\n      ]\n    },\n    \"Google Sheets Append\": {\n      \"main\": [\n        [\n          {\n            \"node\": \"Email to Teacher\",\n            \"type\": \"main\",\n            \"index\": 0\n          },\n          {\n            \"node\": \"Email to Participant\",\n            \"type\": \"main\",\n            \"index\": 0\n          }\n        ]\n      ]\n    },\n    \"Stripe Event Trigger\": {\n      \"main\": [\n        [\n          {\n            \"node\": \"Stripe Create Product\",\n            \"type\": \"main\",\n            \"index\": 0\n          }\n        ]\n      ]\n    },\n    \"Stripe Create Product\": {\n      \"main\": [\n        [\n          {\n            \"node\": \"Stripe Create Checkout Session\",\n            \"type\": \"main\",\n            \"index\": 0\n          }\n        ]\n      ]\n    },\n    \"Stripe Create Checkout Session\": {\n      \"main\": [\n        [\n          {\n            \"node\": \"Email to Teacher (Stripe)\",\n            \"type\": \"main\",\n            \"index\": 0\n          }\n        ]\n      ]\n    }\n  },\n  \"active\": false\n}"], ["44", "{\n  \"name\": \"Retrieve All Shopify Products\",\n  \"nodes\": [\n    {\n      \"parameters\": {},\n      \"name\": \"Manual Trigger\",\n      \"type\": \"n8n-nodes-base.manualTrigger\",\n      \"typeVersion\": 1,\n      \"position\": [\n        200,\n        200\n      ]\n    },\n    {\n      \"parameters\": {\n        \"resource\": \"product\",\n        \"operation\": \"getAll\",\n        \"returnAll\": true,\n        \"credentials\": {\n          \"shopifyApi\": \"shopify_creds\"\n        }\n      },\n      \"name\": \"Shopify\",\n      \"type\": \"n8n-nodes-base.shopify\",\n      \"typeVersion\": 1,\n      \"position\": [\n        600,\n        200\n      ]\n    }\n  ],\n  \"connections\": {\n    \"Manual Trigger\": {\n      \"main\": [\n        [\n          {\n            \"node\": \"Shopify\",\n            \"type\": \"main\",\n            \"index\": 0\n          }\n        ]\n      ]\n    }\n  },\n  \"active\": false\n}"], ["45", "{\n  \"name\": \"Twilio Test\",\n  \"nodes\": [\n    {\n      \"parameters\": {\n        \"webhookEvent\": \"inboundMessage\",\n        \"credentials\": \"Twilio Credentials\"\n      },\n      \"name\": \"InboundTwilioTrigger\",\n      \"type\": \"twilioTrigger\",\n      \"typeVersion\": 1,\n      \"position\": [\n        250,\n        300\n      ]\n    }\n  ],\n  \"connections\": {},\n  \"active\": false\n}"], ["46", "{\n  \"name\": \"WooCommerce Update Rank Math SEO Meta\",\n  \"nodes\": [\n    {\n      \"parameters\": {},\n      \"name\": \"Manual Trigger\",\n      \"type\": \"n8n-nodes-base.manualTrigger\",\n      \"typeVersion\": 1,\n      \"position\": [\n        200,\n        200\n      ]\n    },\n    {\n      \"parameters\": {\n        \"values\": [\n          {\n            \"name\": \"woocommerceUrl\",\n            \"value\": \"https://example.com\"\n          }\n        ],\n        \"options\": {}\n      },\n      \"name\": \"Set WooCommerce URL\",\n      \"type\": \"n8n-nodes-base.set\",\n      \"typeVersion\": 1,\n      \"position\": [\n        400,\n        200\n      ]\n    },\n    {\n      \"parameters\": {\n        \"url\": \"{{$node[\\'Set WooCommerce URL\\'].json.woocommerceUrl}}/wp-json/rank-math-api/v1/update-meta\",\n        \"method\": \"POST\",\n        \"authentication\": \"basicAuth\",\n        \"username\": \"admin\",\n        \"password\": \"password\",\n        \"bodyParametersJson\": {\n          \"post_id\": 246,\n          \"title\": \"Your New Title\",\n          \"description\": \"Your SEO description.\",\n          \"canonical\": \"https://example.com/custom-canonical\"\n        },\n        \"retryOnFail\": true\n      },\n      \"name\": \"Update Rank Math Meta\",\n      \"type\": \"n8n-nodes-base.httpRequest\",\n      \"typeVersion\": 1,\n      \"position\": [\n        600,\n        200\n      ]\n    }\n  ],\n  \"connections\": {\n    \"Manual Trigger\": {\n      \"main\": [\n        [\n          {\n            \"node\": \"Set WooCommerce URL\",\n            \"type\": \"main\",\n            \"index\": 0\n          }\n        ]\n      ]\n    },\n    \"Set WooCommerce URL\": {\n      \"main\": [\n        [\n          {\n            \"node\": \"Update Rank Math Meta\",\n            \"type\": \"main\",\n            \"index\": 0\n          }\n        ]\n      ]\n    }\n  },\n  \"active\": false\n}"], ["47", "{\n  \"name\": \"Telegram Reacts to Sheet\",\n  \"nodes\": [\n    {\n      \"parameters\": {\n        \"operation\": \"watch\",\n        \"googleSheetsId\": \"YOUR_SHEET_ID\",\n        \"range\": \"Sheet1!A:C\"\n      },\n      \"name\": \"GoogleSheetsTrigger\",\n      \"type\": \"n8n-nodes-base.googleSheets\",\n      \"typeVersion\": 1,\n      \"position\": [0, 0]\n    },\n    {\n      \"parameters\": {\n        \"functionCode\": \"return { json: { telegramLink: $node['GoogleSheetsTrigger'].json['TelegramLink'] } };\"\n      },\n      \"name\": \"Parse Telegram Link\",\n      \"type\": \"n8n-nodes-base.function\",\n      \"typeVersion\": 1,\n      \"position\": [200, 0]\n    },\n    {\n      \"parameters\": {\n        \"url\": \"https://api.telegram.org/botYOUR_BOT_TOKEN/getReactions?url={{ $json['telegramLink'] }}\",\n        \"method\": \"GET\"\n      },\n      \"name\": \"Get Telegram Reactions\",\n      \"type\": \"n8n-nodes-base.httpRequest\",\n      \"typeVersion\": 1,\n      \"position\": [400, 0]\n    },\n    {\n      \"parameters\": {\n        \"functionCode\": \"let reactions = [];\\ntry { reactions = items[0].json['results']['reactions'] || []; }\\ncatch(e){}\\nlet reactionCount = 0;\\nreactions.forEach(r => { if(r.count) reactionCount += r.count; });\\nreturn { json: { reactionCount } };\"\n      },\n      \"name\": \"Parse Reaction Data\",\n      \"type\": \"n8n-nodes-base.function\",\n      \"typeVersion\": 1,\n      \"position\": [600, 0]\n    },\n    {\n      \"parameters\": {\n        \"conditions\": {\n          \"boolean\": [\n            {\n              \"value1\": \"{{ $json['reactionCount'] }}\",\n              \"operation\": \"greaterThan\",\n              \"value2\": 0,\n              \"name\": \"HasReactions\"\n            }\n          ]\n        }\n      },\n      \"name\": \"Condition Check\",\n      \"type\": \"n8n-nodes-base.condition\",\n      \"typeVersion\": 1,\n      \"position\": [800, 0]\n    },\n    {\n      \"parameters\": {\n        \"googleSheetsId\": \"YOUR_SHEET_ID\",\n        \"range\": \"Sheet1!C1\",\n        \"values\": [\n          [\n            \"{{ $json['reactionCount'] }}\"\n          ]\n        ],\n        \"operation\": \"appendRow\"\n      },\n      \"name\": \"Update Google Sheets\",\n      \"type\": \"n8n-nodes-base.googleSheets\",\n      \"typeVersion\": 1,\n      \"position\": [1000, 0]\n    }\n  ],\n  \"connections\": {\n    \"GoogleSheetsTrigger\": {\n      \"main\": [\n        [\n          {\n            \"node\": \"Parse Telegram Link\",\n            \"type\": \"main\",\n            \"index\": 0\n          }\n        ]\n      ]\n    },\n    \"Parse Telegram Link\": {\n      \"main\": [\n        [\n          {\n            \"node\": \"Get Telegram Reactions\",\n            \"type\": \"main\",\n            \"index\": 0\n          }\n        ]\n      ]\n    },\n    \"Get Telegram Reactions\": {\n      \"main\": [\n        [\n          {\n            \"node\": \"Parse Reaction Data\",\n            \"type\": \"main\",\n            \"index\": 0\n          }\n        ]\n      ]\n    },\n    \"Parse Reaction Data\": {\n      \"main\": [\n        [\n          {\n            \"node\": \"Condition Check\",\n            \"type\": \"main\",\n            \"index\": 0\n          }\n        ]\n      ]\n    },\n    \"Condition Check\": {\n      \"main\": [\n        [\n          {\n            \"node\": \"Update Google Sheets\",\n            \"type\": \"main\",\n            \"index\": 0\n          }\n        ]\n      ]\n    }\n  },\n  \"active\": false\n}"], ["48", "{\n  \"id\": \"f8c9d73c-4e1e-4a4e-99f2-9b8e6e5d3e6a\",\n  \"name\": \"email extractor in airtable\",\n  \"createdAt\": \"2025-08-07T12:00:00Z\",\n  \"updatedAt\": \"2025-08-07T12:00:00Z\",\n  \"status\": \"inactive\",\n  \"executionOrder\": \"v1\",\n  \"active\": false,\n  \"nodes\": [],\n  \"connections\": {}\n}"], ["49", "{\n  \"name\": \"Parallel Set Workflow\",\n  \"nodes\": [\n    {\n      \"parameters\": {\n        \"intervalSeconds\": 3600,\n        \"timezone\": \"UTC\"\n      },\n      \"name\": \"Schedule Trigger\",\n      \"type\": \"n8n-nodes-base.schedule\",\n      \"typeVersion\": 1,\n      \"position\": [0, 0]\n    },\n    {\n      \"parameters\": {\n        \"values\": {\n          \"json\": [\n            {\n              \"name\": \"First item\",\n              \"code\": 1\n            },\n            {\n              \"name\": \"Second item\",\n              \"code\": 2\n            }\n          ]\n        },\n        \"alwaysSet\": true,\n        \"options\": {\n          \"setDefaultValues\": true\n        }\n      },\n      \"name\": \"Edit Fields7\",\n      \"type\": \"n8n-nodes-base.set\",\n      \"typeVersion\": 1,\n      \"position\": [300, 300]\n    },\n    {\n      \"parameters\": {\n        \"values\": {\n          \"json\": [\n            {\n              \"name\": \"First item\",\n              \"code\": 1\n            },\n            {\n              \"name\": \"Second item\",\n              \"code\": 2\n            }\n          ]\n        },\n        \"alwaysSet\": true,\n        \"options\": {\n          \"setDefaultValues\": true\n        }\n      },\n      \"name\": \"Edit Fields2\",\n      \"type\": \"n8n-nodes-base.set\",\n      \"typeVersion\": 1,\n      \"position\": [300, 500]\n    }\n  ],\n  \"connections\": {\n    \"Schedule Trigger\": {\n      \"main\": [\n        [\n          {\n            \"node\": \"Edit Fields7\",\n            \"type\": \"main\",\n            \"index\": 0\n          }\n        ],\n        [\n          {\n            \"node\": \"Edit Fields2\",\n            \"type\": \"main\",\n            \"index\": 0\n          }\n        ]\n      ]\n    }\n  },\n  \"active\": false\n}"]], "shape": {"columns": 1, "rows": 63}}, "text/plain": ["0     {\\n  \"name\": \"Backup Workflows\",\\n  \"nodes\": [...\n", "1     {\\n  \"name\": \"+11 AI-Powered RAG Workflow For ...\n", "2     {\\n  \"id\": \"placeholder-id\",\\n  \"versionId\": \"...\n", "3     {\\n  \"name\": \"Daily Stock Trade Recommendation...\n", "4     {\\n  \"name\": \"AppTest n8n Onboarding Scaling A...\n", "                            ...                        \n", "58    {\\n  \"name\": \"Xero Quote and Invoice Workflow\"...\n", "59                                                     \n", "60                                                     \n", "61    {\\n  \"name\": \"GitHub Notifications to Discord\"...\n", "62                                                     \n", "Name: generated_json, Length: 63, dtype: object"]}, "execution_count": 33, "metadata": {}, "output_type": "execute_result"}], "source": ["df[\"generated_json\"]"]}, {"cell_type": "code", "execution_count": 34, "id": "eafc097f", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "\n", "\n", "\n", "\n", "{\n", "  \"name\": \"Auto Appointment Response\",\n", "  \"nodes\": [\n", "    {\n", "      \"parameters\": {\n", "        \"mode\": \"trigger\",\n", "        \"triggerType\": \"gmailNew\",\n", "        \"auth\": \"default\",\n", "        \"label\": \"emailMatch\",\n", "        \"options\": {\n", "          \"subject\": \"\",\n", "          \"from\": \"\",\n", "          \"until\": \"\"\n", "        }\n", "      },\n", "      \"name\": \"<PERSON><PERSON><PERSON><PERSON><PERSON>\",\n", "      \"type\": \"n8n-nodes-base.gmailTrigger\",\n", "      \"typeVersion\": 4,\n", "      \"position\": [\n", "        0,\n", "        0\n", "      ]\n", "    },\n", "    {\n", "      \"parameters\": {\n", "        \"code\": \"return items.map(item => ({ json: { emailContent: item.json.body }}));\"\n", "      },\n", "      \"name\": \"ExtractEmailContent\",\n", "      \"type\": \"n8n-nodes-base.function\",\n", "      \"typeVersion\": 1,\n", "      \"position\": [\n", "        300,\n", "        0\n", "      ]\n", "    },\n", "    {\n", "      \"parameters\": {\n", "        \"model\": \"gpt-4o-mini\",\n", "        \"inputMessageField\": \"emailContent\",\n", "        \"additionalPrompt\": \"Classify if this email is an appointment request and extract \\\"senderEmail\\\" and \\\"subject\\\".\",\n", "        \"responseParser\": \"json\"\n", "      },\n", "      \"name\": \"OpenAIClassify\",\n", "      \"type\": \"n8n-nodes-base.openAI\",\n", "      \"typeVersion\": 1,\n", "      \"position\": [\n", "        600,\n", "        0\n", "      ]\n", "    },\n", "    {\n", "      \"parameters\": {\n", "        \"code\": \"return items.map(item => {\\n  const data = item.json.openAIResponse || {};\\n  return { json: { emailContent: item.json.emailContent, sender: data.senderEmail, subject: data.subject }};\\n});\"\n", "      },\n", "      \"name\": \"ParseResponse\",\n", "      \"type\": \"n8n-nodes-base.function\",\n", "      \"typeVersion\": 1,\n", "      \"position\": [\n", "        900,\n", "        0\n", "      ]\n", "    },\n", "    {\n", "      \"parameters\": {\n", "        \"eventType\": \"freeBusy\",\n", "        \"timeMin\": \"{{$json[\\\"timestamp\\\"]}}\",\n", "        \"timeMax\": \"$now + 30d\",\n", "        \"timeZone\": \"UTC\",\n", "        \"items\": [\n", "          {\n", "            \"resourceId\": \"primary\"\n", "          }\n", "        ],\n", "        \"output\": \"structured\",\n", "        \"calendarId\": \"primary\"\n", "      },\n", "      \"name\": \"<PERSON><PERSON><PERSON><PERSON>\",\n", "      \"type\": \"n8n-nodes-base.googleCalendar\",\n", "      \"typeVersion\": 1,\n", "      \"position\": [\n", "        1200,\n", "        0\n", "      ]\n", "    },\n", "    {\n", "      \"parameters\": {\n", "        \"code\": \"return items.map(item => {\\n  const times = item.json.calendarFreeBusy?.slots || [];\\n  const replyBody = `Hello,\\\\n\\\\nThank you for your appointment request.\\\\nHere are some available times:\\\\n` + times.map((t, i) => `${i+1}. ${t}`).join('\\\\n') + '\\\\n\\\\nPlease let us know which works for you.\\\\n\\\\nBest regards,\\\\nYour Scheduler';\\n  return { json: { replyBody, sender: item.json.emailContent, subject: item.json.subject + ' - Re', originalMessageId: item.json.id } };\\n});\"\n", "      },\n", "      \"name\": \"<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\",\n", "      \"type\": \"n8n-nodes-base.function\",\n", "      \"typeVersion\": 1,\n", "      \"position\": [\n", "        1500,\n", "        0\n", "      ]\n", "    },\n", "    {\n", "      \"parameters\": {\n", "        \"accountId\": \"default\",\n", "        \"to\": \"={{$json['sender']}}\",\n", "        \"cc\": \"\",\n", "        \"bcc\": \"\",\n", "        \"subject\": \"={{$json.subject}}\",\n", "        \"bodyHtml\": \"={{$json.replyBody}}\",\n", "        \"replyToMessageId\": \"={{$json.originalMessageId}}\",\n", "        \"sendMessage\": true\n", "      },\n", "      \"name\": \"<PERSON><PERSON><PERSON><PERSON>\",\n", "      \"type\": \"n8n-nodes-base.gmail\",\n", "      \"typeVersion\": 1,\n", "      \"position\": [\n", "        1800,\n", "        0\n", "      ]\n", "    },\n", "    {\n", "      \"parameters\": {\n", "        \"action\": \"markRead\",\n", "        \"messageIds\": \"={{$json.originalMessageId}}\"\n", "      },\n", "      \"name\": \"<PERSON><PERSON><PERSON>\",\n", "      \"type\": \"n8n-nodes-base.gmail\",\n", "      \"typeVersion\": 1,\n", "      \"position\": [\n", "        2100,\n", "        0\n", "      ]\n", "    }\n", "  ],\n", "  \"connections\": {\n", "    \"GmailTrigger\": {\n", "      \"main\": [\n", "        [\n", "          {\n", "            \"node\": \"ExtractEmailContent\",\n", "            \"type\": \"main\",\n", "            \"index\": 0\n", "          }\n", "        ]\n", "      ]\n", "    },\n", "    \"ExtractEmailContent\": {\n", "      \"main\": [\n", "        [\n", "          {\n", "            \"node\": \"OpenAIClassify\",\n", "            \"type\": \"main\",\n", "            \"index\": 0\n", "          }\n", "        ]\n", "      ]\n", "    },\n", "    \"OpenAIClassify\": {\n", "      \"main\": [\n", "        [\n", "          {\n", "            \"node\": \"ParseResponse\",\n", "            \"type\": \"main\",\n", "            \"index\": 0\n", "          }\n", "        ]\n", "      ]\n", "    },\n", "    \"ParseResponse\": {\n", "      \"main\": [\n", "        [\n", "          {\n", "            \"node\": \"CalendarCheck\",\n", "            \"type\": \"main\",\n", "            \"index\": 0\n", "          }\n", "        ]\n", "      ]\n", "    },\n", "    \"CalendarCheck\": {\n", "      \"main\": [\n", "        [\n", "          {\n", "            \"node\": \"ComposeReply\",\n", "            \"type\": \"main\",\n", "            \"index\": 0\n", "          }\n", "        ]\n", "      ]\n", "    },\n", "    \"ComposeReply\": {\n", "      \"main\": [\n", "        [\n", "          {\n", "            \"node\": \"DraftReply\",\n", "            \"type\": \"main\",\n", "            \"index\": 0\n", "          }\n", "        ]\n", "      ]\n", "    },\n", "    \"DraftReply\": {\n", "      \"main\": [\n", "        [\n", "          {\n", "            \"node\": \"MarkRead\",\n", "            \"type\": \"main\",\n", "            \"index\": 0\n", "          }\n", "        ]\n", "      ]\n", "    }\n", "  ],\n", "  \"active\": false\n", "}\n", "{\n", "  \"name\": \"WooCommerce Update Rank Math SEO Meta\",\n", "  \"nodes\": [\n", "    {\n", "      \"parameters\": {},\n", "      \"name\": \"<PERSON> Trigger\",\n", "      \"type\": \"n8n-nodes-base.manualTrigger\",\n", "      \"typeVersion\": 1,\n", "      \"position\": [\n", "        200,\n", "        200\n", "      ]\n", "    },\n", "    {\n", "      \"parameters\": {\n", "        \"values\": [\n", "          {\n", "            \"name\": \"woocommerceUrl\",\n", "            \"value\": \"https://example.com\"\n", "          }\n", "        ],\n", "        \"options\": {}\n", "      },\n", "      \"name\": \"Set WooCommerce URL\",\n", "      \"type\": \"n8n-nodes-base.set\",\n", "      \"typeVersion\": 1,\n", "      \"position\": [\n", "        400,\n", "        200\n", "      ]\n", "    },\n", "    {\n", "      \"parameters\": {\n", "        \"url\": \"{{$node[\\'Set WooCommerce URL\\'].json.woocommerceUrl}}/wp-json/rank-math-api/v1/update-meta\",\n", "        \"method\": \"POST\",\n", "        \"authentication\": \"basicAuth\",\n", "        \"username\": \"admin\",\n", "        \"password\": \"password\",\n", "        \"bodyParametersJson\": {\n", "          \"post_id\": 246,\n", "          \"title\": \"Your New Title\",\n", "          \"description\": \"Your SEO description.\",\n", "          \"canonical\": \"https://example.com/custom-canonical\"\n", "        },\n", "        \"retryOnFail\": true\n", "      },\n", "      \"name\": \"Update Rank Math Meta\",\n", "      \"type\": \"n8n-nodes-base.httpRequest\",\n", "      \"typeVersion\": 1,\n", "      \"position\": [\n", "        600,\n", "        200\n", "      ]\n", "    }\n", "  ],\n", "  \"connections\": {\n", "    \"Manual Trigger\": {\n", "      \"main\": [\n", "        [\n", "          {\n", "            \"node\": \"Set WooCommerce URL\",\n", "            \"type\": \"main\",\n", "            \"index\": 0\n", "          }\n", "        ]\n", "      ]\n", "    },\n", "    \"Set WooCommerce URL\": {\n", "      \"main\": [\n", "        [\n", "          {\n", "            \"node\": \"Update Rank Math Meta\",\n", "            \"type\": \"main\",\n", "            \"index\": 0\n", "          }\n", "        ]\n", "      ]\n", "    }\n", "  },\n", "  \"active\": false\n", "}\n", "\n", "\n", "\n"]}], "source": ["nodes = []\n", "for json_ in df[\"generated_json\"]:\n", "    try:\n", "        n8n_workfow = json.loads(json_)\n", "    except:\n", "        print(json_)\n", "        continue\n", "    # print(n8n_workfow[\"nodes\"])\n", "    nodes.extend(n8n_workfow[\"nodes\"])"]}, {"cell_type": "code", "execution_count": 35, "id": "d24b48f1", "metadata": {}, "outputs": [{"data": {"text/plain": ["214"]}, "execution_count": 35, "metadata": {}, "output_type": "execute_result"}], "source": ["len(nodes)"]}, {"cell_type": "code", "execution_count": 37, "id": "1489c009", "metadata": {}, "outputs": [], "source": ["count = 0\n", "for n in nodes:\n", "    if n[\"type\"] in valid_node_and_version:\n", "        count += 1"]}, {"cell_type": "code", "execution_count": 38, "id": "b4a1af75", "metadata": {}, "outputs": [{"data": {"text/plain": ["171"]}, "execution_count": 38, "metadata": {}, "output_type": "execute_result"}], "source": ["count"]}, {"cell_type": "code", "execution_count": 40, "id": "70b980c7", "metadata": {}, "outputs": [], "source": ["node_lowercase = list(valid_node_and_version.keys())\n", "for i in range(len(node_lowercase)):\n", "    node_lowercase[i] = node_lowercase[i].lower()"]}, {"cell_type": "code", "execution_count": 44, "id": "bc4fa3be", "metadata": {}, "outputs": [], "source": ["count = 0\n", "for n in nodes:\n", "    if n[\"type\"].lower() in node_lowercase:\n", "        count += 1"]}, {"cell_type": "code", "execution_count": 45, "id": "4d3e47b6", "metadata": {}, "outputs": [{"data": {"text/plain": ["179"]}, "execution_count": 45, "metadata": {}, "output_type": "execute_result"}], "source": ["count"]}, {"cell_type": "code", "execution_count": 1, "id": "648a7f56", "metadata": {}, "outputs": [], "source": ["import pandas as pd"]}, {"cell_type": "code", "execution_count": 2, "id": "7a034523", "metadata": {}, "outputs": [], "source": ["dataset=pd.read_json(\"result_openai_orr_20B-0.json\")"]}, {"cell_type": "code", "execution_count": 4, "id": "b221132a", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{\"name\": \"Main workflow loop backup\", \"nodes\": [{\"parameters\": {}, \"name\": \"On clicking 'execute'\", \"type\": \"n8n-nodes-base.manualTrigger\", \"typeVersion\": 1, \"position\": [1640, 580]}, {\"parameters\": {\"filters\": {}, \"requestOptions\": {}}, \"name\": \"n8n\", \"type\": \"n8n-nodes-base.n8n\", \"typeVersion\": 1, \"position\": [2040, 680]}, {\"parameters\": {\"options\": {}}, \"name\": \"Loop Over Items\", \"type\": \"n8n-nodes-base.splitInBatches\", \"typeVersion\": 3, \"position\": [2240, 680]}, {\"parameters\": {\"rule\": {\"interval\": [{\"triggerAtHour\": 1, \"triggerAtMinute\": 33}]}}, \"name\": \"Schedule Trigger\", \"type\": \"n8n-nodes-base.scheduleTrigger\", \"typeVersion\": 1.2, \"position\": [1620, 800]}, {\"parameters\": {\"content\": \"## Main workflow loop\", \"height\": 416.1856906618075, \"width\": 1272.6408145680155, \"color\": 7}, \"name\": \"Sticky Note2\", \"type\": \"n8n-nodes-base.stickyNote\", \"typeVersion\": 1, \"position\": [1620, 500]}, {\"parameters\": {\"workflowId\": \"=rkExj7m8P27lT2xs\", \"options\": {}}, \"name\": \"Execute Workflow\", \"type\": \"n8n-nodes-base.executeWorkflow\", \"typeVersion\": 1, \"position\": [2460, 720]}, {\"parameters\": {\"sendTo\": \"<EMAIL>\", \"subject\": \"=Backup Started {{ $execution.id }}\", \"emailType\": \"text\", \"message\": \"=:information_source:  Starting Workflow Backup [{{ $execution.id }}]\", \"options\": {}}, \"name\": \"Gmail\", \"type\": \"n8n-nodes-base.gmail\", \"typeVersion\": 2.1, \"position\": [1860, 220]}, {\"parameters\": {\"sendTo\": \"<EMAIL>\", \"subject\": \"=Backup Started {{ $execution.id }}\", \"emailType\": \"text\", \"message\": \"=\\u2705 Backup has completed - {{ $('n8n').all().length }} workflows have been processed.\", \"options\": {}}, \"name\": \"Gmail1\", \"type\": \"n8n-nodes-base.gmail\", \"typeVersion\": 2.1, \"position\": [2460, 360]}, {\"parameters\": {\"sendTo\": \"<EMAIL>\", \"subject\": \"=Backup Started {{ $execution.id }}\", \"emailType\": \"text\", \"message\": \"=:x: Failed to backup {{ $('Loop Over Items').item.json.id }}\", \"options\": {}}, \"name\": \"Gmail2\", \"type\": \"n8n-nodes-base.gmail\", \"typeVersion\": 2.1, \"position\": [2660, 1020]}, {\"parameters\": {\"chatId\": \"489359810\", \"text\": \"=:information_source:  Starting Workflow Backup [{{ $execution.id }}]\", \"additionalFields\": {}}, \"name\": \"Telegram\", \"type\": \"n8n-nodes-base.telegram\", \"typeVersion\": 1.2, \"position\": [1860, 680]}, {\"parameters\": {\"chatId\": \"489359810\", \"text\": \"=Backup n8n.psyii.od.ua/workflow Done {{ $execution.id }}\", \"additionalFields\": {}}, \"name\": \"Telegram1\", \"type\": \"n8n-nodes-base.telegram\", \"typeVersion\": 1.2, \"position\": [2460, 520]}, {\"parameters\": {\"chatId\": \"489359810\", \"text\": \"=:x: Failed to backup {{ $('Loop Over Items').item.json.id }}\", \"additionalFields\": {}}, \"name\": \"Telegram2\", \"type\": \"n8n-nodes-base.telegram\", \"typeVersion\": 1.2, \"position\": [2720, 780]}], \"connections\": {\"On clicking 'execute'\": {\"main\": [[{\"node\": \"Telegram\", \"type\": \"main\", \"index\": 0}]]}, \"n8n\": {\"main\": [[{\"node\": \"Loop Over Items\", \"type\": \"main\", \"index\": 0}]]}, \"Loop Over Items\": {\"main\": [[{\"node\": \"Telegram1\", \"type\": \"main\", \"index\": 0}], [{\"node\": \"Execute Workflow\", \"type\": \"main\", \"index\": 0}]]}, \"Schedule Trigger\": {\"main\": [[{\"node\": \"Telegram\", \"type\": \"main\", \"index\": 0}]]}, \"Execute Workflow\": {\"main\": [[{\"node\": \"Loop Over Items\", \"type\": \"main\", \"index\": 0}], [{\"node\": \"Telegram2\", \"type\": \"main\", \"index\": 0}]]}, \"Gmail\": {\"main\": [[]]}, \"Gmail2\": {\"main\": [[]]}, \"Telegram\": {\"main\": [[{\"node\": \"n8n\", \"type\": \"main\", \"index\": 0}]]}, \"Telegram2\": {\"main\": [[{\"node\": \"Loop Over Items\", \"type\": \"main\", \"index\": 0}]]}}, \"active\": false}\n"]}], "source": ["print(dataset.loc[0, \"json\"])"]}, {"cell_type": "code", "execution_count": 5, "id": "57117ba0", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{\n", "  \"name\": \"Backup Workflows\",\n", "  \"nodes\": [\n", "    {\n", "      \"parameters\": {\n", "        \"cronTime\": \"* 2 * * *\",\n", "        \"timezone\": \"\",\n", "        \"start\": false,\n", "        \"stop\": false,\n", "        \"inExpressions\": false\n", "      },\n", "      \"name\": \"Schedule\",\n", "      \"type\": \"n8n-nodes-base.cron\",\n", "      \"typeVersion\": 1,\n", "      \"position\": [\n", "        200,\n", "        300\n", "      ]\n", "    },\n", "    {\n", "      \"parameters\": {\n", "        \"url\": \"https://YOUR-N8N-INSTANCE/api/workflows\",\n", "        \"method\": \"GET\",\n", "        \"responseFormat\": \"json\",\n", "        \"simpleHttpRequestOptions\": {}\n", "      },\n", "      \"name\": \"Get Workflows\",\n", "      \"type\": \"n8n-nodes-base.httpRequest\",\n", "      \"typeVersion\": 1,\n", "      \"position\": [\n", "        400,\n", "        300\n", "      ]\n", "    },\n", "    {\n", "      \"parameters\": {\n", "        \"batchSize\": 1\n", "      },\n", "      \"name\": \"Loop Workflows\",\n", "      \"type\": \"n8n-nodes-base.splitInBatches\",\n", "      \"typeVersion\": 1,\n", "      \"position\": [\n", "        600,\n", "        300\n", "      ]\n", "    },\n", "    {\n", "      \"parameters\": {\n", "        \"url\": \"https://YOUR-N8N-INSTANCE/api/workflows/{{$json.id}}\",\n", "        \"method\": \"GET\",\n", "        \"responseFormat\": \"json\",\n", "        \"simpleHttpRequestOptions\": {}\n", "      },\n", "      \"name\": \"Get Workflow\",\n", "      \"type\": \"n8n-nodes-base.httpRequest\",\n", "      \"typeVersion\": 1,\n", "      \"position\": [\n", "        800,\n", "        300\n", "      ]\n", "    },\n", "    {\n", "      \"parameters\": {\n", "        \"functionCode\": \"items[0].json.data = items[0].json;\\nitems[0].binary = { data: Buffer.from(JSON.stringify(items[0].json, null, 2)) };\\nreturn items[0];\"\n", "      },\n", "      \"name\": \"<PERSON><PERSON><PERSON>\",\n", "      \"type\": \"n8n-nodes-base.function\",\n", "      \"typeVersion\": 1,\n", "      \"position\": [\n", "        1000,\n", "        300\n", "      ]\n", "    },\n", "    {\n", "      \"parameters\": {\n", "        \"fileName\": \"workflow-backup-{{$json.id}}.json\",\n", "        \"binaryPropertyName\": \"data\",\n", "        \"destination\": \"/tmp\"\n", "      },\n", "      \"name\": \"Save Backup\",\n", "      \"type\": \"n8n-nodes-base.writeBinaryFile\",\n", "      \"typeVersion\": 1,\n", "      \"position\": [\n", "        1200,\n", "        300\n", "      ]\n", "    },\n", "    {\n", "      \"parameters\": {\n", "        \"chatId\": \"{{your_chat_id}}\",\n", "        \"message\": \"Workflow backup completed for workflow ID {{$json.id}}.\"\n", "      },\n", "      \"name\": \"Telegram Notification\",\n", "      \"type\": \"n8n-nodes-base.telegram\",\n", "      \"typeVersion\": 1,\n", "      \"position\": [\n", "        1400,\n", "        200\n", "      ]\n", "    },\n", "    {\n", "      \"parameters\": {\n", "        \"subject\": \"Workflow Backup Completed\",\n", "        \"text\": \"Backup has been created for workflow ID {{$json.id}}.\",\n", "        \"to\": \"{{<EMAIL>}}\",\n", "        \"from\": \"{{<EMAIL>}}\",\n", "        \"attachments\": [\n", "          {\n", "            \"filename\": \"workflow-backup-{{$json.id}}.json\",\n", "            \"content\": \"{{$binary.data.base64}}\",\n", "            \"encoding\": \"base64\"\n", "          }\n", "        ]\n", "      },\n", "      \"name\": \"Send Email\",\n", "      \"type\": \"n8n-nodes-base.gmail\",\n", "      \"typeVersion\": 1,\n", "      \"position\": [\n", "        1400,\n", "        400\n", "      ]\n", "    }\n", "  ],\n", "  \"connections\": {\n", "    \"Schedule\": {\n", "      \"main\": [\n", "        [\n", "          {\n", "            \"node\": \"Get Workflows\",\n", "            \"type\": \"main\",\n", "            \"index\": 0\n", "          }\n", "        ]\n", "      ]\n", "    },\n", "    \"Get Workflows\": {\n", "      \"main\": [\n", "        [\n", "          {\n", "            \"node\": \"Loop Workflows\",\n", "            \"type\": \"main\",\n", "            \"index\": 0\n", "          }\n", "        ]\n", "      ]\n", "    },\n", "    \"Loop Workflows\": {\n", "      \"main\": [\n", "        [\n", "          {\n", "            \"node\": \"Get Workflow\",\n", "            \"type\": \"main\",\n", "            \"index\": 0\n", "          }\n", "        ]\n", "      ]\n", "    },\n", "    \"Get Workflow\": {\n", "      \"main\": [\n", "        [\n", "          {\n", "            \"node\": \"Prepare Backup\",\n", "            \"type\": \"main\",\n", "            \"index\": 0\n", "          }\n", "        ]\n", "      ]\n", "    },\n", "    \"Prepare Backup\": {\n", "      \"main\": [\n", "        [\n", "          {\n", "            \"node\": \"Save Backup\",\n", "            \"type\": \"main\",\n", "            \"index\": 0\n", "          }\n", "        ]\n", "      ]\n", "    },\n", "    \"Save Backup\": {\n", "      \"main\": [\n", "        [\n", "          {\n", "            \"node\": \"Telegram Notification\",\n", "            \"type\": \"main\",\n", "            \"index\": 0\n", "          },\n", "          {\n", "            \"node\": \"Send Email\",\n", "            \"type\": \"main\",\n", "            \"index\": 0\n", "          }\n", "        ]\n", "      ]\n", "    }\n", "  },\n", "  \"active\": false\n", "}\n"]}], "source": ["print(dataset.loc[0, \"generated_json\"])"]}, {"cell_type": "code", "execution_count": null, "id": "9c2c81c8", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.9"}}, "nbformat": 4, "nbformat_minor": 5}